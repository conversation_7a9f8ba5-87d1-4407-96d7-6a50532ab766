#!/usr/bin/env node

/**
 * Debug script to test reasoning engine directly
 */

async function testReasoningEngine() {
  console.log('Testing reasoning engine directly...\n');

  try {
    // Import the reasoning engine
    const { reasoningEngine } = await import('./lib/reasoning-engine.js');
    
    console.log('1. Setting model to qwen2.5:3b...');
    reasoningEngine.setModel('qwen2.5:3b');
    
    console.log('2. Testing simple question...');
    const thinkingProcess = await reasoningEngine.think('Hello, how are you?');
    
    console.log('3. Results:');
    console.log('   - User message:', thinkingProcess.userMessage);
    console.log('   - Task type:', thinkingProcess.taskType);
    console.log('   - Steps count:', thinkingProcess.steps?.length || 0);
    console.log('   - Duration:', thinkingProcess.duration, 'ms');
    
    if (thinkingProcess.steps && thinkingProcess.steps.length > 0) {
      console.log('\n4. Steps details:');
      thinkingProcess.steps.forEach((step, index) => {
        console.log(`   Step ${index + 1}:`);
        console.log(`     - ID: ${step.id}`);
        console.log(`     - Stage: ${step.stage}`);
        console.log(`     - Confidence: ${step.confidence}`);
        console.log(`     - Reasoning: ${step.reasoning.substring(0, 100)}...`);
      });
    } else {
      console.log('\n❌ No steps generated!');
    }
    
    console.log('\n✅ Reasoning engine test completed');
    
  } catch (error) {
    console.error('❌ Reasoning engine test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Test task manager integration
async function testTaskManager() {
  console.log('\n\nTesting task manager integration...\n');

  try {
    const { taskManager } = await import('./lib/task-manager.js');
    
    console.log('1. Processing message through task manager...');
    const task = await taskManager.processUserMessage('What models are available?', 'qwen2.5:3b');
    
    console.log('2. Task results:');
    console.log('   - Task ID:', task.id);
    console.log('   - Task type:', task.type);
    console.log('   - Status:', task.status);
    console.log('   - Has thinking process:', !!task.thinkingProcess);
    
    if (task.thinkingProcess) {
      console.log('   - Thinking steps:', task.thinkingProcess.steps?.length || 0);
      console.log('   - Task type from thinking:', task.thinkingProcess.taskType);
    }
    
    const response = taskManager.getTaskResponse(task);
    console.log('   - Response preview:', response.substring(0, 100) + '...');
    
    console.log('\n✅ Task manager test completed');
    
  } catch (error) {
    console.error('❌ Task manager test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run tests
async function runTests() {
  await testReasoningEngine();
  await testTaskManager();
}

if (require.main === module) {
  runTests();
}

module.exports = { testReasoningEngine, testTaskManager };
