console.log("Node.js version:", process.version)
console.log("Operating System:", process.platform)

try {
  const fs = require("fs")

  // Check if package.json exists
  if (fs.existsSync("./package.json")) {
    const pkg = require("./package.json")
    console.log("Package name:", pkg.name)
    console.log("Next.js version:", pkg.dependencies.next)
    console.log("React version:", pkg.dependencies.react)
  } else {
    console.log("package.json not found")
  }

  // Check if next.config.mjs exists
  if (fs.existsSync("./next.config.mjs")) {
    console.log("next.config.mjs exists")
  } else {
    console.log("next.config.mjs not found")
  }

  // Check if app directory exists
  if (fs.existsSync("./app")) {
    console.log("app directory exists")
    const files = fs.readdirSync("./app")
    console.log("Files in app directory:", files)
  } else {
    console.log("app directory not found")
  }
} catch (error) {
  console.error("Error during diagnostics:", error)
}

