"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Loader2, Send, Bot, Settings, Sparkles, Brain, RefreshCw, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"

// Model provider types
enum ModelProvider {
  Ollama = "ollama",
  HuggingFace = "huggingface"
}

// Model interface
interface Model {
  id: string
  name: string
  type: string
  description: string
}

// Message interface
interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  pending?: boolean
}

/**
 * AI Frontend Template
 *
 * A reusable template for building frontends that communicate with AI models
 * via Ollama or Hugging Face
 */
export default function AIFrontendTemplate() {
  // State for messages
  const [messages, setMessages] = useState<Message[]>([
    {
      id: "welcome",
      role: "assistant",
      content: "Hello! I'm an AI assistant. How can I help you today?",
      timestamp: new Date()
    }
  ])

  // State for input
  const [input, setInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)

  // State for models
  const [models, setModels] = useState<Model[]>([])
  const [selectedModel, setSelectedModel] = useState("")
  const [selectedProvider, setSelectedProvider] = useState<ModelProvider>(ModelProvider.Ollama)
  const [isLoadingModels, setIsLoadingModels] = useState(false)

  // State for settings
  const [temperature, setTemperature] = useState(0.7)
  const [maxTokens, setMaxTokens] = useState(1024)
  const [streamResponse, setStreamResponse] = useState(true)

  // Refs
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  // Toast
  const { toast } = useToast()

  // Fetch models on mount and when provider changes
  useEffect(() => {
    fetchModels()
  }, [selectedProvider])

  // Scroll to bottom when messages change
  useEffect(() => {
    scrollToBottom()
  }, [messages])

  // Fetch available models
  const fetchModels = async () => {
    setIsLoadingModels(true)

    try {
      // In a real implementation, this would call your API
      // For this template, we'll use mock data
      const mockOllamaModels = [
        { id: "llama2:7b", name: "Llama 2 (7B)", type: "ollama", description: "General purpose language model" },
        { id: "codellama:7b", name: "Code Llama (7B)", type: "ollama", description: "Specialized for code generation" },
        { id: "mistral:7b", name: "Mistral (7B)", type: "ollama", description: "High-performance language model" }
      ]

      const mockHuggingFaceModels = [
        { id: "gpt2", name: "GPT-2", type: "huggingface", description: "OpenAI GPT-2 model" },
        { id: "facebook/opt-350m", name: "OPT-350M", type: "huggingface", description: "Meta OPT model" },
        { id: "EleutherAI/gpt-neo-1.3B", name: "GPT-Neo", type: "huggingface", description: "EleutherAI GPT-Neo model" }
      ]

      // Filter models based on selected provider
      const filteredModels = selectedProvider === ModelProvider.Ollama
        ? mockOllamaModels
        : mockHuggingFaceModels

      setModels(filteredModels)

      // Set default selected model
      if (filteredModels.length > 0 && !selectedModel) {
        setSelectedModel(filteredModels[0].id)
      }
    } catch (error) {
      console.error("Error fetching models:", error)
      toast({
        title: "Error",
        description: "Failed to fetch models. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoadingModels(false)
    }
  }

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }

  // Handle key down in input
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  // Generate a unique ID
  const generateId = () => {
    return Math.random().toString(36).substring(2, 15)
  }

  // Send message to AI model
  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return

    // Create user message
    const userMessage: Message = {
      id: generateId(),
      role: "user",
      content: input,
      timestamp: new Date()
    }

    // Add user message to chat
    setMessages(prev => [...prev, userMessage])

    // Clear input
    setInput("")

    // Create pending message
    const pendingId = generateId()
    const pendingMessage: Message = {
      id: pendingId,
      role: "assistant",
      content: "",
      timestamp: new Date(),
      pending: true
    }

    setMessages(prev => [...prev, pendingMessage])
    setIsProcessing(true)

    try {
      // In a real implementation, this would call your API
      // For this template, we'll simulate a response

      // Determine which API to use based on selected provider
      if (selectedProvider === ModelProvider.Ollama) {
        await simulateOllamaResponse(pendingId, userMessage.content)
      } else {
        await simulateHuggingFaceResponse(pendingId, userMessage.content)
      }
    } catch (error) {
      console.error("Error sending message:", error)
      toast({
        title: "Error",
        description: "Failed to get a response. Please try again.",
        variant: "destructive"
      })

      // Remove the pending message
      setMessages(prev => prev.filter(msg => msg.id !== pendingId))
    } finally {
      setIsProcessing(false)
    }
  }

  // Simulate Ollama response
  const simulateOllamaResponse = async (pendingId: string, userPrompt: string) => {
    // Simulate streaming response
    if (streamResponse) {
      const fullResponse = generateMockResponse(userPrompt, selectedModel)
      let currentResponse = ""

      // Break response into chunks
      for (let i = 0; i < fullResponse.length; i += 5) {
        currentResponse += fullResponse.slice(i, i + 5)

        // Update the pending message with the current response
        setMessages(prev =>
          prev.map(msg => (msg.id === pendingId ? { ...msg, content: currentResponse, pending: true } : msg))
        )

        // Simulate delay
        await new Promise(resolve => setTimeout(resolve, 50))
      }

      // Mark message as no longer pending
      setMessages(prev =>
        prev.map(msg => (msg.id === pendingId ? { ...msg, content: fullResponse, pending: false } : msg))
      )
    } else {
      // Simulate non-streaming response
      await new Promise(resolve => setTimeout(resolve, 1000))

      const response = generateMockResponse(userPrompt, selectedModel)

      // Update the pending message with the response
      setMessages(prev =>
        prev.map(msg => (msg.id === pendingId ? { ...msg, content: response, pending: false } : msg))
      )
    }
  }

  // Simulate Hugging Face response
  const simulateHuggingFaceResponse = async (pendingId: string, userPrompt: string) => {
    // Simulate response delay
    await new Promise(resolve => setTimeout(resolve, 1500))

    const response = generateMockResponse(userPrompt, selectedModel)

    // Update the pending message with the response
    setMessages(prev =>
      prev.map(msg => (msg.id === pendingId ? { ...msg, content: response, pending: false } : msg))
    )
  }

  // Generate a mock response based on the prompt
  const generateMockResponse = (prompt: string, modelId: string) => {
    // This is just a simple mock response generator
    // In a real implementation, this would be replaced with actual API calls

    if (prompt.toLowerCase().includes("hello") || prompt.toLowerCase().includes("hi")) {
      return `Hello! I'm using the ${modelId} model. How can I assist you with your frontend development today?`
    }

    if (prompt.toLowerCase().includes("react")) {
      return `React is a popular JavaScript library for building user interfaces. Here's a simple React component example:

\`\`\`jsx
import React, { useState } from 'react';

function Counter() {
  const [count, setCount] = useState(0);

  return (
    <div>
      <h1>Count: {count}</h1>
      <button onClick={() => setCount(count + 1)}>Increment</button>
    </div>
  );
}

export default Counter;
\`\`\`

This component creates a simple counter that increments when the button is clicked. Is there something specific about React that you'd like to know more about?`
    }

    if (prompt.toLowerCase().includes("api") || prompt.toLowerCase().includes("fetch")) {
      return `Here's an example of how to fetch data from an API in React:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

function DataFetcher() {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await fetch('https://api.example.com/data');
        if (!response.ok) {
          throw new Error('Network response was not ok');
        }
        const result = await response.json();
        setData(result);
      } catch (error) {
        setError(error);
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;

  return (
    <div>
      <h1>Data from API</h1>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}

export default DataFetcher;
\`\`\`

This component fetches data from an API when it mounts and displays the result.`
    }

    // Default response
    return `I understand you're asking about "${prompt}". As an AI assistant, I can help you with frontend development questions, especially about building UIs that communicate with AI models like Ollama or Hugging Face.

Would you like me to:
1. Generate code for a specific component?
2. Explain how to integrate with AI models?
3. Provide examples of frontend patterns?

Let me know what you're looking for, and I'll be happy to help!`
  }

  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="border-b p-4 flex justify-between items-center bg-muted/10">
        <div className="flex items-center gap-2">
          <Bot className="h-5 w-5 text-primary" />
          <h1 className="text-lg font-medium">AI Frontend Template</h1>
        </div>

        <div className="flex items-center gap-2">
          <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as ModelProvider)}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select provider" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={ModelProvider.Ollama}>Ollama</SelectItem>
              <SelectItem value={ModelProvider.HuggingFace}>Hugging Face</SelectItem>
            </SelectContent>
          </Select>

          <Button variant="outline" size="icon" onClick={fetchModels} disabled={isLoadingModels}>
            {isLoadingModels ? <Loader2 className="h-4 w-4 animate-spin" /> : <RefreshCw className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      {/* Main content */}
      <div className="flex flex-1 overflow-hidden">
        {/* Chat area */}
        <div className="flex-1 flex flex-col h-full overflow-hidden">
          <Tabs defaultValue="chat" className="flex-1 flex flex-col">
            <div className="border-b px-4 bg-muted/10">
              <TabsList className="h-10">
                <TabsTrigger value="chat" className="flex items-center">
                  <Bot className="h-4 w-4 mr-2" />
                  Chat
                </TabsTrigger>
                <TabsTrigger value="settings" className="flex items-center">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </TabsTrigger>
              </TabsList>
            </div>

            <TabsContent value="chat" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
              <ScrollArea className="flex-1 p-4">
                <div className="space-y-4 max-w-3xl mx-auto">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex items-start gap-3 ${message.role === "user" ? "justify-end" : "justify-start"}`}
                    >
                      {message.role === "assistant" && (
                        <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                          <Bot className="h-5 w-5 text-primary" />
                        </div>
                      )}

                      <Card
                        className={`p-4 max-w-[80%] ${
                          message.role === "user"
                            ? "bg-primary text-primary-foreground"
                            : "bg-muted"
                        } ${message.pending ? "animate-pulse" : ""}`}
                      >
                        <div className="whitespace-pre-wrap">{message.content}</div>
                        <div className="text-xs mt-2 opacity-70">
                          {message.timestamp.toLocaleTimeString()}
                        </div>
                      </Card>

                      {message.role === "user" && (
                        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                          <span className="text-primary-foreground font-medium">You</span>
                        </div>
                      )}
                    </div>
                  ))}
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              <div className="p-4 border-t bg-muted/10">
                <div className="max-w-3xl mx-auto">
                  <div className="flex gap-2">
                    <Select value={selectedModel} onValueChange={setSelectedModel}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Select model" />
                      </SelectTrigger>
                      <SelectContent>
                        {models.map((model) => (
                          <SelectItem key={model.id} value={model.id}>
                            {model.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>

                    <Textarea
                      ref={inputRef}
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      onKeyDown={handleKeyDown}
                      placeholder="Type your message here..."
                      className="flex-1 min-h-[60px] resize-none"
                      disabled={isProcessing || !selectedModel}
                    />

                    <Button
                      onClick={handleSendMessage}
                      disabled={!input.trim() || isProcessing || !selectedModel}
                      className="self-end"
                      size="icon"
                    >
                      {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                    </Button>
                  </div>

                  <div className="text-xs text-muted-foreground mt-2">
                    Press Enter to send, Shift+Enter for new line
                  </div>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="settings" className="flex-1 p-0 m-0 overflow-hidden">
              <ScrollArea className="h-full p-4">
                <div className="space-y-6 max-w-3xl mx-auto">
                  <div>
                    <h2 className="text-lg font-medium mb-4">Model Settings</h2>

                    <div className="space-y-4">
                      <div className="grid gap-2">
                        <Label htmlFor="provider">Provider</Label>
                        <Select value={selectedProvider} onValueChange={(value) => setSelectedProvider(value as ModelProvider)}>
                          <SelectTrigger id="provider">
                            <SelectValue placeholder="Select provider" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value={ModelProvider.Ollama}>Ollama</SelectItem>
                            <SelectItem value={ModelProvider.HuggingFace}>Hugging Face</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="grid gap-2">
                        <Label htmlFor="model">Model</Label>
                        <Select value={selectedModel} onValueChange={setSelectedModel}>
                          <SelectTrigger id="model">
                            <SelectValue placeholder="Select model" />
                          </SelectTrigger>
                          <SelectContent>
                            {models.map((model) => (
                              <SelectItem key={model.id} value={model.id}>
                                {model.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <p className="text-xs text-muted-foreground">
                          {models.find(m => m.id === selectedModel)?.description || "Select a model to see its description"}
                        </p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-lg font-medium mb-4">Generation Settings</h2>

                    <div className="space-y-4">
                      <div className="grid gap-2">
                        <div className="flex justify-between">
                          <Label htmlFor="temperature">Temperature: {temperature.toFixed(1)}</Label>
                        </div>
                        <Slider
                          id="temperature"
                          min={0}
                          max={1}
                          step={0.1}
                          value={[temperature]}
                          onValueChange={(value) => setTemperature(value[0])}
                        />
                        <p className="text-xs text-muted-foreground">
                          Controls randomness: Lower values are more deterministic, higher values are more creative.
                        </p>
                      </div>

                      <div className="grid gap-2">
                        <div className="flex justify-between">
                          <Label htmlFor="max-tokens">Max Tokens: {maxTokens}</Label>
                        </div>
                        <Slider
                          id="max-tokens"
                          min={64}
                          max={4096}
                          step={64}
                          value={[maxTokens]}
                          onValueChange={(value) => setMaxTokens(value[0])}
                        />
                        <p className="text-xs text-muted-foreground">
                          Maximum number of tokens to generate.
                        </p>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Switch
                          id="stream"
                          checked={streamResponse}
                          onCheckedChange={setStreamResponse}
                        />
                        <Label htmlFor="stream">Stream response</Label>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-lg font-medium mb-4">Connection Settings</h2>

                    <div className="space-y-4">
                      {selectedProvider === ModelProvider.Ollama && (
                        <div className="grid gap-2">
                          <Label htmlFor="ollama-url">Ollama API URL</Label>
                          <Input id="ollama-url" defaultValue="http://127.0.0.1:11434" />
                          <p className="text-xs text-muted-foreground">
                            URL of your Ollama instance.
                          </p>
                        </div>
                      )}

                      {selectedProvider === ModelProvider.HuggingFace && (
                        <div className="grid gap-2">
                          <Label htmlFor="hf-token">Hugging Face API Token</Label>
                          <Input id="hf-token" type="password" placeholder="Enter your API token" />
                          <p className="text-xs text-muted-foreground">
                            Your Hugging Face API token. Leave empty for public models with rate limits.
                          </p>
                        </div>
                      )}

                      <div className="flex justify-end">
                        <Button variant="outline" className="flex items-center gap-2">
                          <AlertCircle className="h-4 w-4" />
                          Test Connection
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
