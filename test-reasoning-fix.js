#!/usr/bin/env node

/**
 * Test to verify reasoning engine generates unique IDs and prevents key duplication
 */

const { reasoningEngine } = require('./lib/reasoning-engine');

async function testReasoningUniqueness() {
  console.log('Testing reasoning engine unique ID generation...\n');

  try {
    // Test 1: Generate a thinking process
    console.log('1. Testing thinking process generation...');
    const thinkingProcess = await reasoningEngine.think('What models are available?');
    
    console.log('Generated thinking process with', thinkingProcess.steps.length, 'steps');
    
    // Test 2: Check for unique IDs
    console.log('\n2. Checking step IDs for uniqueness...');
    const stepIds = thinkingProcess.steps.map(step => step.id);
    const uniqueIds = new Set(stepIds);
    
    if (stepIds.length === uniqueIds.size) {
      console.log('✅ All step IDs are unique');
      stepIds.forEach((id, index) => {
        console.log(`   Step ${index + 1}: ${id}`);
      });
    } else {
      console.log('❌ Duplicate IDs found!');
      console.log('All IDs:', stepIds);
      console.log('Unique IDs:', Array.from(uniqueIds));
    }
    
    // Test 3: Check step structure
    console.log('\n3. Checking step structure...');
    thinkingProcess.steps.forEach((step, index) => {
      const hasRequiredFields = step.id && step.stage && step.reasoning && step.confidence !== undefined;
      console.log(`   Step ${index + 1}: ${hasRequiredFields ? '✅' : '❌'} Has all required fields`);
      if (!hasRequiredFields) {
        console.log('     Missing fields:', {
          id: !step.id,
          stage: !step.stage,
          reasoning: !step.reasoning,
          confidence: step.confidence === undefined
        });
      }
    });
    
    console.log('\n✅ Reasoning engine test completed successfully!');
    
  } catch (error) {
    console.error('❌ Reasoning engine test failed:', error.message);
    console.error('Stack trace:', error.stack);
  }
}

// Run the test
if (require.main === module) {
  testReasoningUniqueness();
}

module.exports = { testReasoningUniqueness };
