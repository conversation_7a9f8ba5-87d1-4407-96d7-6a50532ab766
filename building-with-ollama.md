# Ollama UI Builder: Technical Documentation

## Table of Contents
1. [Introduction to Ollama](#introduction-to-ollama)
2. [Project Overview](#project-overview)
3. [API Integration](#api-integration)
4. [Core Components](#core-components)
5. [Key Features](#key-features)
6. [Advanced Agent Capabilities](#advanced-agent-capabilities)
7. [Project Structure](#project-structure)
8. [Development Guide](#development-guide)
9. [Implementation Details](#implementation-details)

## Introduction to Ollama

Ollama is an open-source tool that enables running large language models (LLMs) locally on your machine. It provides a simple API for starting, managing, and interacting with these models, allowing developers to build applications that leverage LLM capabilities without relying on cloud-based services.

### Architecture and Technical Design

Ollama employs a client-server architecture where:
- The **server component** handles model loading, management, and inference
- The **client component** provides interfaces for user interaction
- Communication between client and server uses HTTP-based APIs

At its core, Ollama consists of two main components:
1. **ollama-http-server**: Manages API requests, model loading, and client interactions
2. **llama.cpp**: Serves as the inference engine that executes the language models

This design allows Ollama to run efficiently on consumer hardware by optimizing memory usage and leveraging hardware acceleration when available. Models are stored locally in a structured format in the `$HOME/.ollama` directory, which includes:
- Blob files containing the model weights
- Manifest files with metadata following OCI (Open Container Initiative) specifications
- Configuration files that define model parameters and behaviors

### Key Capabilities

Ollama offers a comprehensive set of features for working with large language models:

- **Model Management**: Pull, create, copy, and delete models with simple commands
- **Model Customization**: Use Modelfiles (similar to Dockerfiles) to create custom models with specific parameters, system prompts, and behaviors
- **Streaming Inference**: Generate text with real-time streaming responses
- **Context Management**: Maintain conversation history and context for continued interactions
- **Format Control**: Specify output formats for structured responses
- **Cross-Platform Support**: Works on macOS, Windows, Linux, and various ARM devices

### Advanced Features

- **Modelfiles**: Create custom models with personalized behaviors using a declarative syntax
- **Parameter Tuning**: Adjust model parameters like temperature, top_p, and context window
- **Knowledge Extension**: Add custom knowledge to models through the `FROM` directive
- **Layer System**: Build models in layers, similar to Docker's layer architecture, allowing for efficient customization and extension

### Use Cases

Ollama enables a wide range of practical applications:

- **Local Development**: Build and test LLM-powered applications without cloud dependencies
- **Privacy-Focused Applications**: Process sensitive data without sending it to external services
- **Offline Capabilities**: Create AI applications that function without internet connectivity
- **Edge Computing**: Deploy LLM capabilities on edge devices with limited connectivity
- **Educational Tools**: Learn about and experiment with LLMs in a controlled environment
- **Customized Assistants**: Build domain-specific assistants with specialized knowledge
- **Code Generation**: Create programming assistants with domain-specific expertise
- **Research and Experimentation**: Test model behaviors and experiment with parameters

### Benefits of Using Ollama

- **Local execution**: Models run directly on your hardware, reducing latency
- **Privacy**: Data never leaves your machine, maintaining confidentiality
- **No API costs**: Free to use once models are downloaded
- **Customizable**: Support for various models like Llama, Mistral, and others
- **Simple API**: Easy to integrate with applications
- **Resource Efficiency**: Optimized for running on consumer hardware
- **Extensibility**: Build on and customize models for specific use cases

Ollama represents a significant advancement in making LLM technology accessible to developers and end-users, enabling a new generation of AI-powered applications that prioritize privacy, customization, and efficiency.

## Project Overview

Ollama UI Builder is a Next.js-based web application that provides a user-friendly interface for interacting with Ollama models. The application allows users to:

- Chat with Ollama models
- Generate code based on user requests
- Preview generated code in real-time
- Edit code directly in the browser
- Save and manage chat history
- Create files from generated code
- Utilize advanced reasoning capabilities
- Interact with specialized AI agents for different tasks

### Application Architecture

The Ollama UI Builder follows a modern web application architecture with the following key components:

1. **Frontend Layer**
   - Built with Next.js and React for component-based UI development
   - Client-side state management for real-time interactions
   - Server components for improved performance and SEO
   - API routes for backend functionality

2. **API Integration Layer**
   - Custom Ollama API client for model communication
   - Streaming response handlers for real-time updates
   - Type-safe API interfaces with TypeScript
   - Connection management with auto-detection capabilities
   - Advanced diagnostic services for troubleshooting

3. **Data Management Layer**
   - Local storage for persistent chat history
   - In-memory state for active sessions
   - Code parsing and formatting utilities
   - Context management for maintaining conversation state
   - Task management system for agent workflows

4. **UI Components Layer**
   - Responsive design with Tailwind CSS
   - Accessible components with shadcn/ui
   - Monaco editor integration for code editing
   - Custom rendering for code blocks and chat messages

### Core Features and Implementation

#### Enhanced Chat Interface
The application provides a sophisticated chat interface with:
- Real-time streaming of model responses
- Support for code block detection and formatting
- Syntax highlighting for multiple programming languages
- Message history navigation and context preservation
- System prompts for guiding model behavior

#### Code Generation and Editing
The code generation features include:
- Intelligent parsing of code blocks from model responses
- Monaco Editor integration for advanced code editing
- Multiple language support with proper syntax highlighting
- Real-time code preview with error detection
- Code snippet extraction and file creation capabilities

#### Ollama Model Management
The application interfaces with Ollama models through:
- Model selection dropdown with available model detection
- Model parameter adjustments (temperature, top_p, etc.)
- Custom system prompts for specialized use cases
- Request formatting for optimal code generation
- Streaming response processing for immediate feedback
- Advanced reasoning engine for complex tasks
- Specialized agent services for different domains

#### User Experience Enhancements
The UI includes modern features like:
- Dark and light theme support
- Responsive design for various screen sizes
- Keyboard shortcuts for common actions
- Accessibility considerations with ARIA attributes
- Loading indicators for network operations

### Technology Stack

#### Frontend Technologies
- **Next.js 14+**: React framework for server and client components
- **React 18+**: UI component library with hooks for state management
- **TypeScript 5+**: Type-safe JavaScript superset
- **TailwindCSS 3+**: Utility-first CSS framework
- **shadcn/ui**: Reusable UI component collection
- **Monaco Editor**: VS Code's editor component for code editing
- **React Markdown**: Markdown rendering for chat messages
- **React Syntax Highlighter**: Code syntax highlighting

#### Backend and API
- **Next.js API Routes**: Serverless functions for backend logic
- **Fetch API**: Modern HTTP client for API requests
- **Web Streams API**: For handling streaming responses
- **Zod**: Runtime type validation for API data

#### Development Tools
- **ESLint**: Code quality and style checking
- **Prettier**: Code formatting
- **Jest**: Testing framework
- **React Testing Library**: Component testing utilities
- **Husky**: Git hooks for pre-commit validation
- **Vercel**: Deployment and hosting platform

### Deployment Strategy

The Ollama UI Builder is designed to be deployed in various environments:

1. **Local Development**:
   - Run alongside a local Ollama instance
   - Auto-connects to local Ollama server

2. **Self-hosted Production**:
   - Deploy as a Next.js application with custom server
   - Configure to connect to Ollama running on same network

3. **Cloud Deployment**:
   - Deploy frontend to Vercel or similar platforms
   - Connect to self-hosted Ollama instance via API configuration

The flexible architecture allows for deployment scenarios that maintain data privacy by keeping the LLM processing local while providing a sophisticated UI for interaction.

## API Integration

### Connecting to Ollama

The application connects to the Ollama API using the client code in `lib/ollama-api.ts`. This module handles all communication with the Ollama server.

```typescript
// lib/ollama-api.ts (simplified)
interface OllamaAPI {
  getModels(): Promise<string[]>;
  streamCompletion(
    options: GenerateRequestOptions,
    onChunk: (chunk: any) => void,
    onError: (error: any) => void,
    onComplete: () => void
  ): Promise<void>;
  chat(options: ChatRequestOptions): Promise<ChatResponse>;
  streamChat(
    options: ChatRequestOptions,
    onChunk: (chunk: any) => void,
    onError: (error: any) => void,
    onComplete: () => void
  ): Promise<void>;
  createEmbedding(options: EmbeddingRequestOptions): Promise<EmbeddingResponse>;
}

export function createOllamaAPI(connectionMode: "local" | "auto" | "custom" = "auto", apiUrl?: string, apiKey?: string): OllamaAPI {
  // Determine the base URL based on connection mode
  let baseUrl = "";
  if (connectionMode === "local") {
    baseUrl = "http://localhost:11434";
  } else if (connectionMode === "auto") {
    // In auto mode, use the current hostname with the standard Ollama port
    const host = typeof window !== 'undefined' ? window.location.hostname : 'localhost';
    baseUrl = `http://${host}:11434`;
  } else if (connectionMode === "custom" && apiUrl) {
    baseUrl = apiUrl;
  } else {
    baseUrl = "http://localhost:11434"; // Default fallback
  }

  // Implementation details...
}
```

### Comprehensive API Endpoints Reference

Ollama provides a rich set of RESTful API endpoints to interact with local large language models. Below is a comprehensive reference of all available endpoints:

#### 1. Model Management Endpoints

##### 1.1 List Available Models
```
GET /api/tags
```
**Description**: Retrieves a list of all models that have been pulled and are available locally on the Ollama server.

**Parameters**: None

**Response Format**:
```json
{
  "models": [
    {
      "name": "llama3:latest",
      "modified_at": "2023-12-07T09:32:18.757212583-08:00",
      "size": 3825819519,
      "digest": "fe938a131f40e6f6d40083c9f0f430a515233eb2edaa6d72eb85c50d64f2300e",
      "details": {
        "format": "gguf",
        "family": "llama",
        "families": null,
        "parameter_size": "7B",
        "quantization_level": "Q4_0"
      }
    },
    // Additional models...
  ]
}
```

**Example Usage**:
```typescript
// Get list of available models
async function fetchModels() {
  try {
    const response = await fetch('http://localhost:11434/api/tags');
    const data = await response.json();
    const modelNames = data.models.map(model => model.name);
    return modelNames;
  } catch (error) {
    console.error('Error fetching models:', error);
    return [];
  }
}
```

##### 1.2 Pull a Model
```
POST /api/pull
```
**Description**: Downloads a model from the Ollama library.

**Parameters**:
- `name` (required): Name of the model to pull (e.g., "llama3:latest")
- `insecure` (optional): Whether to allow insecure connections for downloading

**Response Format**: A stream of JSON objects with progress information.

**Example Usage**:
```javascript
fetch('http://localhost:11434/api/pull', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: 'llama3.2'
  })
}).then(response => {
  const reader = response.body.getReader();
  // Process the stream of JSON progress updates
});
```

##### 1.3 Copy a Model
```
POST /api/copy
```
**Description**: Creates a copy of an existing model with a new name.

**Parameters**:
- `source` (required): Name of the source model
- `destination` (required): Name for the new model copy

**Example Usage**:
```bash
curl http://localhost:11434/api/copy -d '{
  "source": "llama3:latest",
  "destination": "my-llama3"
}'
```

##### 1.4 Delete a Model
```
DELETE /api/delete
```
**Description**: Deletes a model from the local storage.

**Parameters**:
- `name` (required): Name of the model to delete

**Example Usage**:
```typescript
// Delete a model
async function deleteModel(modelName) {
  try {
    const response = await fetch('http://localhost:11434/api/delete', {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        name: modelName
      })
    });
    return await response.json();
  } catch (error) {
    console.error(`Error deleting model ${modelName}:`, error);
    throw error;
  }
}
```

#### 2. Text Generation Endpoints

##### 2.1 Generate Completions
```
POST /api/generate
```
**Description**: Generates text based on a prompt using a specified model. This is the primary endpoint for simple text generation.

**Parameters**:
- `model` (required): The name of the model to use
- `prompt` (required): The prompt to generate text from
- `stream` (optional): Whether to stream the response (default: true)
- `options` (optional): Model-specific parameters like temperature, top_p, etc.
- `keep_alive` (optional): Duration to keep model loaded in memory after request (default: "5m")
- `format` (optional): Response format specification for structured outputs
- `context` (optional): Previous context for continued generation

**Response Format** (non-streaming):
```json
{
  "model": "llama3.2",
  "created_at": "2023-12-12T14:13:43.416799Z",
  "response": "The sky appears blue because light from the sun is scattered by air molecules. Blue light has a shorter wavelength and is scattered more than other colors.",
  "done": true,
  "done_reason": "stop",
  "context": [3, 234, 24, ...],
  "total_duration": 5191566416,
  "load_duration": 2154458,
  "prompt_eval_count": 26,
  "prompt_eval_duration": *********,
  "eval_count": 298,
  "eval_duration": 4799921000
}
```

**Example Usage**:
```typescript
const options: GenerateRequestOptions = {
  model: "llama3.2",
  prompt: "Generate a React component that displays a counter with increment and decrement buttons.",
  stream: true,
  options: {
    temperature: 0.7,
    top_p: 0.9,
    top_k: 40,
    seed: 42,  // For reproducible outputs
    num_predict: 1000  // Maximum number of tokens to generate
  }
};

await ollamaApi.streamCompletion(
  options,
  (chunk) => {
    // Handle each chunk of the response
    fullResponse += chunk.response;
    updateUI(fullResponse); // Update UI with the current response
  },
  (error) => {
    // Handle errors
    console.error("Generation error:", error);
    showErrorNotification(error);
  },
  () => {
    // Handle completion
    console.log("Generation complete");
    setLoading(false);
  }
);
```

##### 2.2 Chat Completions
```
POST /api/chat
```
**Description**: Generates a response in a chat conversation format. Optimized for multi-turn dialogue with role-based messaging.

**Parameters**:
- `model` (required): The name of the model to use
- `messages` (required): Array of message objects with `role` and `content`
- `stream` (optional): Whether to stream the response (default: true)
- `options` (optional): Model-specific parameters
- `keep_alive` (optional): Duration to keep model loaded (default: "5m")
- `format` (optional): Response format specification for structured outputs
- `images` (optional): Array of base64-encoded images for multimodal models

**Response Format** (non-streaming):
```json
{
  "model": "llama3.2",
  "created_at": "2023-12-12T14:13:43.416799Z",
  "message": {
    "role": "assistant",
    "content": "The sky appears blue because sunlight is scattered by air molecules. The shorter blue wavelengths are scattered more strongly, giving the sky its blue color."
  },
  "done": true,
  "done_reason": "stop",
  "total_duration": 5191566416,
  "load_duration": 2154458,
  "prompt_eval_count": 26,
  "prompt_eval_duration": *********,
  "eval_count": 298,
  "eval_duration": 4799921000
}
```

**Example Usage**:
```typescript
const chatOptions = {
  model: "llama3.2",
  messages: [
    { role: "system", content: "You are a helpful coding assistant." },
    { role: "user", content: "How do I create a React component?" },
    { role: "assistant", content: "React components can be created using function or class syntax..." },
    { role: "user", content: "Can you show me an example with hooks?" }
  ],
  stream: true,
  options: {
    temperature: 0.5
  }
};

await ollamaApi.streamChat(
  chatOptions,
  (chunk) => {
    // Process each chunk of the assistant's response
    if (chunk.message?.content) {
      updateChatUI(chunk.message.content);
    }
  },
  (error) => handleError(error),
  () => finishChatResponse()
);
```

##### 2.3 Chat with Images (Multimodal)
```
POST /api/chat
```
**Description**: Allows chat interactions with image inputs for multimodal models like LLaVA.

**Parameters**: Same as the chat endpoint, with the addition of:
- `images`: Array of base64-encoded images

**Example Usage**:
```javascript
// First convert your image to base64
function imageToBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      // Extract the base64 part only
      const base64String = reader.result.toString().split(',')[1];
      resolve(base64String);
    };
    reader.onerror = error => reject(error);
  });
}

// Then use it in a chat request
async function chatWithImage(imageFile, question) {
  const base64Image = await imageToBase64(imageFile);

  const response = await fetch('http://localhost:11434/api/chat', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      model: 'llava',
      messages: [
        {
          role: 'user',
          content: question,
          images: [base64Image]
        }
      ]
    })
  });

  return await response.json();
}
```

#### 3. Embedding Endpoints

##### 3.1 Generate Embeddings (Current)
```
POST /api/embed
```
**Description**: Generates vector embeddings from text using an embedding model.

**Parameters**:
- `model` (required): Name of embedding model (e.g., "mxbai-embed-large")
- `input` (required): Text to generate embeddings for
- `options` (optional): Additional model parameters
- `keep_alive` (optional): Duration to keep model loaded (default: "5m")

**Response Format**:
```json
{
  "embedding": [0.5670403838157654, 0.009260174818336964, 0.23178744316101074, -0.2916173040866852, ... ]
}
```

**Example Usage**:
```typescript
async function createEmbedding(text: string) {
  try {
    const response = await fetch('http://localhost:11434/api/embed', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: 'all-minilm',
        input: text
      })
    });

    const data = await response.json();
    return data.embedding;
  } catch (error) {
    console.error('Error generating embedding:', error);
    throw error;
  }
}
```

##### 3.2 Generate Embeddings (Legacy)
```
POST /api/embeddings
```
**Description**: Legacy endpoint for generating embeddings. Superseded by `/api/embed`.

**Parameters**:
- `model` (required): Name of model
- `prompt` (required): Text to generate embeddings for
- `options` (optional): Additional model parameters
- `keep_alive` (optional): Duration to keep model loaded (default: "5m")

**Response Format**: Same as `/api/embed`

#### 4. System Information Endpoints

##### 4.1 Version Information
```
GET /api/version
```
**Description**: Retrieves the version of the running Ollama server.

**Response Format**:
```json
{
  "version": "0.5.1"
}
```

**Example Usage**:
```typescript
async function checkOllamaVersion() {
  try {
    const response = await fetch('http://localhost:11434/api/version');
    const data = await response.json();
    return data.version;
  } catch (error) {
    console.error('Error checking Ollama version:', error);
    return null;
  }
}
```

##### 4.2 Blob Management
```
HEAD /api/blobs/:digest
POST /api/blobs/:digest
```
**Description**: Check for existence of and upload binary large objects (blobs) for model creation.

#### 5. Model Creation and Customization

##### 5.1 Create Model
```
POST /api/create
```
**Description**: Creates a custom model from a Modelfile or based on existing model files.

**Parameters**:
- `name` (required): Name for the new model
- `modelfile` or `files`: Either a Modelfile content or a map of files to include

**Example Usage** (with Modelfile):
```typescript
async function createCustomModel(modelName: string, baseModel: string, systemPrompt: string) {
  // Create a Modelfile
  const modelfile = `
FROM ${baseModel}
SYSTEM ${systemPrompt}
PARAMETER temperature 0.7
  `;

  try {
    const response = await fetch('http://localhost:11434/api/create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        name: modelName,
        modelfile: modelfile
      })
    });

    // Process streaming response
    const reader = response.body.getReader();
    const decoder = new TextDecoder();

    while (true) {
      const { done, value } = await reader.read();
      if (done) break;

      const chunk = decoder.decode(value);
      console.log(chunk); // Log progress
    }

    return true;
  } catch (error) {
    console.error('Error creating model:', error);
    return false;
  }
}
```

### How Ollama UI Builder Uses These Endpoints

The Ollama UI Builder primarily interacts with these endpoints:

1. **List Models** (`GET /api/tags`): Used on application startup to retrieve available models and populate the model selector dropdown.

2. **Generate Completions** (`POST /api/generate`): Used for single-turn interactions and code generation tasks.

3. **Chat Completions** (`POST /api/chat`): Used for more complex, multi-turn conversations in the chat interface.

Example implementation of model listing:

```typescript
// In the main component
useEffect(() => {
  const initializeAPI = async () => {
    try {
      setIsLoading(true);
      setConnectionError(false);
      const ollamaApi = createOllamaAPI(settings.connectionMode, settings.apiUrl, settings.apiKey);
      const response = await ollamaApi.listModels();

      if (response && response.models) {
        const modelNames = response.models.map((model: any) => model.name);
        setModels(modelNames);

        if (modelNames.length > 0) {
          setSelectedModel(modelNames[0]);
        }
      }
    } catch (error) {
      console.error("Error initializing API:", error);
      setConnectionError(true);
      toast({
        title: "Connection Error",
        description: `Failed to connect to Ollama API. Please check your connection settings.`,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (mounted) {
    initializeAPI();
  }
}, [settings.connectionMode, settings.apiUrl, settings.apiKey, toast, mounted]);
```

Example of generating code with the generate endpoint:

```typescript
const generateCode = async (prompt) => {
  setGenerating(true);
  let fullResponse = "";

  try {
    await ollamaApi.streamCompletion(
      {
        model: selectedModel,
        prompt: `Generate the following code: ${prompt}`,
        stream: true,
        options: {
          temperature: 0.7,
        }
      },
      (chunk) => {
        fullResponse += chunk.response;
        setPartialResponse(fullResponse);
      },
      (error) => {
        console.error("Error:", error);
        toast({
          title: "Generation Error",
          description: String(error),
          variant: "destructive",
        });
      },
      () => {
        // Extract code blocks from the full response
        extractCodeBlocks(fullResponse);
        setGenerating(false);
      }
    );
  } catch (error) {
    console.error("Error in code generation:", error);
    setGenerating(false);
  }
};
```

## Core Components

This section details the core components of the Ollama UI Builder, explaining their purpose, implementation details, and how they interact with each other to deliver a cohesive user experience.

### Enhanced Chat Interface

The main component that handles the chat interaction with Ollama models is `EnhancedChatInterface`. This component serves as the central orchestrator for user interactions with the LLM.

#### Purpose
- Provides the primary user interface for communicating with Ollama models
- Manages the conversation flow between user and AI assistant
- Handles special commands and code generation requests
- Coordinates between different UI modes (chat and editor)
- Maintains conversation context for coherent interactions

#### Implementation Details
```typescript
// components/enhanced-chat-interface.tsx
export default function EnhancedChatInterface({
  selectedModel,
  onCreateFile,
  temperature,
}: EnhancedChatInterfaceProps) {
  // Core state management
  const [messages, setMessages] = useState<Message[]>([/* initial message */]);
  const [viewMode, setViewMode] = useState<"chat" | "editor">("chat");
  const [isProcessing, setIsProcessing] = useState(false);
  const [extractedCode, setExtractedCode] = useState<ExtractedCode>({
    html: "",
    css: "",
    js: "",
    combined: "",
    otherFiles: []
  });

  // Chat history persistence
  const storeChatHistory = useCallback((msgs: Message[]) => {
    const chatId = localStorage.getItem("currentChatId") || uuidv4();
    localStorage.setItem("currentChatId", chatId);
    localStorage.setItem(`chat-${chatId}`, JSON.stringify(msgs));
    localStorage.setItem("chatList", JSON.stringify([
      ...(JSON.parse(localStorage.getItem("chatList") || "[]")),
      { id: chatId, title: `Chat ${msgs.length}` }
    ]));
  }, []);

  // Message handling
  const handleSendMessage = async (content: string) => {
    // Add user message to state
    const userMessage = { role: "user", content };
    const updatedMessages = [...messages, userMessage];
    setMessages(updatedMessages);
    setIsProcessing(true);

    try {
      let fullResponse = "";

      // Stream response from Ollama
      await ollamaApi.streamChat({
        model: selectedModel,
        messages: updatedMessages,
        options: { temperature }
      },
      // Process each chunk as it arrives
      (chunk) => {
        if (chunk.message?.content) {
          fullResponse += chunk.message.content;
          // Update UI in real-time with each chunk
          setMessages(prev => [
            ...prev.slice(0, -1),
            { role: "assistant", content: fullResponse }
          ]);
        }
      },
      (error) => console.error("Error:", error),
      () => {
        // On completion
        setIsProcessing(false);
        saveToHistory(updatedMessages, fullResponse);

        // Check for code generation and extract code if needed
        if (isCodeGenerationRequest(content)) {
          const extracted = extractCodeFromText(fullResponse);
          if (hasValidCode(extracted)) {
            setExtractedCode(extracted);
            setViewMode("editor");
          }
        }
      });
    } catch (error) {
      console.error("Failed to send message:", error);
      setIsProcessing(false);
    }
  };

  // Code extraction logic, view mode controls, etc.
  // ...

  return (
    <div className="flex flex-col h-full">
      {viewMode === "chat" ? (
        <ChatView
          messages={messages}
          onSendMessage={handleSendMessage}
          isProcessing={isProcessing}
        />
      ) : (
        <EditorView
          extractedCode={extractedCode}
          onSwitchToChat={() => setViewMode("chat")}
          onCreateFile={onCreateFile}
        />
      )}
    </div>
  );
}
```

#### Interactions
The `EnhancedChatInterface` component:
- Receives props for `selectedModel` and `temperature` from parent components
- Uses the Ollama API client to communicate with the model
- Calls `extractCodeFromText` utility to parse code from responses
- Triggers the `onCreateFile` callback when users want to save generated code
- Manages localStorage for chat persistence
- Renders either the `ChatView` or `EditorView` based on current mode

### Code Preview

The `CodePreview` component provides a live rendering environment for web code (HTML, CSS, JavaScript) generated by the LLM.

#### Purpose
- Renders previews of generated code in real-time
- Supports different view modes for better user experience
- Provides immediate visual feedback for code changes
- Enables testing of generated code functionality
- Supports fullscreen mode for focused development

#### Implementation Details
```typescript
// components/code-preview.tsx
export default function CodePreview({
  html,
  css,
  js,
  isFullscreen,
  onToggleFullscreen
}: CodePreviewProps) {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const [activeTab, setActiveTab] = useState<'preview' | 'html' | 'css' | 'js'>('preview');

  // Combine code for the iframe document
  const combinedCode = useMemo(() => {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <style>${css}</style>
        </head>
        <body>
          ${html}
          <script>${js}</script>
        </body>
      </html>
    `;
  }, [html, css, js]);

  // Update iframe content when code changes
  useEffect(() => {
    if (iframeRef.current) {
      const iframe = iframeRef.current;
      const document = iframe.contentDocument;
      if (document) {
        document.open();
        document.write(combinedCode);
        document.close();
      }
    }
  }, [combinedCode]);

  // Handle security and sandbox settings
  const sandboxAttributes = "allow-scripts allow-modals allow-forms allow-pointer-lock allow-popups allow-same-origin";

  return (
    <div className={`code-preview ${isFullscreen ? 'fullscreen' : ''}`}>
      <div className="preview-header">
        <div className="tabs">
          <button
            className={activeTab === 'preview' ? 'active' : ''}
            onClick={() => setActiveTab('preview')}
          >
            Preview
          </button>
          <button
            className={activeTab === 'html' ? 'active' : ''}
            onClick={() => setActiveTab('html')}
          >
            HTML
          </button>
          {/* CSS and JS tab buttons */}
        </div>
        <button onClick={onToggleFullscreen}>
          {isFullscreen ? 'Exit Fullscreen' : 'Fullscreen'}
        </button>
      </div>

      <div className="preview-content">
        {activeTab === 'preview' ? (
          <iframe
            ref={iframeRef}
            sandbox={sandboxAttributes}
            title="Code Preview"
            className="preview-iframe"
          />
        ) : (
          <pre className="code-display">
            {activeTab === 'html' && html}
            {activeTab === 'css' && css}
            {activeTab === 'js' && js}
          </pre>
        )}
      </div>
    </div>
  );
}
```

#### Interactions
The `CodePreview` component:
- Receives HTML, CSS, and JS code as props from parent components
- Communicates with parent components through the `onToggleFullscreen` callback
- Manages an internal iframe for rendering the combined code
- Uses tabs to allow users to switch between preview and code views
- Updates its rendered content reactively when code props change

### Editable Code Block

The `EditableCodeBlock` component provides a sophisticated code editing environment powered by Monaco Editor, the same editor used in VS Code.

#### Purpose
- Enables editing of code generated by the LLM
- Provides syntax highlighting and language-specific features
- Supports code formatting and error checking
- Allows users to modify and refine AI-generated code
- Offers a familiar editing experience for developers

#### Implementation Details
```typescript
// components/editable-code-block.tsx
import { useEffect, useRef, useState } from 'react';
import Editor, { Monaco } from '@monaco-editor/react';
import { formatCode } from '@/lib/code-formatter';

export default function EditableCodeBlock({
  language,
  code,
  onCodeChange,
  onSave,
  onRun,
  readOnly = false,
  theme = 'vs-dark',
}: EditableCodeBlockProps) {
  const [editorValue, setEditorValue] = useState(code);
  const editorRef = useRef<any>(null);
  const monacoRef = useRef<Monaco | null>(null);

  // Format code on mount and when language changes
  useEffect(() => {
    const formatInitialCode = async () => {
      try {
        const formatted = await formatCode(code, language);
        setEditorValue(formatted);
      } catch (error) {
        console.error("Error formatting code:", error);
        setEditorValue(code);
      }
    };

    formatInitialCode();
  }, [code, language]);

  // Handle editor instance and Monaco setup
  const handleEditorDidMount = (editor: any, monaco: Monaco) => {
    editorRef.current = editor;
    monacoRef.current = monaco;

    // Set up editor options
    editor.updateOptions({
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      fontFamily: '"Fira Code", Menlo, Monaco, "Courier New", monospace',
      fontLigatures: true,
      fontSize: 14,
      tabSize: 2,
      wordWrap: 'on',
      // More editor options...
    });

    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      if (onSave) onSave(editorValue);
    });

    // Focus the editor
    editor.focus();
  };

  // Handle code changes
  const handleChange = (value: string | undefined) => {
    const newValue = value || '';
    setEditorValue(newValue);
    if (onCodeChange) onCodeChange(newValue);
  };

  // Handle formatting button click
  const handleFormat = async () => {
    try {
      const formatted = await formatCode(editorValue, language);
      setEditorValue(formatted);
      if (onCodeChange) onCodeChange(formatted);
    } catch (error) {
      console.error("Error formatting code:", error);
    }
  };

  return (
    <div className="editable-code-block">
      <div className="editor-toolbar">
        <div className="language-indicator">{language}</div>
        <div className="toolbar-actions">
          {!readOnly && (
            <>
              <button onClick={handleFormat} className="format-btn">
                Format
              </button>
              {onSave && (
                <button onClick={() => onSave(editorValue)} className="save-btn">
                  Save
                </button>
              )}
              {onRun && (
                <button onClick={() => onRun(editorValue)} className="run-btn">
                  Run
                </button>
              )}
            </>
          )}
        </div>
      </div>

      <div className="editor-container">
        <Editor
          height="100%"
          language={language}
          value={editorValue}
          onChange={handleChange}
          onMount={handleEditorDidMount}
          theme={theme}
          options={{
            readOnly,
            scrollBeyondLastLine: false
          }}
        />
      </div>
    </div>
  );
}
```

#### Interactions
The `EditableCodeBlock` component:
- Receives code content and language as props from parent components
- Uses the `formatCode` utility from `lib/code-formatter.ts`
- Communicates code changes back to parent components via callbacks
- Supports save and run actions through provided callback props
- Adapts its behavior based on the readOnly prop

### Code Generation Tracker

The `CodeGenerationTracker` component visualizes the progress of code generation, providing users with feedback on the LLM's generation process.

#### Purpose
- Visualizes the steps in the code generation process
- Provides feedback on current generation status
- Helps users understand what the system is doing
- Improves perceived performance during waiting periods
- Indicates completion of generation phases

#### Implementation Details
```typescript
// components/code-generation-tracker.tsx
export default function CodeGenerationTracker({
  steps,
  currentStep
}: CodeGenerationTrackerProps) {
  return (
    <div className="code-generation-tracker">
      <h3 className="tracker-title">Generating Code</h3>
      <div className="steps">
        {steps.map((step, index) => (
          <div
            key={index}
            className={`step ${index < currentStep ? 'completed' : ''} ${index === currentStep ? 'active' : ''}`}
          >
            <div className="step-indicator">
              {index < currentStep ? (
                <CheckIcon className="check-icon" />
              ) : (
                <div className="step-number">{index + 1}</div>
              )}
            </div>
            <div className="step-label">{step}</div>
            {index < steps.length - 1 && <div className="step-connector" />}
          </div>
        ))}
      </div>
    </div>
  );
}

// Example usage
const generationSteps = [
  "Analyzing request",
  "Planning components",
  "Generating HTML structure",
  "Adding CSS styles",
  "Implementing JavaScript functionality",
  "Finalizing code"
];
```

#### Interactions
The `CodeGenerationTracker` component:
- Receives an array of steps and the current step index as props
- Renders a visual progression through the steps
- Indicates completed steps with checkmarks
- Highlights the current active step
- Is typically shown during the code generation process

### Code Generation Display

The `CodeGenerationDisplay` component organizes and presents generated code files in a structured and navigable format.

#### Purpose
- Displays multiple generated code files in an organized way
- Enables navigation between different files
- Provides a unified interface for code editing and preview
- Supports file operations (save, download, etc.)
- Presents code with proper formatting and organization

#### Implementation Details
```typescript
// components/code-generation-display.tsx
export default function CodeGenerationDisplay({
  extractedCode,
  onCreateFile,
  onViewChange
}: CodeGenerationDisplayProps) {
  const [activeFileIndex, setActiveFileIndex] = useState(0);
  const [showPreview, setShowPreview] = useState(true);
  const [isFullscreenPreview, setIsFullscreenPreview] = useState(false);

  // Prepare the file array from extracted code
  const files = useMemo(() => {
    const fileArray = [];

    // Add main web files if they exist
    if (extractedCode.html) {
      fileArray.push({ name: 'index.html', language: 'html', content: extractedCode.html });
    }
    if (extractedCode.css) {
      fileArray.push({ name: 'styles.css', language: 'css', content: extractedCode.css });
    }
    if (extractedCode.js) {
      fileArray.push({ name: 'script.js', language: 'javascript', content: extractedCode.js });
    }

    // Add any other extracted files
    extractedCode.otherFiles.forEach(file => {
      fileArray.push(file);
    });

    return fileArray;
  }, [extractedCode]);

  // Handle code changes in the editor
  const handleCodeChange = (newCode: string) => {
    const currentFile = files[activeFileIndex];

    // Update the appropriate property in extractedCode
    if (currentFile.name === 'index.html') {
      extractedCode.html = newCode;
    } else if (currentFile.name === 'styles.css') {
      extractedCode.css = newCode;
    } else if (currentFile.name === 'script.js') {
      extractedCode.js = newCode;
    } else {
      // Handle other file types
      const fileIndex = extractedCode.otherFiles.findIndex(f => f.name === currentFile.name);
      if (fileIndex >= 0) {
        extractedCode.otherFiles[fileIndex].content = newCode;
      }
    }
  };

  // Handle saving the current file
  const handleSaveFile = () => {
    if (files.length > 0) {
      const currentFile = files[activeFileIndex];
      onCreateFile(currentFile.name, currentFile.content);
    }
  };

  return (
    <div className="code-generation-display">
      <div className="file-tabs">
        {files.map((file, index) => (
          <button
            key={file.name}
            className={`file-tab ${index === activeFileIndex ? 'active' : ''}`}
            onClick={() => setActiveFileIndex(index)}
          >
            {file.name}
          </button>
        ))}
      </div>

      <div className="display-content">
        <div className="editor-section">
          {files.length > 0 && (
            <EditableCodeBlock
              language={files[activeFileIndex].language}
              code={files[activeFileIndex].content}
              onCodeChange={handleCodeChange}
              onSave={handleSaveFile}
            />
          )}
        </div>

        {showPreview && (
          <div className={`preview-section ${isFullscreenPreview ? 'fullscreen' : ''}`}>
            <CodePreview
              html={extractedCode.html}
              css={extractedCode.css}
              js={extractedCode.js}
              isFullscreen={isFullscreenPreview}
              onToggleFullscreen={() => setIsFullscreenPreview(!isFullscreenPreview)}
            />
          </div>
        )}
      </div>

      <div className="display-actions">
        <button onClick={() => setShowPreview(!showPreview)}>
          {showPreview ? 'Hide Preview' : 'Show Preview'}
        </button>
        <button onClick={() => onViewChange('chat')}>
          Back to Chat
        </button>
      </div>
    </div>
  );
}
```

#### Interactions
The `CodeGenerationDisplay` component:
- Receives the extracted code data structure from parent components
- Uses `EditableCodeBlock` for code editing
- Integrates with `CodePreview` for real-time preview
- Communicates with parent components via callbacks for file creation
- Manages file navigation and active file selection

### Component Interactions Overview

These core components work together to create a seamless user experience:

1. **User Input Flow**:
   - User enters a message in `EnhancedChatInterface`
   - Message is sent to Ollama API
   - Response is streamed back and displayed in the chat

2. **Code Generation Flow**:
   - When code generation is detected, `CodeGenerationTracker` shows progress
   - Response is processed to extract code using utility functions
   - `EnhancedChatInterface` switches to editor mode
   - `CodeGenerationDisplay` shows the extracted files
   - `EditableCodeBlock` provides editing capabilities
   - `CodePreview` renders the code for immediate feedback

3. **Data Persistence Flow**:
   - Chat history is saved to localStorage
   - Generated code can be saved to files
   - Application state is maintained across sessions

4. **Component Communication**:
   - Props and callbacks for parent-child communication
   - Context API for global state (themes, preferences)
   - Custom hooks for shared functionality
   - Event-based communication for complex interactions

## Advanced Agent Capabilities

The Ollama UI Builder incorporates a sophisticated agent system that enhances the application's intelligence and capabilities:

### Reasoning Engine

The `ReasoningEngine` class provides step-by-step thinking processes for complex tasks:

```typescript
export class ReasoningEngine {
  private llmModel: string = 'llama2:7b';
  private thinkingProcesses: ThinkingProcess[] = [];
  private systemPrompt = `You are an AI assistant specialized in machine learning tasks.
  You analyze user requests carefully, understand intent, and take immediate action.`;

  // Core thinking method
  public async think(userMessage: string, conversationContext?: string): Promise<ThinkingProcess> {
    // Implementation of multi-stage reasoning process
  }

  // Execute individual thinking steps
  private async executeThinkingStep(stage: ThinkingStage, userMessage: string,
    conversationContext?: string, specificPrompt?: string): Promise<ThinkingStepResult> {
    // Implementation of individual reasoning steps
  }
}
```

### Task Management System

The `TaskManager` class orchestrates the execution of different types of tasks:

```typescript
export class TaskManager {
  private tasks: TaskState[] = [];
  private systemStatus: SystemStatus = {
    ollamaConnected: false,
    backendConnected: false,
  };
  private conversationHistory: Message[] = [];

  // Process user messages through the reasoning engine
  public async processUserMessage(userMessage: string): Promise<TaskState> {
    // Implementation of task processing workflow
  }

  // Execute API calls based on task type
  private async executeApiCalls(task: TaskState, userMessage: string): Promise<void> {
    // Implementation of API call execution
  }
}
```

### Thinking Process Stages

The reasoning system operates through multiple stages:

1. **Intent Understanding**: Analyzes the user's message to identify their primary intent
2. **Task Classification**: Categorizes the request into specific task types
3. **Execution Planning**: Determines the necessary steps to fulfill the request
4. **Response Formulation**: Generates a coherent and helpful response
5. **Error Recovery**: Handles exceptions and provides graceful fallbacks

### Specialized Agent Services

The application includes several specialized agent services:

- **Ollama Service**: Handles communication with Ollama models
- **Diagnostic Service**: Provides health checks and troubleshooting
- **API Service**: Manages backend API interactions
- **Code Generation Agents**: Specialized for different programming tasks

### Agent-Based Workflows

These components work together to create intelligent workflows:

1. User submits a request
2. The reasoning engine analyzes the intent
3. The task manager classifies and routes the request
4. Appropriate API calls are executed
5. Results are processed through the reasoning engine
6. A coherent response is formulated and presented to the user

This agent architecture enables the application to handle complex requests with more intelligence and flexibility than traditional chat interfaces.

## Key Features

### 1. Intelligent Chat Interface with Real-time Streaming

The chat interface allows users to interact with Ollama models through a familiar messaging UI. Key functionality includes:

- User/assistant message display
- Message streaming
- Code block handling
- Message history
- Chat history management

#### Implementation Details

The chat interface utilizes real-time streaming to provide immediate feedback:

```typescript
// Implementation of real-time streaming in chat interface
async function handleSendMessage(content: string) {
  // Add user message to state
  const userMessage = { role: "user", content };
  const updatedMessages = [...messages, userMessage];
  setMessages(updatedMessages);
  setIsProcessing(true);

  try {
    let fullResponse = "";

    // Stream response from Ollama
    await ollamaApi.streamChat({
      model: selectedModel,
      messages: updatedMessages,
      options: { temperature }
    },
    // Process each chunk as it arrives
    (chunk) => {
      if (chunk.message?.content) {
        fullResponse += chunk.message.content;
        // Update UI in real-time with each chunk
        setMessages(prev => [
          ...prev.slice(0, -1),
          { role: "assistant", content: fullResponse }
        ]);
      }
    },
    (error) => console.error("Error:", error),
    () => {
      // On completion
      setIsProcessing(false);
      saveToHistory(updatedMessages, fullResponse);

      // Check for code generation and extract code if needed
      if (isCodeGenerationRequest(content)) {
        const extracted = extractCodeFromText(fullResponse);
        if (hasValidCode(extracted)) {
          setExtractedCode(extracted);
          setViewMode("editor");
        }
      }
    });
  } catch (error) {
    console.error("Failed to send message:", error);
    setIsProcessing(false);
  }
}
```

#### Advanced Features

- **Intelligent Command Detection**: Recognizes special commands like `/generate` or `/clear`
- **Syntax Highlighting**: Automatically formats and highlights code blocks in responses
- **Markdown Support**: Renders markdown formatting in messages
- **Interactive Elements**: Provides clickable actions within the chat interface
- **Conversation Context Management**: Maintains coherent multi-turn conversations

### 2. Advanced Code Generation and Preview System

When a user asks to generate code, the application:

1. Identifies the request as a code generation task
2. Shows progress steps in the generation tracker
3. Extracts code blocks from the model's response
4. Organizes code into appropriate files
5. Provides a live preview of HTML/CSS/JS code
6. Allows editing of generated code

#### Code Generation Pipeline

The code generation system uses advanced prompt engineering to get high-quality results:

```typescript
// Enhanced prompt for code generation
function createCodeGenerationPrompt(userRequest: string) {
  return `
    Generate complete and functional code for: ${userRequest}

    Please follow these guidelines:
    - Provide clean, well-structured code with appropriate comments
    - Use modern coding practices and patterns
    - Separate code into appropriate files and modules
    - Include all necessary imports and dependencies
    - Format code blocks with language identifiers (e.g., \`\`\`html, \`\`\`css, \`\`\`javascript)
    - For multiple files, include the filename (e.g., \`\`\`typescript App.tsx)
  `;
}

// Process for extracting code from response
function extractCodeFiles(response: string): CodeFile[] {
  const files: CodeFile[] = [];

  // Extract code blocks with filename pattern (```language filename)
  const fileRegex = /```(\w+)\s+([^\n]+)\n([\s\S]*?)```/g;
  let match;

  while ((match = fileRegex.exec(response)) !== null) {
    const [_, language, filename, code] = match;

    files.push({
      name: filename.trim(),
      language,
      content: code.trim()
    });
  }

  // Extract standard code blocks without filenames
  if (files.length === 0) {
    const standardRegex = /```(\w+)\n([\s\S]*?)```/g;

    // Default filenames for common languages
    const defaultNames: Record<string, string> = {
      html: 'index.html',
      css: 'styles.css',
      javascript: 'script.js',
      js: 'script.js',
      typescript: 'index.ts',
      tsx: 'App.tsx',
      jsx: 'App.jsx'
    };

    while ((match = standardRegex.exec(response)) !== null) {
      const [_, language, code] = match;
      const normalizedLang = language.toLowerCase();

      files.push({
        name: defaultNames[normalizedLang] || `file.${normalizedLang}`,
        language: normalizedLang,
        content: code.trim()
      });
    }
  }

  return files;
}
```

#### Interactive Preview System

The preview system renders code in real-time with a sandboxed environment:

```typescript
// Preview component with tab navigation
function CodePreviewWithTabs({ files, activeFile, onSelectFile }) {
  const [previewMode, setPreviewMode] = useState<'preview'|'split'|'editor'>('preview');
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Extract HTML, CSS, JS content for preview
  const htmlFile = files.find(f => f.language === 'html');
  const cssFile = files.find(f => f.language === 'css');
  const jsFile = files.find(f => f.language === 'javascript' || f.language === 'js');

  // Create combined preview document
  const previewDocument = `
    <!DOCTYPE html>
    <html>
      <head>
        <style>${cssFile?.content || ''}</style>
      </head>
      <body>
        ${htmlFile?.content || ''}
        <script>${jsFile?.content || ''}</script>
      </body>
    </html>
  `;

  // Update preview when code changes
  useEffect(() => {
    if (iframeRef.current) {
      const doc = iframeRef.current.contentDocument;
      if (doc) {
        doc.open();
        doc.write(previewDocument);
        doc.close();
      }
    }
  }, [previewDocument]);

  return (
    <div className="code-preview-container">
      <div className="preview-tabs">
        {files.map(file => (
          <button
            key={file.name}
            className={file === activeFile ? 'active' : ''}
            onClick={() => onSelectFile(file)}
          >
            {file.name}
          </button>
        ))}
      </div>

      <div className="preview-actions">
        <button onClick={() => setPreviewMode('preview')}>Preview</button>
        <button onClick={() => setPreviewMode('split')}>Split View</button>
        <button onClick={() => setPreviewMode('editor')}>Editor</button>
      </div>

      <div className={`preview-content mode-${previewMode}`}>
        {(previewMode === 'preview' || previewMode === 'split') && (
          <div className="preview-pane">
            <iframe
              ref={iframeRef}
              sandbox="allow-scripts"
              title="Code Preview"
            />
          </div>
        )}

        {(previewMode === 'editor' || previewMode === 'split') && (
          <div className="editor-pane">
            <EditableCodeBlock
              language={activeFile.language}
              code={activeFile.content}
              onCodeChange={(code) => updateFileContent(activeFile, code)}
            />
          </div>
        )}
      </div>
    </div>
  );
}
```

### 3. Persistent Local Storage for Chat History

The application saves chat history to localStorage:

```typescript
const saveChat = (id: string, title: string, messages: Message[]) => {
  try {
    // Create a copy of the messages array to avoid modifying the original
    const messagesToSave = messages.map(msg => {
      // For very large messages, truncate content to save space
      if (msg.content.length > 100000) {
        return {
          ...msg,
          content: msg.content.substring(0, 100000) + "\n... [Content truncated for storage]",
          originalLength: msg.content.length
        };
      }
      return msg;
    });

    // Save to localStorage
    localStorage.setItem(`ollama-ui-chat-${id}`, JSON.stringify({ title, messages: messagesToSave }));

    // Update chat history in memory if needed
    // ...
  } catch (error) {
    console.error("Error saving chat:", error);
    // Handle errors
  }
};
```

#### Comprehensive Chat Management System

The application implements a full chat management system with indexing and retrieval:

```typescript
// lib/chat-storage.ts
export interface ChatSession {
  id: string;
  title: string;
  createdAt: number;
  updatedAt: number;
  messageCount: number;
  preview: string;
}

// Save chat messages and metadata
export function saveChat(chatId: string, messages: Message[]): void {
  if (!chatId || messages.length === 0) return;

  try {
    // Generate title from first few messages
    const title = generateChatTitle(messages);

    // Process messages for storage efficiency
    const processedMessages = prepareMessagesForStorage(messages);

    // Current timestamp
    const now = Date.now();

    // Full chat data
    const chatData = {
      id: chatId,
      title,
      messages: processedMessages,
      createdAt: now,
      updatedAt: now
    };

    // Save full chat data
    localStorage.setItem(`ollama-chat-${chatId}`, JSON.stringify(chatData));

    // Update chat index
    updateChatIndex(chatId, {
      id: chatId,
      title,
      createdAt: now,
      updatedAt: now,
      messageCount: messages.length,
      preview: createChatPreview(messages)
    });
  } catch (error) {
    console.error('Error saving chat:', error);
  }
}

// Load a chat by ID
export function loadChat(chatId: string): { title: string; messages: Message[] } | null {
  try {
    const chatJson = localStorage.getItem(`ollama-chat-${chatId}`);
    if (!chatJson) return null;

    const chatData = JSON.parse(chatJson);
    return {
      title: chatData.title,
      messages: chatData.messages
    };
  } catch (error) {
    console.error(`Error loading chat ${chatId}:`, error);
    return null;
  }
}

// Get all chat sessions (metadata only)
export function getAllChats(): ChatSession[] {
  try {
    const indexJson = localStorage.getItem('ollama-chat-index');
    if (!indexJson) return [];

    const chatIndex = JSON.parse(indexJson);

    // Sort by last updated (newest first)
    return chatIndex.sort((a: ChatSession, b: ChatSession) =>
      b.updatedAt - a.updatedAt
    );
  } catch (error) {
    console.error('Error loading chat index:', error);
    return [];
  }
}

// Delete a chat
export function deleteChat(chatId: string): boolean {
  try {
    // Remove chat data
    localStorage.removeItem(`ollama-chat-${chatId}`);

    // Update index
    const indexJson = localStorage.getItem('ollama-chat-index');
    if (indexJson) {
      const chatIndex = JSON.parse(indexJson);
      const updatedIndex = chatIndex.filter((chat: ChatSession) => chat.id !== chatId);
      localStorage.setItem('ollama-chat-index', JSON.stringify(updatedIndex));
    }

    return true;
  } catch (error) {
    console.error(`Error deleting chat ${chatId}:`, error);
    return false;
  }
}

// Helper functions
function generateChatTitle(messages: Message[]): string {
  // Find first user message
  const firstUserMsg = messages.find(m => m.role === 'user');
  if (!firstUserMsg) return 'New Chat';

  // Extract first line, truncate to reasonable length
  const firstLine = firstUserMsg.content.split('\n')[0].trim();
  return firstLine.length > 50 ? firstLine.substring(0, 50) + '...' : firstLine;
}

function createChatPreview(messages: Message[]): string {
  // Get last assistant message for preview
  for (let i = messages.length - 1; i >= 0; i--) {
    if (messages[i].role === 'assistant') {
      const content = messages[i].content;
      // Strip code blocks and markdown
      const plainText = content.replace(/```[\s\S]*?```/g, '[code block]')
                               .replace(/\*\*(.*?)\*\*/g, '$1');
      return plainText.substring(0, 100) + (plainText.length > 100 ? '...' : '');
    }
  }
  return 'No responses yet';
}

function updateChatIndex(chatId: string, chatInfo: ChatSession): void {
  let chatIndex = [];

  try {
    const indexJson = localStorage.getItem('ollama-chat-index');
    if (indexJson) {
      chatIndex = JSON.parse(indexJson);
    }

    // Update or add to index
    const existingIndex = chatIndex.findIndex((chat: ChatSession) => chat.id === chatId);
    if (existingIndex >= 0) {
      chatIndex[existingIndex] = chatInfo;
    } else {
      chatIndex.push(chatInfo);
    }

    localStorage.setItem('ollama-chat-index', JSON.stringify(chatIndex));
  } catch (error) {
    console.error('Error updating chat index:', error);
  }
}
```

#### Chat Management UI

The application provides a complete interface for managing chat history:

```tsx
// components/chat-history-sidebar.tsx
export function ChatHistorySidebar({
  onSelectChat,
  onNewChat,
  currentChatId
}: ChatHistorySidebarProps) {
  const [chats, setChats] = useState<ChatSession[]>([]);
  const [searchQuery, setSearchQuery] = useState('');

  // Load chats from storage
  useEffect(() => {
    const loadedChats = getAllChats();
    setChats(loadedChats);
  }, []);

  // Filter chats based on search
  const filteredChats = useMemo(() => {
    if (!searchQuery.trim()) return chats;

    const query = searchQuery.toLowerCase();
    return chats.filter(chat =>
      chat.title.toLowerCase().includes(query) ||
      chat.preview.toLowerCase().includes(query)
    );
  }, [chats, searchQuery]);

  // Handle chat deletion
  const handleDeleteChat = (event: React.MouseEvent, chatId: string) => {
    event.stopPropagation();

    if (confirm('Are you sure you want to delete this chat?')) {
      const success = deleteChat(chatId);
      if (success) {
        setChats(chats.filter(chat => chat.id !== chatId));

        // If current chat was deleted, create a new one
        if (currentChatId === chatId) {
          onNewChat();
        }
      }
    }
  };

  return (
    <div className="chat-history-sidebar">
      <div className="sidebar-header">
        <button className="new-chat-button" onClick={onNewChat}>
          <PlusIcon /> New Chat
        </button>

        <div className="search-container">
          <input
            type="text"
            placeholder="Search chats..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="search-input"
          />
          {searchQuery && (
            <button
              className="clear-search"
              onClick={() => setSearchQuery('')}
              aria-label="Clear search"
            >
              <XIcon />
            </button>
          )}
        </div>
      </div>

      <div className="chat-list">
        {filteredChats.length === 0 ? (
          <div className="no-chats-message">
            {searchQuery ? 'No matching chats found.' : 'No chat history yet.'}
          </div>
        ) : (
          filteredChats.map(chat => (
            <div
              key={chat.id}
              className={`chat-item ${chat.id === currentChatId ? 'active' : ''}`}
              onClick={() => onSelectChat(chat.id)}
            >
              <div className="chat-item-content">
                <div className="chat-title">{chat.title}</div>
                <div className="chat-preview">{chat.preview}</div>
                <div className="chat-meta">
                  <span className="chat-date">
                    {new Date(chat.updatedAt).toLocaleDateString()}
                  </span>
                  <span className="message-count">
                    {chat.messageCount} message{chat.messageCount !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>

              <button
                className="delete-chat-button"
                onClick={(e) => handleDeleteChat(e, chat.id)}
                aria-label="Delete chat"
              >
                <TrashIcon />
              </button>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
```

### 4. Sophisticated Code Extraction and Formatting

The application extracts code blocks from model responses using advanced regex patterns:

```typescript
// lib/code-extraction.ts
export function extractCodeBlocks(text: string): ExtractedCode {
  const result: ExtractedCode = {
    html: '',
    css: '',
    js: '',
    combined: '',
    otherFiles: []
  };

  // Extract HTML code blocks
  const htmlMatches = [...text.matchAll(/```html\n([\s\S]*?)```/g)];
  if (htmlMatches.length > 0) {
    // Use the last HTML block if multiple are found
    result.html = htmlMatches[htmlMatches.length - 1][1].trim();
  }

  // Extract CSS code blocks
  const cssMatches = [...text.matchAll(/```css\n([\s\S]*?)```/g)];
  if (cssMatches.length > 0) {
    result.css = cssMatches[cssMatches.length - 1][1].trim();
  }

  // Extract JavaScript code blocks (supports both js and javascript tags)
  const jsMatches = [...text.matchAll(/```(javascript|js)\n([\s\S]*?)```/g)];
  if (jsMatches.length > 0) {
    result.js = jsMatches[jsMatches.length - 1][2].trim();
  }

  // Create combined HTML document for preview
  result.combined = `
<!DOCTYPE html>
<html>
<head>
  <style>${result.css}</style>
</head>
<body>
  ${result.html}
  <script>${result.js}</script>
</body>
</html>
  `.trim();

  return result;
}
```

#### Code Formatting and Beautification

The application uses Prettier to format extracted code, ensuring consistent style:

```typescript
// lib/code-formatter.ts
import prettier from 'prettier';
import parserBabel from 'prettier/parser-babel';
import parserHtml from 'prettier/parser-html';
import parserPostcss from 'prettier/parser-postcss';
import parserTypescript from 'prettier/parser-typescript';

export async function formatCode(code: string, language: string): Promise<string> {
  if (!code || code.trim() === '') {
    return code;
  }

  // Map language to appropriate parser
  let parser: string;
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'js':
      parser = 'babel';
      break;
    case 'typescript':
    case 'ts':
      parser = 'typescript';
      break;
    case 'jsx':
      parser = 'babel';
      break;
    case 'tsx':
      parser = 'typescript';
      break;
    case 'html':
      parser = 'html';
      break;
    case 'css':
      parser = 'css';
      break;
    case 'json':
      parser = 'json';
      break;
    default:
      // Return unformatted for unsupported languages
      return code;
  }

  try {
    // Format using Prettier with appropriate settings
    const formatted = await prettier.format(code, {
      parser,
      plugins: [parserBabel, parserHtml, parserPostcss, parserTypescript],
      printWidth: 80,
      tabWidth: 2,
      useTabs: false,
      semi: true,
      singleQuote: true,
      trailingComma: 'es5',
      bracketSpacing: true,
      arrowParens: 'avoid'
    });

    return formatted;
  } catch (error) {
    console.error(`Error formatting ${language} code:`, error);
    // Return original if formatting fails
    return code;
  }
}
```

### 5. Responsive UI with Accessibility Features

The application includes a responsive design with built-in accessibility features for an inclusive user experience. Key capabilities include:

- Adapts to different screen sizes and devices
- Support for keyboard navigation
- Screen reader compatibility with ARIA attributes
- Dark and light theme modes
- Proper color contrast for text readability
- Focus management for keyboard users
- Mobile-friendly touch targets

#### Theme Implementation

The application implements a comprehensive theming system:

```typescript
// contexts/theme-context.tsx
import React, { createContext, useContext, useEffect, useState } from 'react';

type Theme = 'light' | 'dark' | 'system';

interface ThemeContextType {
  theme: Theme;
  resolvedTheme: 'light' | 'dark';
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Get initial theme from localStorage or default to system
  const [theme, setThemeState] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('theme') as Theme;
      return savedTheme || 'system';
    }
    return 'system';
  });

  // Determine the actual theme based on system preference if needed
  const [resolvedTheme, setResolvedTheme] = useState<'light' | 'dark'>('light');

  // Update the resolved theme when theme changes or system preference changes
  useEffect(() => {
    const updateResolvedTheme = () => {
      if (theme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches
          ? 'dark'
          : 'light';
        setResolvedTheme(systemTheme);
      } else {
        setResolvedTheme(theme as 'light' | 'dark');
      }
    };

    updateResolvedTheme();

    // Listen for system theme changes
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    mediaQuery.addEventListener('change', updateResolvedTheme);

    return () => mediaQuery.removeEventListener('change', updateResolvedTheme);
  }, [theme]);

  // Apply theme to document
  useEffect(() => {
    document.documentElement.classList.remove('light', 'dark');
    document.documentElement.classList.add(resolvedTheme);
  }, [resolvedTheme]);

  // Set theme and save to localStorage
  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    if (typeof window !== 'undefined') {
      localStorage.setItem('theme', newTheme);
    }
  };

  return (
    <ThemeContext.Provider value={{ theme, resolvedTheme, setTheme }}>
      {children}
    </ThemeContext.Provider>
  );
}
```

#### Responsive Layout Component

The application uses a responsive layout with accessibility features:

```tsx
// components/responsive-layout.tsx
export default function ResponsiveLayout({
  children,
  sidebar,
  showSidebar,
  onToggleSidebar
}: ResponsiveLayoutProps) {
  // Track screen size with useMediaQuery hook
  const isMobile = useMediaQuery('(max-width: 768px)');

  // Trap focus in sidebar when open on mobile
  useEffect(() => {
    if (isMobile && showSidebar) {
      // Focus trap implementation
      const sidebarElement = document.getElementById('sidebar');
      if (sidebarElement) {
        const focusableElements = sidebarElement.querySelectorAll(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElements.length > 0) {
          (focusableElements[0] as HTMLElement).focus();
        }

        // Handle tab and shift+tab navigation
        const handleKeyDown = (e: KeyboardEvent) => {
          if (e.key === 'Escape') {
            onToggleSidebar();
            return;
          }

          if (e.key === 'Tab') {
            if (focusableElements.length === 0) return;

            const firstElement = focusableElements[0] as HTMLElement;
            const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

            if (e.shiftKey && document.activeElement === firstElement) {
              e.preventDefault();
              lastElement.focus();
            } else if (!e.shiftKey && document.activeElement === lastElement) {
              e.preventDefault();
              firstElement.focus();
            }
          }
        };

        document.addEventListener('keydown', handleKeyDown);
        return () => document.removeEventListener('keydown', handleKeyDown);
      }
    }
  }, [isMobile, showSidebar, onToggleSidebar]);

  return (
    <div className="responsive-layout">
      {/* Mobile header with menu button */}
      {isMobile && (
        <header className="mobile-header" role="banner">
          <button
            onClick={onToggleSidebar}
            className="menu-button"
            aria-label={showSidebar ? "Close sidebar" : "Open sidebar"}
            aria-expanded={showSidebar}
          >
            <MenuIcon aria-hidden="true" />
          </button>
          <h1 className="app-title">Ollama UI Builder</h1>
        </header>
      )}

      {/* Sidebar with proper ARIA attributes */}
      <aside
        id="sidebar"
        className={`sidebar ${showSidebar ? 'visible' : 'hidden'} ${isMobile ? 'mobile' : 'desktop'}`}
        aria-hidden={isMobile && !showSidebar}
        role="navigation"
        aria-label="Main navigation"
      >
        {sidebar}

        {/* Close button for mobile */}
        {isMobile && (
          <button
            className="close-sidebar"
            onClick={onToggleSidebar}
            aria-label="Close sidebar"
          >
            <XIcon aria-hidden="true" />
          </button>
        )}
      </aside>

      {/* Overlay for mobile sidebar */}
      {isMobile && showSidebar && (
        <div
          className="sidebar-overlay"
          onClick={onToggleSidebar}
          aria-hidden="true"
        />
      )}

      {/* Main content area */}
      <main
        id="main-content"
        className={`main-content ${isMobile && showSidebar ? 'blurred' : ''}`}
        aria-live="polite"
      >
        {children}
      </main>
    </div>
  );
}
```

This documentation provides a comprehensive overview of the Ollama UI Builder, its architecture, and implementation details. Developers should be able to use this as a guide to understand and recreate the project.

Don't forget to commit your changes:
```
git add .
git commit -m "Add detailed Ollama UI Builder documentation"
```

## Project Structure

This section provides a comprehensive breakdown of the Ollama UI Builder project structure, detailing the purpose and organization of directories and files.

### Directory Structure Overview

```
ollama-ui/
├── app/                              # Next.js app directory (App Router)
│   ├── layout.tsx                    # Root layout with providers and global structure
│   ├── page.tsx                      # Main application page component
│   ├── api/                          # API route handlers
│   │   └── proxy/                    # Proxy routes for Ollama API
│   │       └── route.ts              # Handles proxying requests to Ollama
│   ├── globals.css                   # Global CSS styles
│   └── favicon.ico                   # Site favicon
├── components/                       # React components
│   ├── core/                         # Core application components
│   │   ├── code-preview.tsx          # Live code preview component
│   │   ├── editable-code-block.tsx   # Code editor with Monaco
│   │   └── enhanced-chat-interface.tsx # Main chat interface
│   ├── code/                         # Code-related components
│   │   ├── code-generation-display.tsx # Display for generated code
│   │   ├── code-generation-tracker.tsx # Progress tracker
│   │   └── syntax-highlighter.tsx    # Code syntax highlighting
│   ├── layout/                       # Layout components
│   │   ├── header.tsx                # Application header
│   │   ├── sidebar.tsx               # Sidebar navigation
│   │   └── responsive-layout.tsx     # Responsive layout wrapper
│   └── ui/                           # UI components (shadcn/ui based)
│       ├── button.tsx                # Button component
│       ├── dialog.tsx                # Dialog/modal component
│       ├── tabs.tsx                  # Tabs component
│       ├── textarea.tsx              # Enhanced textarea
│       └── ... (other UI components)
├── contexts/                         # React context providers
│   ├── settings-context.tsx          # Application settings context
│   ├── theme-context.tsx             # Theme mode context
│   ├── chat-context.tsx              # Chat state and history context
│   └── ollama-context.tsx            # Ollama connection context
├── hooks/                            # Custom React hooks
│   ├── use-chat-history.ts           # Hook for managing chat history
│   ├── use-code-extraction.ts        # Hook for extracting code from responses
│   ├── use-local-storage.ts          # Persistent storage hook
│   ├── use-ollama.ts                 # Ollama API interaction hook
│   ├── use-streaming.ts              # Streaming response handling
│   └── use-toast.ts                  # Toast notification hook
├── lib/                              # Utility libraries
│   ├── ollama-api.ts                 # Ollama API client implementation
│   ├── code-formatter.ts             # Code formatting with Prettier
│   ├── code-extraction.ts            # Code block extraction utilities
│   ├── utils.ts                      # General utility functions
│   └── constants.ts                  # Application constants
├── public/                           # Static assets
│   ├── logo.svg                      # Application logo
│   └── models/                       # Model-related assets
│       └── icons/                    # Model icons
├── styles/                           # Additional styles
│   ├── monaco-theme.ts               # Monaco editor theme
│   └── syntax-highlighting.css       # Syntax highlighting styles
├── types/                            # TypeScript type definitions
│   ├── ollama.ts                     # Ollama API types
│   ├── chat.ts                       # Chat and message types
│   └── code.ts                       # Code-related types
├── config/                           # Configuration files
│   ├── site.ts                       # Site metadata and config
│   └── models.ts                     # Model configurations
├── next.config.mjs                   # Next.js configuration
├── postcss.config.js                 # PostCSS configuration
├── tailwind.config.ts                # Tailwind CSS configuration
├── tsconfig.json                     # TypeScript configuration
└── package.json                      # Project dependencies
```

### Key Directories Explained

#### 1. `app/` Directory

The `app/` directory uses Next.js App Router pattern and contains:

- **layout.tsx**: The root layout component that wraps the entire application with necessary providers:
  ```tsx
  // app/layout.tsx
  export default function RootLayout({ children }: { children: React.ReactNode }) {
    return (
      <html lang="en" suppressHydrationWarning>
        <body className={inter.className}>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
            <SettingsProvider>
              <OllamaProvider>
                <ChatProvider>
                  <Toaster />
                  {children}
                </ChatProvider>
              </OllamaProvider>
            </SettingsProvider>
          </ThemeProvider>
        </body>
      </html>
    );
  }
  ```

- **page.tsx**: The main application page that serves as the entry point:
  ```tsx
  // app/page.tsx (simplified)
  export default function Home() {
    const { models, selectedModel, setSelectedModel } = useOllama();
    const { settings } = useSettings();

    return (
      <ResponsiveLayout
        sidebar={<Sidebar />}
        header={<Header />}
      >
        <EnhancedChatInterface
          selectedModel={selectedModel}
          temperature={settings.temperature}
          onCreateFile={handleFileCreation}
        />
      </ResponsiveLayout>
    );
  }
  ```

#### 2. `components/` Directory

The components directory is organized by functionality:

- **core/**: Contains the primary components that form the backbone of the application:
  - `enhanced-chat-interface.tsx`: The main chat component
  - `code-preview.tsx`: Live preview of generated code
  - `editable-code-block.tsx`: Monaco-powered code editor

- **ui/**: Reusable UI components built on shadcn/ui:
  - `button.tsx`, `dialog.tsx`, `tabs.tsx`, etc.
  - These are styled with Tailwind CSS and follow a consistent design system

- **layout/**: Components that define the overall application layout:
  - `header.tsx`: Top navigation and actions
  - `sidebar.tsx`: Side navigation and model selection
  - `responsive-layout.tsx`: Handles responsive behavior for different devices

#### 3. `lib/` Directory

The `lib/` directory contains utility functions and core logic:

- **ollama-api.ts**: Implements the Ollama API client:
  ```typescript
  // lib/ollama-api.ts (simplified interface)
  export interface OllamaAPI {
    getModels(): Promise<string[]>;
    streamCompletion(options: GenerateRequestOptions, ...callbacks): Promise<void>;
    streamChat(options: ChatRequestOptions, ...callbacks): Promise<void>;
    createEmbedding(options: EmbeddingRequestOptions): Promise<EmbeddingResponse>;
  }

  export function createOllamaAPI(connectionMode: "local" | "auto" | "custom" = "auto",
                                 apiUrl?: string): OllamaAPI {
    // Implementation details
  }
  ```

- **code-formatter.ts**: Handles code formatting using Prettier
- **code-extraction.ts**: Contains logic to extract code blocks from LLM responses
- **utils.ts**: General utilities for string manipulation, error handling, etc.

#### 4. `hooks/` Directory

Custom React hooks for reusable functionality:

- **use-ollama.ts**: For interacting with the Ollama API:
  ```typescript
  // hooks/use-ollama.ts (simplified)
  export function useOllama() {
    const [models, setModels] = useState<string[]>([]);
    const [selectedModel, setSelectedModel] = useState<string>("");
    const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>("disconnected");

    // Functions for model operations and API interactions
    const fetchModels = async () => {/* implementation */};
    const generateCompletion = async (prompt: string) => {/* implementation */};
    const streamChat = async (messages: Message[]) => {/* implementation */};

    return {
      models,
      selectedModel,
      setSelectedModel,
      connectionStatus,
      fetchModels,
      generateCompletion,
      streamChat
    };
  }
  ```

- **use-chat-history.ts**: For managing chat persistence
- **use-code-extraction.ts**: For code block extraction from responses
- **use-streaming.ts**: For handling streaming responses from the Ollama API

#### 5. `contexts/` Directory

React contexts for global state management:

- **settings-context.tsx**: Manages application settings:
  ```typescript
  // contexts/settings-context.tsx (simplified)
  export const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

  export function SettingsProvider({ children }: { children: React.ReactNode }) {
    const [settings, setSettings] = useState<Settings>({
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: DEFAULT_SYSTEM_PROMPT,
      connectionMode: "auto",
      customApiUrl: "",
      theme: "system"
    });

    // Save settings to localStorage when they change
    useEffect(() => {
      localStorage.setItem("ollama-ui-settings", JSON.stringify(settings));
    }, [settings]);

    return (
      <SettingsContext.Provider value={{ settings, setSettings }}>
        {children}
      </SettingsContext.Provider>
    );
  }
  ```

- **theme-context.tsx**: Handles theme mode (light/dark)
- **chat-context.tsx**: Manages chat state and history
- **ollama-context.tsx**: Provides Ollama connection and model information

#### 6. `types/` Directory

TypeScript type definitions that structure the application data:

- **ollama.ts**: Types for the Ollama API:
  ```typescript
  // types/ollama.ts (simplified)
  export interface GenerateRequestOptions {
    model: string;
    prompt: string;
    options?: {
      temperature?: number;
      top_p?: number;
      top_k?: number;
      seed?: number;
      num_predict?: number;
    };
    stream?: boolean;
  }

  export interface ChatRequestOptions {
    model: string;
    messages: { role: string; content: string }[];
    options?: {
      temperature?: number;
      // Other options
    };
    stream?: boolean;
  }
  ```

- **chat.ts**: Types for chat messages and history
- **code.ts**: Types for code files and code generation

### Configuration Files

- **next.config.mjs**: Next.js configuration including API rewrites
- **tailwind.config.ts**: Tailwind CSS theme and plugin configuration
- **tsconfig.json**: TypeScript compiler options
- **package.json**: Project dependencies and scripts

### Key File Relationships

The project follows a modular architecture with clear relationships between files:

1. **UI Flow**:
   - `app/page.tsx` → `components/layout/responsive-layout.tsx` → `components/core/enhanced-chat-interface.tsx`

2. **Data Flow**:
   - User input → `hooks/use-ollama.ts` → `lib/ollama-api.ts` → Ollama server → `hooks/use-streaming.ts` → UI update

3. **Code Generation Flow**:
   - LLM response → `hooks/use-code-extraction.ts` → `lib/code-extraction.ts` → `components/code/code-generation-display.tsx`

This structure enables maintainable development and clear separation of concerns throughout the application.

## Development Guide

This section provides detailed guidance for setting up, developing, and troubleshooting the Ollama UI Builder.

### Prerequisites

Before you begin working with the Ollama UI Builder, ensure you have the following installed:

- **Node.js**: Version 18.x or newer
- **npm** or **yarn**: For package management
- **Ollama**: The local Ollama server must be installed and running
- **Git**: For version control

### Setting Up the Development Environment

Follow these steps to set up your development environment:

1. **Clone the repository**:
   ```bash
git clone https://github.com/yourusername/ollama-ui.git
cd ollama-ui
```

2. **Install dependencies**:
   ```bash
npm install
   # or
   yarn install
   ```

3. **Set up environment variables**:
   Create a `.env.local` file in the root of the project with the following variables:
   ```
   # Ollama API settings
   NEXT_PUBLIC_OLLAMA_API_BASE_URL=http://localhost:11434

   # Optional settings
   NEXT_PUBLIC_DEFAULT_MODEL=llama3
   NEXT_PUBLIC_ENABLE_DEBUG_LOGGING=false
   ```

4. **Start the development server**:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Access the application**:
   Open your browser and navigate to `http://localhost:3000`

### Key Development Workflows

#### Adding a New Component

1. Create the component file in the appropriate directory:
   ```tsx
   // components/ui/new-component.tsx
   import React from 'react';

   interface NewComponentProps {
     // Define props here
   }

   export const NewComponent: React.FC<NewComponentProps> = (props) => {
     // Implement component
     return (
       <div>
         {/* Component JSX */}
       </div>
     );
   };
   ```

2. If needed, add styles using Tailwind CSS classes:
   ```tsx
   <div className="rounded-lg bg-background p-4 shadow-md dark:bg-gray-800">
     {/* Styled component content */}
   </div>
   ```

3. Export the component:
   ```tsx
   export { NewComponent };
   // or
   export default NewComponent;
   ```

4. Import and use the component in other parts of the application:
   ```tsx
   import { NewComponent } from '@/components/ui/new-component';

   // Use in JSX
   <NewComponent />
   ```

#### Working with Ollama API

1. **Establish API connection**:
   ```tsx
   import { createOllamaAPI } from '@/lib/ollama-api';

   // Create an API instance (typically done in a context provider)
   const api = createOllamaAPI('auto');
   ```

2. **Fetch available models**:
   ```tsx
   const fetchModels = async () => {
     try {
       const models = await api.getModels();
       console.log('Available models:', models);
     } catch (error) {
       console.error('Failed to fetch models:', error);
     }
   };
   ```

3. **Generate completions with streaming**:
   ```tsx
   const generateText = async (prompt: string) => {
     try {
       let fullResponse = '';

       await api.streamCompletion(
         {
           model: 'llama3',
           prompt: prompt,
           options: {
             temperature: 0.7,
           },
           stream: true,
         },
         (chunk) => {
           // Process each chunk as it arrives
           fullResponse += chunk.response;
           setPartialResponse(fullResponse);
         },
         () => {
           // Called when streaming is complete
           console.log('Streaming completed');
         }
       );
     } catch (error) {
       console.error('Generation failed:', error);
     }
   };
   ```

#### Adding a New Hook

1. Create the hook file in the `hooks` directory:
   ```tsx
   // hooks/use-new-feature.ts
   import { useState, useEffect } from 'react';

   export function useNewFeature(param: string) {
     const [state, setState] = useState<any>(null);

     useEffect(() => {
       // Effect implementation
       const handleFeature = async () => {
         // Async operations
         setState(result);
       };

       handleFeature();
     }, [param]);

     const helperFunction = () => {
       // Additional functionality
     };

     return {
       state,
       helperFunction,
     };
   }
   ```

2. Use the hook in components:
   ```tsx
   import { useNewFeature } from '@/hooks/use-new-feature';

   function MyComponent() {
     const { state, helperFunction } = useNewFeature('param');

     // Use in component
   }
   ```

### Testing Your Changes

The Ollama UI Builder includes several types of tests to ensure quality:

1. **Run component tests**:
   ```bash
   npm test
   # or
   yarn test
   ```

2. **Run e2e tests**:
   ```bash
   npm run test:e2e
   # or
   yarn test:e2e
   ```

3. **Type checking**:
   ```bash
   npm run type-check
   # or
   yarn type-check
   ```

4. **Lint your code**:
   ```bash
   npm run lint
   # or
   yarn lint
   ```

### Building for Production

To create a production build:

1. **Build the application**:
   ```bash
   npm run build
   # or
   yarn build
   ```

2. **Test the production build locally**:
   ```bash
   npm run start
   # or
   yarn start
   ```

3. **Deployment options**:
   - Deploy to Vercel: `vercel --prod`
   - Deploy to Netlify or other platforms using their respective deployment tools
   - For self-hosting, use Docker:
     ```bash
     docker build -t ollama-ui .
     docker run -p 3000:3000 ollama-ui
     ```

### Troubleshooting Common Issues

#### Ollama Connection Problems

1. **Ollama server not detected**:
   - Ensure Ollama is running: `ollama serve`
   - Check if Ollama is accessible at `http://localhost:11434`
   - Verify network settings if running on a different machine

2. **CORS issues**:
   - If encountering CORS errors, use the built-in proxy by setting:
     ```
     NEXT_PUBLIC_USE_API_PROXY=true
     ```
   - This routes requests through the Next.js API proxy to avoid CORS problems

3. **API request errors**:
   - Check browser console for specific error messages
   - Verify API URL is correct in settings
   - Confirm that requested models are installed in Ollama (`ollama list`)

#### UI and Rendering Issues

1. **Components not rendering correctly**:
   - Clear browser cache and reload
   - Check for CSS conflicts, especially when using custom themes
   - Verify that component props are correctly passed

2. **Performance problems**:
   - Use React DevTools to identify unnecessary re-renders
   - Check for memory leaks by monitoring component mount/unmount cycles
   - Consider optimizing with `useMemo`, `useCallback`, and `memo`

3. **Monaco editor not loading properly**:
   - Ensure Monaco editor is properly loaded:
     ```tsx
     import { loader } from '@monaco-editor/react';

     // Set path to Monaco workers
     loader.config({
       paths: {
         vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.36.1/min/vs',
       },
     });
     ```

#### Code Generation and Extraction Issues

1. **Code not properly formatted**:
   - Check Prettier configuration in `code-formatter.ts`
   - Verify that language parsers are correctly installed:
```typescript
     // Required parser packages
     // prettier
     // @prettier/plugin-babel
     // prettier-plugin-tailwindcss
     // etc.
     ```

2. **Code extraction failing**:
   - Debug regex patterns in `code-extraction.ts`
   - Add additional logging to trace extraction process

### Contributing Guidelines

When contributing to the Ollama UI Builder:

1. **Branching strategy**:
   - Create feature branches from `main`: `git checkout -b feature/your-feature-name`
   - For bugfixes, use: `git checkout -b fix/issue-description`

2. **Commit conventions**:
   - Use conventional commits: `feat:`, `fix:`, `chore:`, `docs:`, etc.
   - Include issue numbers when applicable: `fix(#123): resolve connection bug`

3. **Pull request process**:
   - Ensure all tests pass before submitting
   - Provide a clear description of changes
   - Link to any related issues
   - Request reviews from maintainers

4. **Code style**:
   - Follow the established project style (enforced by ESLint/Prettier)
   - Document complex functions with JSDoc comments
   - Use meaningful variable and function names

## Implementation Details

This section provides in-depth technical details about key implementation aspects of the Ollama UI Builder, focusing on advanced features, optimizations, and internal workings.

### Ollama API Communication Layer

The communication with Ollama is handled through a carefully designed API layer that supports different connection modes and handles streaming efficiently.

#### Connection Management

The API connection is implemented with built-in resilience and error handling:

```typescript
// lib/ollama-api.ts (simplified)
export function createOllamaAPI(connectionMode: ConnectionMode, customUrl?: string): OllamaAPI {
  // Determine base URL based on connection mode
  const baseUrl = determineBaseUrl(connectionMode, customUrl);

  // Create fetch wrapper with timeout and error handling
  const enhancedFetch = async (endpoint: string, options?: RequestInit) => {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), API_TIMEOUT_MS);

      const response = await fetch(`${baseUrl}${endpoint}`, {
        ...options,
        signal: controller.signal,
      headers: {
        'Content-Type': 'application/json',
          ...options?.headers,
      },
    });

      clearTimeout(timeoutId);

    if (!response.ok) {
        throw new ApiError(`API error: ${response.status}`, response.status);
      }

      return response;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408);
      }
      throw error;
    }
  };

  // Return API methods
  return {
    getModels: async () => {
      const response = await enhancedFetch('/api/tags');
      const data = await response.json();
      return data.models.map(model => model.name);
    },

    // Additional methods...
  };
}
```

#### Streaming Implementation

The streaming functionality uses the Fetch API's streaming capabilities to process chunks of data as they arrive:

```typescript
// lib/ollama-api.ts
const streamCompletion = async (
  options: GenerateRequestOptions,
  onChunk: (chunk: GenerateResponseChunk) => void,
  onComplete?: () => void
): Promise<void> => {
  const response = await enhancedFetch('/api/generate', {
    method: 'POST',
    body: JSON.stringify({ ...options, stream: true }),
  });

  if (!response.body) {
    throw new Error('Response body is null');
  }

  const reader = response.body.getReader();
    const decoder = new TextDecoder();
    let buffer = '';

  try {
    while (true) {
      const { done, value } = await reader.read();

      if (done) {
        break;
      }

      // Process the binary data
      buffer += decoder.decode(value, { stream: true });

      // Split buffer by newlines and process complete chunks
        const lines = buffer.split('\n');
        buffer = lines.pop() || '';

        for (const line of lines) {
        if (line.trim() === '') continue;

        try {
          const chunk = JSON.parse(line);
          onChunk(chunk);
        } catch (e) {
          console.error('Failed to parse chunk:', line, e);
        }
      }
    }

    // Process any remaining data in the buffer
    if (buffer.trim() !== '') {
      try {
        const chunk = JSON.parse(buffer);
        onChunk(chunk);
      } catch (e) {
        console.error('Failed to parse final chunk:', buffer, e);
      }
    }

    onComplete?.();
  } catch (error) {
    // Handle stream errors
    throw new Error(`Stream error: ${error.message}`);
  }
};
```

### React Component Architecture

The application follows a sophisticated component architecture with careful attention to performance optimization and reusability.

#### Memoization Strategy

Critical components use React's memoization features to prevent unnecessary re-renders:

```typescript
// components/core/enhanced-chat-interface.tsx
const EnhancedChatInterface = React.memo(({
  selectedModel,
  temperature,
  onCreateFile
}: EnhancedChatInterfaceProps) => {
  // Component implementation

  // Memoize expensive computations
  const processedMessages = useMemo(() => {
    return messages.map(processMessage);
  }, [messages]);

  // Memoize callbacks
  const handleSubmit = useCallback((message: string) => {
    // Handle message submission
  }, [selectedModel, temperature]);

  return (
    // JSX implementation
  );
});
```

#### Virtualization for Performance

For handling large chat histories, the chat interface implements virtualization to render only visible messages:

```typescript
// components/core/chat-message-list.tsx
import { useVirtualizer } from '@tanstack/react-virtual';

const ChatMessageList = ({ messages }: ChatMessageListProps) => {
  const containerRef = useRef<HTMLDivElement>(null);

  // Set up virtualization
  const virtualizer = useVirtualizer({
    count: messages.length,
    getScrollElement: () => containerRef.current,
    estimateSize: () => 100, // Estimate height of each row
    overscan: 5, // Number of items to render outside visible area
  });

  return (
    <div
      ref={containerRef}
      className="chat-container h-full overflow-auto"
    >
      <div
        style={{
          height: `${virtualizer.getTotalSize()}px`,
          width: '100%',
          position: 'relative',
        }}
      >
        {virtualizer.getVirtualItems().map((virtualItem) => (
          <div
            key={virtualItem.key}
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: '100%',
              height: `${virtualItem.size}px`,
              transform: `translateY(${virtualItem.start}px)`,
            }}
          >
            <ChatMessage message={messages[virtualItem.index]} />
          </div>
        ))}
      </div>
    </div>
  );
};
```

### Code Extraction and Formatting System

The code extraction and formatting system uses sophisticated regex patterns and Prettier for consistent code styling.

#### Advanced Regex Patterns

The code extraction uses carefully crafted regex patterns to identify and extract code blocks:

```typescript
// lib/code-extraction.ts
const CODE_BLOCK_REGEX = /```([\w-]*)\n([\s\S]*?)```/g;
const LANGUAGE_ALIASES: Record<string, string> = {
  js: 'javascript',
  ts: 'typescript',
  jsx: 'javascript',
  tsx: 'typescript',
  // Additional aliases...
};

export function extractCodeBlocks(text: string): ExtractedCode[] {
  const blocks: ExtractedCode[] = [];
  let match;

  // Reset regex state
  CODE_BLOCK_REGEX.lastIndex = 0;

  while ((match = CODE_BLOCK_REGEX.exec(text)) !== null) {
    const [, lang, code] = match;
    const language = LANGUAGE_ALIASES[lang.toLowerCase()] || lang.toLowerCase() || 'plaintext';

    blocks.push({
      language,
      code: code.trim(),
      startIndex: match.index,
      endIndex: match.index + match[0].length,
    });
  }

  return blocks;
}
```

#### Dynamic Prettier Configuration

The formatter dynamically configures Prettier based on the language of the code being formatted:

```typescript
// lib/code-formatter.ts
import * as prettier from 'prettier';
import parserBabel from 'prettier/parser-babel';
import parserPostcss from 'prettier/parser-postcss';
import parserHtml from 'prettier/parser-html';

interface FormatOptions {
  language: string;
  content: string;
}

export async function formatCode({ language, content }: FormatOptions): Promise<string> {
  if (!content.trim()) return content;

  // Select parser and plugins based on language
  const config = getParserConfig(language);

  try {
    return await prettier.format(content, {
      parser: config.parser,
      plugins: config.plugins,
      printWidth: 80,
      tabWidth: 2,
      useTabs: false,
      semi: true,
      singleQuote: true,
      // Additional configuration options...
    });
  } catch (error) {
    console.warn(`Formatting error for ${language}:`, error);
    return content; // Return original content if formatting fails
  }
}

function getParserConfig(language: string): { parser: string; plugins: any[] } {
  switch (language.toLowerCase()) {
    case 'javascript':
    case 'typescript':
    case 'jsx':
    case 'tsx':
      return { parser: 'babel', plugins: [parserBabel] };
    case 'css':
      return { parser: 'css', plugins: [parserPostcss] };
    case 'html':
      return { parser: 'html', plugins: [parserHtml] };
    // Additional language support...
    default:
      return { parser: 'babel', plugins: [parserBabel] };
  }
}
```

### State Management Strategy

The application uses a combination of React context and custom hooks for state management, avoiding external state libraries.

#### Context Hierarchy

The context hierarchy is designed to minimize re-renders and optimize performance:

```typescript
// app/layout.tsx (simplified)
const RootLayout = ({ children }: { children: React.ReactNode }) => {
  return (
    <html lang="en">
      <body>
        {/* Providers are ordered by dependency and update frequency */}
        <ThemeProvider>
          <SettingsProvider>
            <OllamaProvider>
              <ChatProvider>
                {children}
              </ChatProvider>
            </OllamaProvider>
          </SettingsProvider>
        </ThemeProvider>
      </body>
    </html>
  );
};
```

#### State Update Batching

The chat state management uses batching to optimize updates during streaming responses:

```typescript
// contexts/chat-context.tsx
const ChatProvider = ({ children }: { children: React.ReactNode }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isStreaming, setIsStreaming] = useState(false);
  const pendingUpdates = useRef<string[]>([]);
  const updateTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Handle streaming updates with batching
  const processStreamingUpdate = useCallback((chunk: string) => {
    pendingUpdates.current.push(chunk);

    // Batch updates to reduce render cycles
    if (!updateTimeoutRef.current) {
      updateTimeoutRef.current = setTimeout(() => {
        setMessages(prev => {
          const lastMessage = prev[prev.length - 1];
          const updatedContent = lastMessage.content + pendingUpdates.current.join('');
          pendingUpdates.current = [];

          return [
            ...prev.slice(0, -1),
            { ...lastMessage, content: updatedContent }
          ];
        });

        updateTimeoutRef.current = null;
      }, STREAMING_UPDATE_INTERVAL_MS);
    }
  }, []);

  // Other implementation details...

  return (
    <ChatContext.Provider value={{
      messages,
      isStreaming,
      sendMessage,
      clearChat,
      // Additional values...
    }}>
      {children}
    </ChatContext.Provider>
  );
};
```

### Monaco Editor Integration

The Monaco editor integration includes advanced features like custom themes, intelligent language detection, and optimized loading.

#### Custom Theme Implementation

```typescript
// styles/monaco-theme.ts
import { editor } from 'monaco-editor';

export const darkTheme: editor.IStandaloneThemeData = {
  base: 'vs-dark',
  inherit: true,
  rules: [
    { token: 'comment', foreground: '6A9955' },
    { token: 'keyword', foreground: '569CD6' },
    { token: 'string', foreground: 'CE9178' },
    // Additional token rules...
  ],
  colors: {
    'editor.background': '#1e1e1e',
    'editor.foreground': '#d4d4d4',
    'editorCursor.foreground': '#00cbdd',
    'editor.lineHighlightBackground': '#2a2d2e',
    // Additional color settings...
  },
};

export const lightTheme: editor.IStandaloneThemeData = {
  base: 'vs',
  inherit: true,
  rules: [
    { token: 'comment', foreground: '008000' },
    { token: 'keyword', foreground: '0000ff' },
    { token: 'string', foreground: 'a31515' },
    // Additional token rules...
  ],
  colors: {
    'editor.background': '#ffffff',
    'editor.foreground': '#000000',
    'editorCursor.foreground': '#007ACC',
    'editor.lineHighlightBackground': '#f3f3f3',
    // Additional color settings...
  },
};
```

#### Advanced Editor Configuration

```typescript
// components/core/editable-code-block.tsx
import { useTheme } from 'next-themes';
import Editor, { loader } from '@monaco-editor/react';

// Configure Monaco loader
loader.config({
  paths: {
    vs: 'https://cdn.jsdelivr.net/npm/monaco-editor@0.36.1/min/vs',
  },
});

const EditableCodeBlock = ({
  language,
  code,
  onChange
}: EditableCodeBlockProps) => {
  const { theme } = useTheme();
  const editorRef = useRef<editor.IStandaloneCodeEditor | null>(null);

  // Handle editor initialization
  const handleEditorDidMount = (editor: editor.IStandaloneCodeEditor) => {
    editorRef.current = editor;

    // Set up event listeners and additional configurations
    editor.onDidChangeModelContent(() => {
      onChange(editor.getValue());
    });

    // Additional editor configuration
    editor.updateOptions({
      minimap: { enabled: false },
      scrollBeyondLastLine: false,
      folding: true,
      lineNumbers: 'on',
      renderLineHighlight: 'all',
      automaticLayout: true,
      wordWrap: 'on',
      tabSize: 2,
    });
  };

  return (
    <div className="border rounded-md overflow-hidden h-[300px]">
      <Editor
        height="100%"
        language={language}
        value={code}
        theme={theme === 'dark' ? 'vs-dark' : 'vs-light'}
        onMount={handleEditorDidMount}
        options={{
          readOnly: false,
          automaticLayout: true,
          scrollBeyondLastLine: false,
          minimap: { enabled: false },
          // Additional options...
        }}
      />
    </div>
  );
};
```

### Performance Optimizations

The application includes several performance optimizations to ensure a smooth user experience even with large chat histories and complex code.

#### Lazy Loading

Components that are not immediately needed are lazily loaded to improve initial page load time:

```typescript
// app/page.tsx
import dynamic from 'next/dynamic';

// Lazily load heavy components
const CodePreview = dynamic(() => import('@/components/core/code-preview'), {
  loading: () => <div className="h-80 w-full animate-pulse bg-muted rounded-md" />,
  ssr: false, // Disable server-side rendering for this component
});

const ModelSettings = dynamic(() => import('@/components/settings/model-settings'), {
  loading: () => <div className="h-40 w-full animate-pulse bg-muted rounded-md" />,
});

// Use components in JSX
```

#### Request Debouncing

API requests are debounced to prevent excessive calls:

```typescript
// hooks/use-debounce.ts
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

// Usage in components
const debouncedSearchTerm = useDebounce(searchTerm, 500);

useEffect(() => {
  if (debouncedSearchTerm) {
    performSearch(debouncedSearchTerm);
  }
}, [debouncedSearchTerm]);
```

#### Web Workers for CPU-Intensive Tasks

Complex operations like code formatting are offloaded to web workers:

```typescript
// lib/worker-formatter.ts
import { wrap } from 'comlink';

// Define worker interface
interface FormatterWorker {
  formatCode(options: FormatOptions): Promise<string>;
}

// Create worker instance
let formatterWorker: FormatterWorker | null = null;

export async function formatCodeInWorker(options: FormatOptions): Promise<string> {
  if (!formatterWorker) {
    // Create worker on demand
    const worker = new Worker(new URL('./formatter.worker.ts', import.meta.url));
    formatterWorker = wrap<FormatterWorker>(worker);
  }

  try {
    return await formatterWorker.formatCode(options);
  } catch (error) {
    console.error('Worker formatting error:', error);
    // Fall back to main thread formatting
    return formatCode(options);
  }
}
```

### Advanced Features

#### Customizable System Prompts

The application supports customizable system prompts with template variables:

```typescript
// lib/prompts.ts
export const DEFAULT_SYSTEM_PROMPT =
  "You are a helpful programming assistant who helps users write code. " +
  "Your responses should be clear, concise, and accurate. " +
  "When providing code, favor practical examples and explain important concepts.";

export function createSystemPrompt(template: string, variables: Record<string, string>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (_, key) => {
    return variables[key] || `{{${key}}}`;
  });
}

// Usage
const customPrompt = createSystemPrompt(
  "You are an assistant specializing in {{language}} development. Focus on {{focus}}.",
  {
    language: "TypeScript",
    focus: "React components"
  }
);
```

#### Context Window Management

The application intelligently manages the context window size for large chat histories:

```typescript
// lib/context-manager.ts
export function prepareMessagesForAPI(
  messages: Message[],
  maxTokens: number = 4000
): Message[] {
  // Clone messages to avoid mutation
  const preparedMessages = [...messages];

  // Always include system prompt
  const systemPrompt = preparedMessages.find(m => m.role === 'system');

  // Estimate token count (simplified)
  const estimateTokens = (text: string) => Math.ceil(text.length / 4);

  // Calculate current token usage
  let totalTokens = preparedMessages.reduce(
    (sum, msg) => sum + estimateTokens(msg.content),
    0
  );

  // If we're over the limit, truncate older messages
  if (totalTokens > maxTokens && preparedMessages.length > 2) {
    // Always keep system prompt and most recent user message
    const keptMessages = preparedMessages.filter(
      m => m.role === 'system' || m === preparedMessages[preparedMessages.length - 1]
    );

    // Calculate tokens for kept messages
    const keptTokens = keptMessages.reduce(
      (sum, msg) => sum + estimateTokens(msg.content),
      0
    );

    const availableTokens = maxTokens - keptTokens;
    const remainingMessages = preparedMessages.filter(
      m => m.role !== 'system' && m !== preparedMessages[preparedMessages.length - 1]
    );

    // Add as many previous messages as possible
    const truncatedHistory: Message[] = [];

    // Start from most recent
    for (let i = remainingMessages.length - 1; i >= 0; i--) {
      const msg = remainingMessages[i];
      const msgTokens = estimateTokens(msg.content);

      if (msgTokens <= availableTokens) {
        truncatedHistory.unshift(msg);
        availableTokens -= msgTokens;
      } else {
        // If message is too large, create a summary message
        const summaryMsg: Message = {
          role: 'system',
          content: 'Earlier conversation has been summarized due to length constraints.',
          id: 'summary-' + Date.now()
        };

        truncatedHistory.unshift(summaryMsg);
        break;
      }
    }

    // Reconstruct the optimized message array
    return [
      ...(systemPrompt ? [systemPrompt] : []),
      ...truncatedHistory,
      preparedMessages[preparedMessages.length - 1]
    ];
  }

  return preparedMessages;
}
```

These implementation details provide a comprehensive overview of the technical decisions and advanced features incorporated into the Ollama UI Builder, demonstrating the sophisticated approaches used to create a high-performance, user-friendly application.