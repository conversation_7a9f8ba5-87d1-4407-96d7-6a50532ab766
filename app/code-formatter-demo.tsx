"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import EditableCodeBlock from "@/components/editable-code-block"
import { formatCode } from "@/lib/code-formatter"
import { useToast } from "@/hooks/use-toast"

// Sample code snippets for different languages
const sampleSnippets = {
  javascript: `// Unformatted JavaScript code
function calculateSum(a,b) {
return a+b;
}

const numbers = [1,2,3,4,5];
const doubled = numbers.map((num)=>{
  return num*2;
});

const person = {
  name:"<PERSON>",
  age:30,
  occupation:"<PERSON><PERSON><PERSON>",
  contacts:{
    email:"<EMAIL>",
    phone:"************"
  }
};

console.log(calculateSum(5,10));`,

  typescript: `// Unformatted TypeScript code
interface Person {
name: string;
age: number;
occupation?: string;
contacts?: {
    email: string;
    phone?: string;
}
}

class Employee implements Person {
  constructor(public name:string,public age:number,private salary:number,public occupation:string="Developer") {}

  public getSalary():number {
    return this.salary;
  }

  public getDetails():{name:string,age:number,occupation:string} {
    return {name:this.name,age:this.age,occupation:this.occupation};
  }
}

const emp = new Employee("Jane Smith",28,75000);
console.log(emp.getDetails());`,

  html: `<!-- Unformatted HTML code -->
<!DOCTYPE html>
<html>
<head>
<title>Sample Page</title>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
<header><h1>Welcome to My Page</h1><nav><ul><li><a href="#">Home</a></li><li><a href="#">About</a></li><li><a href="#">Contact</a></li></ul></nav></header>
<main>
<section class="hero">
<h2>This is a Code Formatter Demo</h2>
<p>Try formatting different code samples to see how it works!</p>
</section>
<section class="features"><div class="feature"><h3>Feature 1</h3><p>Description of feature 1</p></div><div class="feature"><h3>Feature 2</h3><p>Description of feature 2</p></div><div class="feature"><h3>Feature 3</h3><p>Description of feature 3</p></div></section>
</main>
<footer><p>&copy; 2023 Code Formatter Demo</p></footer>
</body>
</html>`,

  css: `/* Unformatted CSS code */
.container{max-width:1200px;margin:0 auto;padding:0 15px;}
header{background-color:#f8f9fa;padding:20px 0;box-shadow:0 2px 4px rgba(0,0,0,0.1);}
nav ul{display:flex;list-style:none;margin:0;padding:0;}
nav li{margin-right:20px;}
nav a{text-decoration:none;color:#333;font-weight:bold;transition:color 0.3s ease;}
nav a:hover{color:#007bff;}
.hero{text-align:center;padding:50px 0;background:linear-gradient(135deg,#f5f7fa 0%,#c3cfe2 100%);}
.features{display:grid;grid-template-columns:repeat(auto-fit,minmax(300px,1fr));gap:30px;padding:50px 0;}
.feature{background-color:white;border-radius:8px;padding:20px;box-shadow:0 4px 6px rgba(0,0,0,0.1);transition:transform 0.3s ease;}
.feature:hover{transform:translateY(-5px);}
@media (max-width:768px){.features{grid-template-columns:1fr;}nav ul{flex-direction:column;}nav li{margin-right:0;margin-bottom:10px;}}`,

  json: `{
"name":"Code Formatter Demo",
"version":"1.0.0",
"description":"A tool to format code in various languages",
"main":"index.js",
"scripts":{
  "dev":"next dev",
  "build":"next build",
  "start":"next start",
  "lint":"next lint"
},
"dependencies":{
  "next":"^13.4.19",
  "react":"^18.2.0",
  "react-dom":"^18.2.0",
  "prettier":"^3.0.3"
},
"keywords":["code","formatter","prettier","demo"],
"author":"Your Name",
"license":"MIT"
}`
}

export default function CodeFormatterDemo() {
  const [language, setLanguage] = useState<string>("javascript")
  const [code, setCode] = useState<string>(sampleSnippets.javascript)
  const [formattedCode, setFormattedCode] = useState<string>("")
  const { toast } = useToast()

  const handleLanguageChange = (value: string) => {
    setLanguage(value)
    setCode(sampleSnippets[value as keyof typeof sampleSnippets])
    setFormattedCode("")
  }

  const handleFormatCode = async () => {
    try {
      const formatted = await formatCode(code, language)
      setFormattedCode(formatted)
      toast({
        title: "Code formatted successfully",
        description: "Your code has been formatted using Prettier",
      })
    } catch (error) {
      toast({
        title: "Format error",
        description: `Error formatting code: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      })
    }
  }

  return (
    <main className="container mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8 text-center">Code Formatter Demo</h1>
      
      <div className="flex flex-col md:flex-row gap-6">
        <Card className="flex-1">
          <CardHeader>
            <CardTitle>Input Code</CardTitle>
            <CardDescription>
              Select a language and edit the unformatted code
            </CardDescription>
            <div className="flex items-center gap-4 mt-4">
              <Select value={language} onValueChange={handleLanguageChange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="typescript">TypeScript</SelectItem>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="css">CSS</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                </SelectContent>
              </Select>
              
              <Button onClick={handleFormatCode}>
                Format Code
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <EditableCodeBlock 
              language={language} 
              code={code} 
              onCodeChange={setCode}
            />
          </CardContent>
        </Card>
        
        {formattedCode && (
          <Card className="flex-1">
            <CardHeader>
              <CardTitle>Formatted Code</CardTitle>
              <CardDescription>
                The result after formatting with Prettier
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EditableCodeBlock 
                language={language} 
                code={formattedCode} 
                readOnly={true}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </main>
  )
} 