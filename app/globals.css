@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --radius: 1rem;

    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 5.9% 10%;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;

    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 0 0% 98%;
    --sidebar-primary-foreground: 240 5.9% 10%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Purple Theme */
  .theme-purple {
    --primary: 270 95% 75%;
    --primary-foreground: 0 0% 100%;
    --ring: 270 95% 75%;
    --background: 270 50% 5%;
    --foreground: 270 20% 96%;
    --card: 270 60% 7%;
    --card-foreground: 270 20% 96%;
    --popover: 270 60% 7%;
    --popover-foreground: 270 20% 96%;
    --secondary: 270 30% 15%;
    --secondary-foreground: 270 20% 96%;
    --muted: 270 30% 15%;
    --muted-foreground: 270 20% 70%;
    --accent: 270 30% 15%;
    --accent-foreground: 270 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 270 20% 96%;
    --border: 270 30% 15%;
    --input: 270 30% 15%;
  }

  /* Indigo Theme */
  .theme-indigo {
    --primary: 226 70% 55.5%;
    --primary-foreground: 0 0% 100%;
    --ring: 226 70% 55.5%;
    --background: 226 50% 5%;
    --foreground: 226 20% 96%;
    --card: 226 60% 7%;
    --card-foreground: 226 20% 96%;
    --popover: 226 60% 7%;
    --popover-foreground: 226 20% 96%;
    --secondary: 226 30% 15%;
    --secondary-foreground: 226 20% 96%;
    --muted: 226 30% 15%;
    --muted-foreground: 226 20% 70%;
    --accent: 226 30% 15%;
    --accent-foreground: 226 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 226 20% 96%;
    --border: 226 30% 15%;
    --input: 226 30% 15%;
  }

  /* Rose Theme */
  .theme-rose {
    --primary: 346 77% 49.8%;
    --primary-foreground: 0 0% 100%;
    --ring: 346 77% 49.8%;
    --background: 346 50% 5%;
    --foreground: 346 20% 96%;
    --card: 346 60% 7%;
    --card-foreground: 346 20% 96%;
    --popover: 346 60% 7%;
    --popover-foreground: 346 20% 96%;
    --secondary: 346 30% 15%;
    --secondary-foreground: 346 20% 96%;
    --muted: 346 30% 15%;
    --muted-foreground: 346 20% 70%;
    --accent: 346 30% 15%;
    --accent-foreground: 346 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 346 20% 96%;
    --border: 346 30% 15%;
    --input: 346 30% 15%;
  }

  /* Green Theme */
  .theme-green {
    --primary: 142 70.6% 45.3%;
    --primary-foreground: 0 0% 100%;
    --ring: 142 70.6% 45.3%;
    --background: 142 50% 5%;
    --foreground: 142 20% 96%;
    --card: 142 60% 7%;
    --card-foreground: 142 20% 96%;
    --popover: 142 60% 7%;
    --popover-foreground: 142 20% 96%;
    --secondary: 142 30% 15%;
    --secondary-foreground: 142 20% 96%;
    --muted: 142 30% 15%;
    --muted-foreground: 142 20% 70%;
    --accent: 142 30% 15%;
    --accent-foreground: 142 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 142 20% 96%;
    --border: 142 30% 15%;
    --input: 142 30% 15%;
  }

  /* Orange Theme */
  .theme-orange {
    --primary: 24.6 95% 53.1%;
    --primary-foreground: 0 0% 100%;
    --ring: 24.6 95% 53.1%;
    --background: 24.6 50% 5%;
    --foreground: 24.6 20% 96%;
    --card: 24.6 60% 7%;
    --card-foreground: 24.6 20% 96%;
    --popover: 24.6 60% 7%;
    --popover-foreground: 24.6 20% 96%;
    --secondary: 24.6 30% 15%;
    --secondary-foreground: 24.6 20% 96%;
    --muted: 24.6 30% 15%;
    --muted-foreground: 24.6 20% 70%;
    --accent: 24.6 30% 15%;
    --accent-foreground: 24.6 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 24.6 20% 96%;
    --border: 24.6 30% 15%;
    --input: 24.6 30% 15%;
  }

  /* Cyan Theme */
  .theme-cyan {
    --primary: 189 94% 43%;
    --primary-foreground: 0 0% 100%;
    --ring: 189 94% 43%;
    --background: 189 50% 5%;
    --foreground: 189 20% 96%;
    --card: 189 60% 7%;
    --card-foreground: 189 20% 96%;
    --popover: 189 60% 7%;
    --popover-foreground: 189 20% 96%;
    --secondary: 189 30% 15%;
    --secondary-foreground: 189 20% 96%;
    --muted: 189 30% 15%;
    --muted-foreground: 189 20% 70%;
    --accent: 189 30% 15%;
    --accent-foreground: 189 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 189 20% 96%;
    --border: 189 30% 15%;
    --input: 189 30% 15%;
  }

  /* Pink Theme */
  .theme-pink {
    --primary: 330 81% 60%;
    --primary-foreground: 0 0% 100%;
    --ring: 330 81% 60%;
    --background: 330 50% 5%;
    --foreground: 330 20% 96%;
    --card: 330 60% 7%;
    --card-foreground: 330 20% 96%;
    --popover: 330 60% 7%;
    --popover-foreground: 330 20% 96%;
    --secondary: 330 30% 15%;
    --secondary-foreground: 330 20% 96%;
    --muted: 330 30% 15%;
    --muted-foreground: 330 20% 70%;
    --accent: 330 30% 15%;
    --accent-foreground: 330 20% 96%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 330 20% 96%;
    --border: 330 30% 15%;
    --input: 330 30% 15%;
  }

  /* Slate Theme (Default) */
  .theme-slate {
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 0 0% 100%;
    --ring: 217.2 91.2% 59.8%;
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 6.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 6.9%;
    --popover-foreground: 210 40% 98%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

/* Glass morphism effect */
.glass-morphism {
  background: hsl(var(--background) / 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid hsl(var(--border) / 0.2);
}

/* Gradient text */
.gradient-text {
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: linear-gradient(to right, hsl(var(--primary)), hsl(var(--primary-foreground)));
}

/* Animated gradient border */
.gradient-border {
  position: relative;
  border-radius: var(--radius);
}

.gradient-border::before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  border-radius: calc(var(--radius) + 1px);
  background: linear-gradient(45deg, hsl(var(--primary)), hsl(var(--accent)), hsl(var(--primary)));
  background-size: 200% 200%;
  animation: gradient-animation 3s ease infinite;
  z-index: -1;
}

@keyframes gradient-animation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Floating animation */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* Pulse animation */
.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

