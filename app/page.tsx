"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs"
import { SettingsDialog } from "@/components/settings-dialog"
import { createOllamaAPI } from "@/lib/ollama-api"
import { useToast } from "@/hooks/use-toast"
import { Loader2, Code, MessageSquare, Menu, HelpCircle, FileType, Settings, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import dynamic from 'next/dynamic'
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import ConnectionTroubleshooter from "@/components/connection-troubleshooter"
import OnboardingDialog from "@/components/onboarding-dialog"
import DeployedConnectionGuide from "@/components/deployed-connection-guide"
import { useLocalStorage } from "@/hooks/use-local-storage"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Toolt<PERSON><PERSON>rigger } from "@/components/ui/tooltip"
import Link from "next/link"
import { ThemeToggle } from "@/components/theme-toggle"
import { UserProfile } from "@/components/user-profile"
import { useAuth } from "@/contexts/auth-context"
import { LoginForm } from "@/components/login-form"
import { SignupForm } from "@/components/signup-form"
import { Dialog, DialogContent } from "@/components/ui/dialog"

// Dynamically import components that use Monaco Editor with SSR disabled
const EditorInterface = dynamic(() => import("@/components/editor-interface"), { ssr: false });
const EnhancedChatInterface = dynamic(() => import("@/components/enhanced-chat-interface"), { ssr: false });

export default function Home() {
  const [activeTab, setActiveTab] = useState("chat")
  const [isLoading, setIsLoading] = useState(true)
  const [models, setModels] = useState<string[]>([])
  const [selectedModel, setSelectedModel] = useState("")
  const [temperature, setTemperature] = useState(0.7)
  const [files, setFiles] = useState<any[]>([])
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [authMode, setAuthMode] = useState<"login" | "signup">("login")
  const { user, isAuthenticated, isLoading: authLoading } = useAuth()
  const [settings, setSettings] = useState({
    connectionMode: "auto" as "local" | "auto" | "custom",
    apiUrl: "auto",
    apiKey: "",
    darkMode: true,
    fontSize: 14,
    minimap: false,
    wordWrap: true,
    tabSize: 2,
    showLineNumbers: true,
    showIndentGuides: true,
    showInvisibles: false,
    autoClosingBrackets: true,
    autoIndent: true,
    smoothScrolling: true,
    quickSuggestions: true,
    autoSave: true,
    formatOnSave: true,
  })
  const { toast } = useToast()
  const { setTheme } = useTheme()
  const [connectionError, setConnectionError] = useState(false)
  const [showOnboarding, setShowOnboarding] = useState(false)
  const [hasCompletedOnboarding, setHasCompletedOnboarding] = useLocalStorage("ollama-ui-onboarding-completed", false)
  const [isDeployed, setIsDeployed] = useState(false)
  const [showDeployedGuide, setShowDeployedGuide] = useState(false)
  const [hasCompletedDeployedGuide, setHasCompletedDeployedGuide] = useLocalStorage("ollama-ui-deployed-guide-completed", false)

  // Prevent hydration issues by only rendering once mounted on client
  useEffect(() => {
    setMounted(true)

    // Check if we're in a deployed environment (not localhost)
    if (typeof window !== 'undefined') {
      const hostname = window.location.hostname
      const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1'
      setIsDeployed(!isLocalhost)
    }
  }, [])

  // Load settings from localStorage on component mount
  useEffect(() => {
    try {
      const savedSettings = localStorage.getItem("ollama-ui-settings")
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings)
        setSettings((prevSettings) => ({ ...prevSettings, ...parsedSettings }))
      }
    } catch (error) {
      console.error("Error loading settings:", error)
    }
  }, [])

  // Apply dark mode setting
  useEffect(() => {
    if (settings.darkMode) {
      setTheme("dark")
    } else {
      setTheme("light")
    }
  }, [settings.darkMode, setTheme])

  // Initialize Ollama API and fetch models
  useEffect(() => {
    const initializeAPI = async () => {
      try {
        setIsLoading(true)
        setConnectionError(false)
        const ollamaApi = createOllamaAPI(settings.connectionMode, settings.apiUrl, settings.apiKey)
        const response = await ollamaApi.listModels()

        if (response && response.models) {
          const modelNames = response.models.map((model: any) => model.name)
          setModels(modelNames)

          if (modelNames.length > 0) {
            setSelectedModel(modelNames[0])
          }
        }
      } catch (error) {
        console.error("Error initializing API:", error)
        setConnectionError(true)
        toast({
          title: "Connection Error",
          description: `Failed to connect to Ollama API at ${
            settings.connectionMode === "auto"
              ? window.location.hostname + ":11434"
              : settings.apiUrl
          }. Please check your connection settings and ensure Ollama is running.`,
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    // Only attempt to initialize if we're mounted on the client
    if (mounted) {
      initializeAPI()
    }
  }, [settings.connectionMode, settings.apiUrl, settings.apiKey, toast, mounted])

  // Show onboarding dialog for new users
  useEffect(() => {
    if (mounted && !hasCompletedOnboarding) {
      setShowOnboarding(true)
    }
  }, [mounted, hasCompletedOnboarding])

  // Show deployed connection guide for users on deployed version
  useEffect(() => {
    if (mounted && isDeployed && !hasCompletedDeployedGuide) {
      setShowDeployedGuide(true)
    }
  }, [mounted, isDeployed, hasCompletedDeployedGuide])

  const handleSettingsChange = (newSettings: any) => {
    setSettings((prevSettings) => {
      const updatedSettings = { ...prevSettings, ...newSettings }

      // Save to localStorage
      try {
        localStorage.setItem("ollama-ui-settings", JSON.stringify(updatedSettings))
      } catch (error) {
        console.error("Error saving settings:", error)
      }

      return updatedSettings
    })
  }

  const handleCreateFile = (file: any) => {
    setFiles((prev) => [...prev, file])
  }

  const handleOnboardingComplete = (open: boolean) => {
    setShowOnboarding(open)
    if (!open) {
      setHasCompletedOnboarding(true)
    }
  }

  // Only render the full UI once mounted on client to prevent hydration issues
  if (!mounted) {
    return (
      <div className="flex flex-col min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  // Show login/signup if not authenticated
  if (!authLoading && !isAuthenticated) {
    return (
      <div className="flex flex-col min-h-screen items-center justify-center bg-gradient-to-b from-background to-muted/20">
        <div className="max-w-md w-full p-6 rounded-xl shadow-xl bg-card border">
          <div className="mb-6 text-center">
            <div className="flex justify-center mb-4">
              <Code className="h-10 w-10 text-primary" />
            </div>
            <h1 className="text-2xl font-bold">Ollama UI Builder</h1>
            <p className="text-muted-foreground mt-2">Sign in to access your Ollama UI</p>
          </div>

          <Tabs value={authMode} onValueChange={(value) => setAuthMode(value as "login" | "signup")}>
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="login">Login</TabsTrigger>
              <TabsTrigger value="signup">Sign Up</TabsTrigger>
            </TabsList>

            <TabsContent value="login">
              <LoginForm onSwitchToSignup={() => setAuthMode("signup")} />
            </TabsContent>

            <TabsContent value="signup">
              <SignupForm onSwitchToLogin={() => setAuthMode("login")} />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col min-h-screen">
      {/* Onboarding Dialog */}
      <OnboardingDialog open={showOnboarding} onOpenChange={handleOnboardingComplete} />

      {/* Deployed Connection Guide */}
      {showDeployedGuide && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="relative w-full max-w-3xl">
            <Button
              variant="ghost"
              size="icon"
              className="absolute right-2 top-2 z-50 bg-background rounded-full"
              onClick={() => {
                setShowDeployedGuide(false)
                setHasCompletedDeployedGuide(true)
              }}
              aria-label="Close"
            >
              <X className="h-4 w-4" />
            </Button>
            <DeployedConnectionGuide
              onSettingsChange={handleSettingsChange}
              onDismiss={() => {
                setShowDeployedGuide(false)
                setHasCompletedDeployedGuide(true)
              }}
            />
          </div>
        </div>
      )}

      <header className="border-b sticky top-0 z-10 bg-background">
        <div className="container flex h-16 items-center justify-between px-4 md:px-6">
          <div className="flex items-center gap-2">
            <Sheet open={isMobileMenuOpen} onOpenChange={setIsMobileMenuOpen}>
              <SheetTrigger asChild className="md:hidden">
                <Button variant="ghost" size="icon" className="mr-2">
                  <Menu className="h-5 w-5" />
                  <span className="sr-only">Toggle menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[240px] sm:w-[300px]">
                <div className="py-4">
                  <h2 className="text-lg font-semibold mb-4">Ollama UI Builder</h2>
                  <div className="space-y-2">
                    <Button
                      variant={activeTab === "chat" ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab("chat")
                        setIsMobileMenuOpen(false)
                      }}
                    >
                      <MessageSquare className="mr-2 h-4 w-4" />
                      Chat
                    </Button>
                    <Button
                      variant={activeTab === "editor" ? "default" : "ghost"}
                      className="w-full justify-start"
                      onClick={() => {
                        setActiveTab("editor")
                        setIsMobileMenuOpen(false)
                      }}
                    >
                      <Code className="mr-2 h-4 w-4" />
                      Editor
                    </Button>
                    <Link href="/code-formatter-demo" passHref>
                      <Button
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => {
                          setIsMobileMenuOpen(false)
                        }}
                      >
                        <FileType className="mr-2 h-4 w-4" />
                        Code Formatter
                      </Button>
                    </Link>
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            <Code className="h-6 w-6" />
            <h1 className="text-xl font-bold">Ollama UI Builder</h1>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            <div className={cn("hidden md:block", isLoading ? "w-[180px]" : "")}>
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Loading models...</span>
                </div>
              ) : (
                <select
                  value={selectedModel}
                  onChange={(e) => setSelectedModel(e.target.value)}
                  className="text-sm border rounded px-2 py-1 bg-background"
                >
                  {models.length === 0 ? (
                    <option value="">No models available</option>
                  ) : (
                    models.map((model) => (
                      <option key={model} value={model}>
                        {model}
                      </option>
                    ))
                  )}
                </select>
              )}
            </div>

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button asChild variant="ghost" size="icon" className="mr-2">
                    <Link href="/code-formatter-demo">
                      <FileType className="h-5 w-5" />
                      <span className="sr-only">Code Formatter</span>
                    </Link>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Code Formatter</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Theme Toggle */}
            <ThemeToggle />

            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={() => setShowOnboarding(true)}
                  >
                    <HelpCircle className="h-4 w-4" />
                    <span className="sr-only">Help</span>
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  <p>Help & Onboarding Guide</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>

            {/* Settings Dialog */}
            <SettingsDialog
              settings={settings}
              onSettingsChange={handleSettingsChange}
              onShowDeployedGuide={() => setShowDeployedGuide(true)}
              isDeployed={isDeployed}
            />

            {/* User Profile */}
            <UserProfile />
          </div>
        </div>
      </header>

      <main className="flex-1 container py-4 px-4 md:px-6">
        {connectionError ? (
          <div className="max-w-3xl mx-auto my-8">
            <h2 className="text-2xl font-bold mb-6">Connection Error</h2>
            <p className="mb-6">
              Unable to connect to Ollama. Please check your connection settings and make sure Ollama is running.
            </p>
            <ConnectionTroubleshooter
              apiUrl={settings.apiUrl}
              connectionMode={settings.connectionMode}
              apiKey={settings.apiKey}
              onSettingsChange={handleSettingsChange}
              onShowDeployedGuide={() => setShowDeployedGuide(true)}
              isDeployed={isDeployed}
            />
            <div className="mt-8 text-center">
              <Button
                onClick={() => window.location.reload()}
                className="mx-auto"
              >
                Retry Connection
              </Button>
            </div>
          </div>
        ) : (
          <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
            <div className="flex justify-between items-center mb-4">
              <TabsList className="hidden md:flex">
                <TabsTrigger value="chat" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Chat
                </TabsTrigger>
                <TabsTrigger value="editor" className="flex items-center gap-2">
                  <Code className="h-4 w-4" />
                  Editor
                </TabsTrigger>
              </TabsList>

              <div className="md:hidden flex-1">
                <h2 className="text-lg font-medium">{activeTab === "chat" ? "Chat" : "Editor"}</h2>
              </div>

              <div className="flex items-center md:hidden">
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <select
                    value={selectedModel}
                    onChange={(e) => setSelectedModel(e.target.value)}
                    className="text-sm border rounded px-2 py-1 bg-background max-w-[120px] truncate"
                  >
                    {models.length === 0 ? (
                      <option value="">No models</option>
                    ) : (
                      models.map((model) => (
                        <option key={model} value={model}>
                          {model}
                        </option>
                      ))
                    )}
                  </select>
                )}
              </div>
            </div>

            <TabsContent value="chat" className="flex-1 h-[calc(100vh-180px)] md:h-[calc(100vh-160px)]">
              {isLoading ? (
                <div className="h-full flex items-center justify-center">
                  <Loader2 className="h-8 w-8 animate-spin" />
                </div>
              ) : (
                <EnhancedChatInterface
                  selectedModel={selectedModel}
                  onCreateFile={handleCreateFile}
                  temperature={temperature}
                />
              )}
            </TabsContent>

            <TabsContent value="editor" className="flex-1 h-[calc(100vh-180px)] md:h-[calc(100vh-160px)]">
              <EditorInterface
                files={files}
                setFiles={setFiles}
                settings={settings}
                onSettingsChange={handleSettingsChange}
              />
            </TabsContent>
          </Tabs>
        )}
      </main>
    </div>
  )
}

