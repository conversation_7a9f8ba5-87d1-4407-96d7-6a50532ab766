"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"

export default function TestConnection() {
  const [status, setStatus] = useState<string>("Not tested")
  const [result, setResult] = useState<string>("")

  const testConnection = async () => {
    setStatus("Testing...")
    try {
      const response = await fetch("http://localhost:11434/api/tags")
      const text = await response.text()
      setResult(text)

      if (response.ok) {
        setStatus("Connected successfully!")
      } else {
        setStatus(`Error: ${response.status} ${response.statusText}`)
      }
    } catch (error) {
      setStatus(`Connection failed: ${error instanceof Error ? error.message : String(error)}`)
      setResult(JSON.stringify(error, null, 2))
    }
  }

  return (
    <div className="p-8 max-w-2xl mx-auto">
      <h1 className="text-2xl font-bold mb-4">Ollama API Connection Test</h1>

      <div className="mb-4">
        <Button onClick={testConnection}>Test Connection</Button>
      </div>

      <div className="mb-4 p-4 border rounded">
        <h2 className="font-semibold mb-2">Status:</h2>
        <div
          className={
            status.includes("Connected")
              ? "text-green-500"
              : status.includes("Testing")
                ? "text-yellow-500"
                : "text-red-500"
          }
        >
          {status}
        </div>
      </div>

      {result && (
        <div className="p-4 border rounded bg-muted">
          <h2 className="font-semibold mb-2">Response:</h2>
          <pre className="whitespace-pre-wrap overflow-auto max-h-96">{result}</pre>
        </div>
      )}

      <div className="mt-8 p-4 border rounded bg-muted/50">
        <h2 className="font-semibold mb-2">Troubleshooting Tips:</h2>
        <ul className="list-disc pl-5 space-y-2">
          <li>
            Make sure Ollama is running with <code className="bg-muted p-1 rounded">ollama serve</code>
          </li>
          <li>
            Check that you have at least one model pulled with <code className="bg-muted p-1 rounded">ollama list</code>
          </li>
          <li>
            Try using <code className="bg-muted p-1 rounded">127.0.0.1</code> instead of{" "}
            <code className="bg-muted p-1 rounded">localhost</code>
          </li>
          <li>Check for firewall or antivirus software blocking connections</li>
          <li>Verify that port 11434 is not being used by another application</li>
        </ul>
      </div>
    </div>
  )
}

