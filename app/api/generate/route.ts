import { type NextRequest, NextResponse } from "next/server"
import { getOllamaAPI } from "@/lib/ollama-api"

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { model, prompt, system, options } = body

    if (!model || !prompt) {
      return NextResponse.json({ error: "Missing required parameters: model and prompt" }, { status: 400 })
    }

    const api = getOllamaAPI()

    try {
      const response = await api.generate({
        model,
        prompt,
        system,
        options,
      })

      return NextResponse.json(response)
    } catch (apiError) {
      console.error("Ollama API error:", apiError)

      // Check if Ollama is running
      const isOllamaRunning = await checkOllamaStatus(api)

      if (!isOllamaRunning) {
        return NextResponse.json(
          { error: "Cannot connect to Ollama. Please ensure Ollama is running on your system." },
          { status: 503 },
        )
      }

      // Check if the model exists
      const modelExists = await checkModelExists(api, model)

      if (!modelExists) {
        return NextResponse.json(
          { error: `Model '${model}' not found. Please pull this model using 'ollama pull ${model}'` },
          { status: 404 },
        )
      }

      return NextResponse.json(
        { error: apiError instanceof Error ? apiError.message : "Error communicating with Ollama API" },
        { status: 500 },
      )
    }
  } catch (error) {
    console.error("Error in generate API:", error)
    return NextResponse.json({ error: error instanceof Error ? error.message : "Unknown error" }, { status: 500 })
  }
}

// Helper function to check if Ollama is running
async function checkOllamaStatus(api: any) {
  try {
    await api.listModels()
    return true
  } catch (error) {
    return false
  }
}

// Helper function to check if a model exists
async function checkModelExists(api: any, modelName: string) {
  try {
    const response = await api.listModels()
    return response.models?.some((model: any) => model.name === modelName) || false
  } catch (error) {
    return false
  }
}

