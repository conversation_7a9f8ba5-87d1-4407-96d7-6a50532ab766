# Installing Docker and Ollama on a Server

This guide provides step-by-step instructions for installing Dock<PERSON> and Ollama on a server environment. These instructions are applicable to most Linux-based servers. The Ollama UI Builder with its advanced agent capabilities can connect to your Ollama server once it's properly set up.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Installing Docker](#installing-docker)
  - [Ubuntu/Debian](#installing-docker-on-ubuntudebian)
  - [CentOS/RHEL](#installing-docker-on-centosrhel)
  - [Verifying Docker Installation](#verifying-docker-installation)
- [Installing Ollama](#installing-ollama)
  - [Server Requirements](#server-requirements)
  - [Installation Steps](#ollama-installation-steps)
  - [Verifying Ollama Installation](#verifying-ollama-installation)
- [Running Ollama with Docker](#running-ollama-with-docker)
- [Advanced Configuration](#advanced-configuration)
- [Troubleshooting](#troubleshooting)

## Prerequisites

Before beginning the installation, ensure your server meets the following requirements:

- A Linux-based server (Ubuntu, Debian, CentOS, or RHEL recommended)
- Minimum 4GB RAM (8GB+ recommended for running larger models)
- At least 10GB of free disk space
- Root or sudo access
- An active internet connection

## Installing Docker

### Installing Docker on Ubuntu/Debian

1. Update your package lists:

```bash
sudo apt update
sudo apt upgrade -y
```

2. Install required dependencies:

```bash
sudo apt install -y apt-transport-https ca-certificates curl software-properties-common gnupg
```

3. Add Docker's official GPG key:

```bash
curl -fsSL https://download.docker.com/linux/ubuntu/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
```

4. Add the Docker repository:

For Ubuntu:
```bash
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/ubuntu $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
```

For Debian:
```bash
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
```

5. Update package lists again:

```bash
sudo apt update
```

6. Install Docker:

```bash
sudo apt install -y docker-ce docker-ce-cli containerd.io
```

7. Start and enable Docker service:

```bash
sudo systemctl start docker
sudo systemctl enable docker
```

8. Add your user to the Docker group to run Docker commands without sudo:

```bash
sudo usermod -aG docker $USER
```

9. Apply the group changes by logging out and back in, or run:

```bash
newgrp docker
```

### Installing Docker on CentOS/RHEL

1. Remove any old versions of Docker (if installed):

```bash
sudo yum remove docker docker-client docker-client-latest docker-common docker-latest docker-latest-logrotate docker-logrotate docker-engine
```

2. Install required packages:

```bash
sudo yum install -y yum-utils
```

3. Add the Docker repository:

```bash
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
```

4. Install Docker:

```bash
sudo yum install -y docker-ce docker-ce-cli containerd.io
```

5. Start and enable Docker service:

```bash
sudo systemctl start docker
sudo systemctl enable docker
```

6. Add your user to the Docker group:

```bash
sudo usermod -aG docker $USER
```

7. Apply the group changes by logging out and back in, or run:

```bash
newgrp docker
```

### Verifying Docker Installation

After installing Docker, verify that it's working correctly:

```bash
docker --version
docker run hello-world
```

You should see the Docker version and a successful test message from the hello-world container.

## Installing Ollama

### Server Requirements

For running Ollama, especially with larger models, ensure your server meets these recommended specifications:

- 8GB+ RAM for running smaller models (7B parameters)
- 16GB+ RAM for medium models (13B parameters)
- 32GB+ RAM for larger models (70B parameters)
- NVIDIA GPU with CUDA support (optional, but recommended for better performance)
- At least 30GB of free disk space for storing models

### Ollama Installation Steps

1. Download and install Ollama:

```bash
curl -fsSL https://ollama.com/install.sh | sh
```

2. Verify the installation:

```bash
ollama --version
```

3. Start the Ollama service:

For systems using systemd:
```bash
sudo systemctl start ollama
sudo systemctl enable ollama
```

If you installed as a non-root user:
```bash
ollama serve
```

### Verifying Ollama Installation

After installation, you can verify Ollama is working correctly by pulling and running a small model:

```bash
ollama run tiny
```

Type a prompt to interact with the model, and press Ctrl+C to exit.

## Running Ollama with Docker

If you prefer to run Ollama in a Docker container:

1. Pull the official Ollama Docker image:

```bash
docker pull ollama/ollama
```

2. Run Ollama in a Docker container:

```bash
docker run -d --name ollama -p 11434:11434 -v ollama:/root/.ollama ollama/ollama
```

3. Interact with Ollama running in the container:

```bash
docker exec -it ollama ollama run tiny
```

## Advanced Configuration

### Enabling Agent Capabilities

The Ollama UI Builder includes advanced agent capabilities that can be fully utilized when your Ollama server is properly configured:

1. Ensure your server has sufficient resources for the reasoning engine:
   - At least 16GB RAM for optimal performance
   - Sufficient CPU cores (4+ recommended)
   - GPU acceleration for faster inference

2. Configure the Ollama UI to connect to your server:
   ```bash
   # In the Ollama UI settings, use the Custom URL option and enter:
   http://YOUR_SERVER_IP:11434
   ```

3. Pull recommended models for the reasoning engine:
   ```bash
   ollama pull llama3
   ollama pull codellama
   ```

4. For optimal agent performance, consider creating a custom model with enhanced reasoning capabilities:
   ```bash
   cat > reasoning-agent.modelfile << EOF
   FROM llama3
   SYSTEM You are an advanced reasoning agent that thinks step-by-step.
   When analyzing requests, first understand the intent, then classify the task,
   plan your approach, and finally provide a clear, helpful response.
   Always consider multiple perspectives and explain your reasoning process.
   EOF

   ollama create reasoning-agent -f reasoning-agent.modelfile
   ```

### Setting Up GPU Support

If your server has an NVIDIA GPU:

1. Install NVIDIA drivers and CUDA toolkit:

```bash
# For Ubuntu/Debian
sudo apt install -y nvidia-driver-XXX cuda-toolkit-XX.X

# For CentOS/RHEL
sudo yum install -y nvidia-driver-XXX cuda-toolkit-XX.X
```
(Replace XXX and XX.X with the appropriate versions)

2. Install the NVIDIA Container Toolkit:

```bash
# For Ubuntu/Debian
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list
sudo apt update && sudo apt install -y nvidia-container-toolkit
sudo systemctl restart docker

# For CentOS/RHEL
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.repo | sudo tee /etc/yum.repos.d/nvidia-docker.repo
sudo yum install -y nvidia-container-toolkit
sudo systemctl restart docker
```

3. Run Ollama with GPU support:

```bash
# For the standalone version
ollama serve

# For Docker
docker run -d --gpus all --name ollama -p 11434:11434 -v ollama:/root/.ollama ollama/ollama
```

### Configuring System Resources

To optimize Ollama performance, consider adjusting these system parameters:

```bash
# Increase max open files
echo "fs.file-max = 65536" | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# Increase max user processes
echo "* soft nproc 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nproc 65536" | sudo tee -a /etc/security/limits.conf
echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
```

## Troubleshooting

### Common Issues and Solutions

#### Docker Issues

1. **Docker daemon not starting**:

   ```bash
   sudo systemctl status docker
   # Look for errors in the output
   sudo journalctl -u docker.service
   ```

2. **Permission denied errors**:

   ```bash
   # Ensure your user is in the docker group
   groups
   # If docker is not listed, run:
   sudo usermod -aG docker $USER
   newgrp docker
   ```

#### Ollama Issues

1. **Ollama service not starting**:

   ```bash
   # Check the status
   sudo systemctl status ollama
   # View logs
   sudo journalctl -u ollama.service
   ```

2. **Out of memory errors when running large models**:

   ```bash
   # Add swap space if needed
   sudo fallocate -l 8G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
   ```

3. **Network issues with Ollama API**:

   ```bash
   # Check if the Ollama service is listening on port 11434
   sudo netstat -tuln | grep 11434

   # If not, ensure the service is running
   sudo systemctl restart ollama

   # For Docker, ensure port forwarding is set up
   docker ps
   ```

### Getting Help

If you encounter issues not covered here:

- Visit the [Ollama GitHub repository](https://github.com/ollama/ollama)
- Join the [Ollama Discord community](https://discord.gg/ollama)
- Check the [Docker documentation](https://docs.docker.com/) for Docker-specific issues

## Conclusion

You now have Docker and Ollama installed on your server. You can proceed to pull and run various language models with Ollama.

For more information on using Ollama, refer to the official documentation:
- [Ollama Documentation](https://github.com/ollama/ollama/blob/main/README.md)
- [Ollama Model Library](https://ollama.com/library)

To ensure everything stays up to date, regularly run:

```bash
# Update Docker
sudo apt update && sudo apt upgrade -y  # For Ubuntu/Debian
sudo yum update -y  # For CentOS/RHEL

# Update Ollama
curl -fsSL https://ollama.com/install.sh | sh
```