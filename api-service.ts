/**
 * API Service - Functions for interacting with backend services
 * This service provides methods for working with datasets, models, and deployments
 */

/**
 * Check if the backend service is connected and responding
 * 
 * @returns Boolean indicating if the backend is connected
 */
export async function checkBackendConnection(): Promise<boolean> {
  try {
    // Simple health check endpoint
    const response = await fetch('/api/health', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    return response.ok;
  } catch (error) {
    console.error("Backend connection check failed:", error);
    return false;
  }
}

/**
 * List available datasets
 * 
 * @returns List of datasets
 */
export async function listDatasets(): Promise<any> {
  try {
    const response = await fetch('/api/datasets', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to list datasets: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error listing datasets:", error);
    throw error;
  }
}

/**
 * List fine-tuning jobs
 * 
 * @returns List of fine-tuning jobs
 */
export async function listFineTuningJobs(): Promise<any> {
  try {
    const response = await fetch('/api/fine-tuning', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to list fine-tuning jobs: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error listing fine-tuning jobs:", error);
    throw error;
  }
}

/**
 * List model deployments
 * 
 * @returns List of model deployments
 */
export async function listModelDeployments(): Promise<any> {
  try {
    const response = await fetch('/api/deployments', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to list model deployments: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error listing model deployments:", error);
    throw error;
  }
}

/**
 * List available models (both local and cloud)
 * 
 * @returns List of models
 */
export async function listModels(): Promise<any> {
  try {
    const response = await fetch('/api/models', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to list models: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error("Error listing models:", error);
    throw error;
  }
}

/**
 * Get dataset details
 * 
 * @param datasetId ID of the dataset
 * @returns Dataset details
 */
export async function getDatasetDetails(datasetId: string): Promise<any> {
  try {
    const response = await fetch(`/api/datasets/${datasetId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get dataset details: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error getting dataset details for ${datasetId}:`, error);
    throw error;
  }
}

/**
 * Get fine-tuning job details
 * 
 * @param jobId ID of the fine-tuning job
 * @returns Fine-tuning job details
 */
export async function getFineTuningJobDetails(jobId: string): Promise<any> {
  try {
    const response = await fetch(`/api/fine-tuning/${jobId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get fine-tuning job details: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error getting fine-tuning job details for ${jobId}:`, error);
    throw error;
  }
}

/**
 * Get deployment details
 * 
 * @param deploymentId ID of the deployment
 * @returns Deployment details
 */
export async function getDeploymentDetails(deploymentId: string): Promise<any> {
  try {
    const response = await fetch(`/api/deployments/${deploymentId}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      throw new Error(`Failed to get deployment details: ${response.statusText}`);
    }
    
    return await response.json();
  } catch (error) {
    console.error(`Error getting deployment details for ${deploymentId}:`, error);
    throw error;
  }
}
