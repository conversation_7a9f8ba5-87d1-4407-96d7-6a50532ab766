#!/usr/bin/env node

/**
 * Simple test to verify model selection functionality
 */

const { taskManager } = require('./lib/task-manager');

async function testModelSelection() {
  console.log('Testing model selection functionality...\n');

  try {
    // Test with default model
    console.log('1. Testing with default model...');
    const task1 = await taskManager.processUserMessage('Hello, what models are available?');
    console.log('Task completed with default model:', task1.id);
    console.log('Response preview:', taskManager.getTaskResponse(task1).substring(0, 100) + '...\n');

    // Test with specific model
    console.log('2. Testing with specific model (qwen2.5:3b)...');
    const task2 = await taskManager.processUserMessage('Create a simple React component', 'qwen2.5:3b');
    console.log('Task completed with qwen2.5:3b model:', task2.id);
    console.log('Response preview:', taskManager.getTaskResponse(task2).substring(0, 100) + '...\n');

    console.log('✅ Model selection test completed successfully!');
  } catch (error) {
    console.error('❌ Model selection test failed:', error.message);
  }
}

// Run the test
if (require.main === module) {
  testModelSelection();
}

module.exports = { testModelSelection };
