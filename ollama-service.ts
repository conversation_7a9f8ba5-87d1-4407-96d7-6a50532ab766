/**
 * Ollama Service - Core functions for interacting with Ollama models
 * This service provides a simplified interface for working with the Ollama API
 */

import { getOllamaAPI } from "./lib/ollama-api";
import { safeFetch } from "./lib/cross-fetch";

/**
 * Generate text using an Ollama model
 *
 * @param model The name of the model to use
 * @param prompt The prompt to generate from
 * @param options Additional options for generation
 * @param extendedTimeout Whether to use an extended timeout for longer operations
 * @param timeoutMs Custom timeout in milliseconds
 * @returns The generated response
 */
export async function generateWithOllama(
  model: string,
  prompt: string,
  options: any = {},
  extendedTimeout: boolean = false,
  timeoutMs?: number
): Promise<any> {
  try {
    const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');

    // Set default options
    const defaultOptions = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      max_tokens: extendedTimeout ? 2048 : 1024,
      stream: false
    };

    // Merge with user options
    const mergedOptions = { ...defaultOptions, ...options };

    // Set up request with timeout
    const controller = new AbortController();
    const timeout = timeoutMs || (extendedTimeout ? 60000 : 30000); // 60s for extended, 30s default

    const timeoutId = setTimeout(() => {
      controller.abort();
    }, timeout);

    try {
      // Make the request to Ollama
      const response = await ollamaApi.generate({
        model,
        prompt,
        options: mergedOptions,
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response;
    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error(`Request timed out after ${timeout}ms`);
      }
      throw error;
    }
  } catch (error) {
    console.error(`Error generating with Ollama (${model}):`, error);
    throw error;
  }
}

/**
 * List available Ollama models
 *
 * @returns List of available models
 */
export async function listOllamaModels(): Promise<any> {
  try {
    const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');
    return await ollamaApi.listModels();
  } catch (error) {
    console.error("Error listing Ollama models:", error);
    throw error;
  }
}

/**
 * Check if Ollama is healthy and responding
 *
 * @returns Boolean indicating if Ollama is healthy
 */
export async function checkOllamaHealth(): Promise<boolean> {
  try {
    // First try a direct fetch to the Ollama API
    try {
      const response = await safeFetch('http://127.0.0.1:11434/api/tags');
      if (response.ok) {
        return true;
      }
    } catch (directError) {
      console.error("Direct Ollama health check failed:", directError);
    }

    // Fallback to using the API instance
    const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');
    await ollamaApi.listModels();
    return true;
  } catch (error) {
    console.error("Ollama health check failed:", error);
    return false;
  }
}

/**
 * Stream chat with an Ollama model
 *
 * @param model The name of the model to use
 * @param messages Array of chat messages
 * @param options Additional options for generation
 * @param onChunk Callback for each chunk of the response
 * @param onError Callback for errors
 * @param onComplete Callback when streaming is complete
 */
export async function streamChatWithOllama(
  model: string,
  messages: any[],
  options: any = {},
  onChunk: (chunk: any) => void,
  onError: (error: any) => void,
  onComplete: () => void
): Promise<void> {
  try {
    const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');

    // Set default options
    const defaultOptions = {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40
    };

    // Merge with user options
    const mergedOptions = { ...defaultOptions, ...options };

    // Stream the chat response
    await ollamaApi.streamChat(
      {
        model,
        messages,
        options: mergedOptions,
        stream: true
      },
      onChunk,
      onError,
      onComplete
    );
  } catch (error) {
    console.error(`Error streaming chat with Ollama (${model}):`, error);
    onError(error);
  }
}

/**
 * Create embeddings using an Ollama model
 *
 * @param model The name of the embedding model to use
 * @param text The text to create embeddings for
 * @returns The embedding vector
 */
export async function createEmbeddingWithOllama(
  model: string,
  text: string
): Promise<number[]> {
  try {
    const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');

    const response = await ollamaApi.createEmbedding({
      model,
      prompt: text
    });

    return response.embedding;
  } catch (error) {
    console.error(`Error creating embedding with Ollama (${model}):`, error);
    throw error;
  }
}
