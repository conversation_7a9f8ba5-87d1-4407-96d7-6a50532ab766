"use client"

import { createContext, useContext, useState, useEffect, type ReactNode } from "react"
import { v4 as uuidv4 } from "uuid"

export interface User {
  id: string
  name: string
  email: string
  avatar?: string
  createdAt: string
  lastActive: string
  passwordHash: string // In a real app, you'd never store this client-side
}

interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<{ success: boolean; message: string }>
  signup: (name: string, email: string, password: string) => Promise<{ success: boolean; message: string }>
  logout: () => void
  updateUser: (updates: Partial<User>) => void
  getAllUsers: () => User[]
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  // Load user from localStorage on mount
  useEffect(() => {
    const loadUser = () => {
      try {
        console.log("AuthProvider: Loading user from localStorage")
        const storedUser = localStorage.getItem("ollama-ui-current-user")
        if (storedUser) {
          console.log("AuthProvider: Found stored user")
          const parsedUser = JSON.parse(storedUser)
          setUser(parsedUser)
          setIsAuthenticated(true)
        } else {
          console.log("AuthProvider: No stored user found")
        }
      } catch (error) {
        console.error("Error loading user:", error)
      } finally {
        console.log("AuthProvider: Setting isLoading to false")
        setIsLoading(false)
      }
    }

    // Add a small delay to ensure the component is mounted
    const timer = setTimeout(() => {
      loadUser()
    }, 100)

    return () => clearTimeout(timer)
  }, [])

  // Simple hash function for passwords (NOT secure for production)
  const hashPassword = (password: string): string => {
    let hash = 0
    for (let i = 0; i < password.length; i++) {
      const char = password.charCodeAt(i)
      hash = (hash << 5) - hash + char
      hash = hash & hash // Convert to 32bit integer
    }
    return hash.toString(16)
  }

  const getAllUsers = (): User[] => {
    try {
      const storedUsers = localStorage.getItem("ollama-ui-users")
      if (storedUsers) {
        return JSON.parse(storedUsers)
      }
      return []
    } catch (error) {
      console.error("Error getting users:", error)
      return []
    }
  }

  const login = async (email: string, password: string): Promise<{ success: boolean; message: string }> => {
    try {
      const users = getAllUsers()
      const user = users.find((u) => u.email.toLowerCase() === email.toLowerCase())

      if (!user) {
        return { success: false, message: "User not found" }
      }

      const passwordHash = hashPassword(password)
      if (user.passwordHash !== passwordHash) {
        return { success: false, message: "Incorrect password" }
      }

      // Update last active
      const updatedUser = {
        ...user,
        lastActive: new Date().toISOString(),
      }

      // Update user in storage
      const updatedUsers = users.map((u) => (u.id === user.id ? updatedUser : u))
      localStorage.setItem("ollama-ui-users", JSON.stringify(updatedUsers))

      // Set current user
      localStorage.setItem("ollama-ui-current-user", JSON.stringify(updatedUser))
      setUser(updatedUser)
      setIsAuthenticated(true)

      return { success: true, message: "Login successful" }
    } catch (error) {
      console.error("Login error:", error)
      return { success: false, message: "An error occurred during login" }
    }
  }

  const signup = async (
    name: string,
    email: string,
    password: string,
  ): Promise<{ success: boolean; message: string }> => {
    try {
      const users = getAllUsers()

      // Check if email already exists
      if (users.some((u) => u.email.toLowerCase() === email.toLowerCase())) {
        return { success: false, message: "Email already in use" }
      }

      const newUser: User = {
        id: uuidv4(),
        name,
        email,
        passwordHash: hashPassword(password),
        createdAt: new Date().toISOString(),
        lastActive: new Date().toISOString(),
      }

      // Add user to storage
      const updatedUsers = [...users, newUser]
      localStorage.setItem("ollama-ui-users", JSON.stringify(updatedUsers))

      // Set current user
      localStorage.setItem("ollama-ui-current-user", JSON.stringify(newUser))
      setUser(newUser)
      setIsAuthenticated(true)

      return { success: true, message: "Account created successfully" }
    } catch (error) {
      console.error("Signup error:", error)
      return { success: false, message: "An error occurred during signup" }
    }
  }

  const logout = () => {
    localStorage.removeItem("ollama-ui-current-user")
    setUser(null)
    setIsAuthenticated(false)
  }

  const updateUser = (updates: Partial<User>) => {
    if (!user) return

    try {
      const updatedUser = { ...user, ...updates, lastActive: new Date().toISOString() }

      // Update in localStorage
      localStorage.setItem("ollama-ui-current-user", JSON.stringify(updatedUser))

      // Update in users list
      const users = getAllUsers()
      const updatedUsers = users.map((u) => (u.id === user.id ? updatedUser : u))
      localStorage.setItem("ollama-ui-users", JSON.stringify(updatedUsers))

      // Update state
      setUser(updatedUser)
    } catch (error) {
      console.error("Error updating user:", error)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        isAuthenticated,
        login,
        signup,
        logout,
        updateUser,
        getAllUsers,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}

