#!/usr/bin/env node

/**
 * Ollama Connection Diagnostic Tool
 * 
 * This script helps diagnose connection issues with Ollama.
 * It tests the connection to Ollama and provides troubleshooting tips.
 */

const http = require('http');
const os = require('os');
const { execSync } = require('child_process');

// Configuration
const OLLAMA_HOST = '127.0.0.1';
const OLLAMA_PORT = 11434;
const OLLAMA_URL = `http://${OLLAMA_HOST}:${OLLAMA_PORT}`;
const TIMEOUT = 5000;

// Print header
console.log('\n=== Mash Bot Ollama Connection Diagnostic Tool ===\n');
console.log(`Testing connection to Ollama at ${OLLAMA_URL}...`);

// Print environment information
console.log('\nEnvironment Information:');
console.log(`- Operating System: ${os.type()} ${os.release()} (${os.platform()})`);
console.log(`- Architecture: ${os.arch()}`);
console.log(`- Node.js Version: ${process.version}`);
console.log(`- Hostname: ${os.hostname()}`);

// Check if port is in use
function checkPortInUse() {
  console.log('\nChecking if port 11434 is in use...');
  
  try {
    let command = '';
    let output = '';
    
    if (os.platform() === 'win32') {
      command = 'netstat -ano | findstr :11434';
      try {
        output = execSync(command).toString();
        console.log('Port check result:');
        console.log(output);
        return output.includes('LISTENING');
      } catch (e) {
        console.log('No process found listening on port 11434');
        return false;
      }
    } else {
      command = 'lsof -i :11434 || netstat -tuln | grep 11434';
      try {
        output = execSync(command).toString();
        console.log('Port check result:');
        console.log(output);
        return output.trim().length > 0;
      } catch (e) {
        console.log('No process found listening on port 11434');
        return false;
      }
    }
  } catch (error) {
    console.log('Could not check port status:', error.message);
    return null;
  }
}

// Test connection to Ollama
function testOllamaConnection() {
  console.log('\nTesting direct HTTP connection to Ollama...');
  
  return new Promise((resolve) => {
    const req = http.request({
      method: 'GET',
      hostname: OLLAMA_HOST,
      port: OLLAMA_PORT,
      path: '/api/tags',
      timeout: TIMEOUT
    }, (res) => {
      console.log(`Response status: ${res.statusCode} ${res.statusMessage}`);
      
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log('✅ Connection successful!');
          try {
            const parsed = JSON.parse(data);
            const modelCount = parsed.models?.length || 0;
            console.log(`Found ${modelCount} models`);
            
            if (modelCount > 0) {
              console.log('Available models:');
              parsed.models.forEach(model => {
                console.log(`- ${model.name}`);
              });
            } else {
              console.log('No models found. You may need to pull a model using:');
              console.log('  ollama pull llama2');
            }
          } catch (e) {
            console.log('Could not parse response:', e.message);
            console.log('Raw response:', data.substring(0, 200));
          }
          resolve(true);
        } else {
          console.log('❌ Connection failed with status code:', res.statusCode);
          console.log('Response data:', data);
          resolve(false);
        }
      });
    });
    
    req.on('error', (error) => {
      console.log(`❌ Connection error: ${error.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.log('❌ Connection timed out after', TIMEOUT, 'ms');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
}

// Provide troubleshooting tips based on the results
function provideTroubleshootingTips(connectionSuccessful, portInUse) {
  console.log('\n=== Diagnostic Results ===');
  
  if (connectionSuccessful) {
    console.log('✅ Ollama connection is working properly!');
    console.log('\nIf you are still experiencing issues in the application:');
    console.log('1. Check that you are using the correct URL in the application settings');
    console.log('2. Ensure the application is configured to use http://127.0.0.1:11434');
    console.log('3. Check for any browser console errors');
    console.log('4. Try restarting the application');
  } else {
    console.log('❌ Could not connect to Ollama');
    
    console.log('\nTroubleshooting tips:');
    
    if (portInUse === true) {
      console.log('✅ Port 11434 is in use, which suggests Ollama is running');
      console.log('1. Check if Ollama is running with the correct configuration');
      console.log('2. Verify there are no firewall rules blocking the connection');
      console.log('3. Try restarting Ollama');
    } else if (portInUse === false) {
      console.log('❌ Port 11434 is not in use, which suggests Ollama is not running');
      console.log('1. Start Ollama using the appropriate command for your system');
      console.log('   - Windows: Start Ollama from the Start Menu or desktop shortcut');
      console.log('   - macOS: Open Ollama from Applications or run `ollama serve` in Terminal');
      console.log('   - Linux: Run `ollama serve` in Terminal');
      console.log('2. Check if Ollama is installed correctly');
      console.log('3. Verify Ollama is configured to use the default port (11434)');
    } else {
      console.log('1. Check if Ollama is installed and running');
      console.log('2. Verify Ollama is listening on 127.0.0.1:11434');
      console.log('3. Check for any firewall rules blocking the connection');
      console.log('4. Try restarting Ollama');
    }
    
    console.log('\nAdditional steps:');
    console.log('- Check Ollama logs for any errors');
    console.log('- Verify your network configuration');
    console.log('- Ensure no other application is using port 11434');
    console.log('- Try accessing http://127.0.0.1:11434/api/tags in your browser');
  }
}

// Main function
async function main() {
  const portInUse = checkPortInUse();
  const connectionSuccessful = await testOllamaConnection();
  provideTroubleshootingTips(connectionSuccessful, portInUse);
  
  console.log('\n=== End of Diagnostic Report ===\n');
}

// Run the diagnostic
main().catch(error => {
  console.error('Error running diagnostics:', error);
});
