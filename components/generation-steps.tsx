import { CheckCircle, Circle, AlertCircle, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

interface Step {
  id: string
  name: string
  status: "pending" | "in-progress" | "completed" | "error"
  description: string
}

interface GenerationStepsProps {
  steps: Step[]
  currentStepId: string
}

export function GenerationSteps({ steps, currentStepId }: GenerationStepsProps) {
  return (
    <div className="space-y-3">
      <h3 className="text-sm font-medium">Generation Progress</h3>
      <div className="flex items-center gap-2">
        {steps.map((step, index) => (
          <div key={step.id} className="flex items-center">
            <div className="flex flex-col items-center">
              <div
                className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 transition-colors",
                  step.status === "completed" && "border-green-500 text-green-500",
                  step.status === "in-progress" && "border-primary text-primary",
                  step.status === "error" && "border-destructive text-destructive",
                  step.status === "pending" && "border-gray-300 text-gray-300",
                )}
              >
                {step.status === "completed" && <CheckCircle className="h-5 w-5" />}
                {step.status === "in-progress" && <Loader2 className="h-5 w-5 animate-spin" />}
                {step.status === "error" && <AlertCircle className="h-5 w-5" />}
                {step.status === "pending" && <Circle className="h-5 w-5" />}
              </div>
              <span
                className={cn(
                  "text-xs mt-1 transition-colors",
                  step.id === currentStepId && "font-medium",
                  step.status === "completed" && "text-green-500",
                  step.status === "in-progress" && "text-primary",
                  step.status === "error" && "text-destructive",
                )}
              >
                {step.name}
              </span>
            </div>

            {index < steps.length - 1 && (
              <div
                className={cn(
                  "h-0.5 w-4 mx-1 transition-colors",
                  steps[index + 1].status === "completed" || steps[index + 1].status === "in-progress"
                    ? "bg-primary"
                    : "bg-gray-300",
                )}
              />
            )}
          </div>
        ))}
      </div>
      {currentStepId && (
        <p className="text-xs text-muted-foreground">{steps.find((step) => step.id === currentStepId)?.description}</p>
      )}
    </div>
  )
}

