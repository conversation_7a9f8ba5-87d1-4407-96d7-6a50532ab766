"use client"

import { cn } from "@/lib/utils"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import ReactMarkdown from "react-markdown"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"

interface ChatMessageProps {
  message: {
    role: string
    content: string
    loading?: boolean
    error?: boolean
    id?: string
  }
}

export function ChatMessage({ message }: ChatMessageProps) {
  const isUser = message.role === "user"
  const [copied, setCopied] = useState(false)

  const copyToClipboard = () => {
    navigator.clipboard.writeText(message.content)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }

  return (
    <div
      className={cn(
        "group relative flex items-start gap-4 pb-4",
        isUser ? "justify-end" : "justify-start",
        message.error && "text-destructive",
      )}
    >
      {!isUser && (
        <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border bg-primary text-primary-foreground">
          <Bot className="h-4 w-4" />
        </div>
      )}

      <div className={cn("flex-1 space-y-2 overflow-hidden px-1", isUser ? "max-w-md ml-auto" : "max-w-2xl")}>
        {message.loading ? (
          <div className="flex items-center gap-2 text-muted-foreground">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Generating response...</span>
          </div>
        ) : (
          <ReactMarkdown
            className={cn("prose prose-sm max-w-none break-words dark:prose-invert", message.error && "prose-rose")}
            components={{
              pre: ({ node, ...props }) => (
                <div className="relative my-2">
                  <pre className="overflow-auto rounded-lg bg-muted p-4 text-sm" {...props} />
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute right-2 top-2 h-8 w-8 opacity-0 group-hover:opacity-100"
                    onClick={copyToClipboard}
                  >
                    {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                  </Button>
                </div>
              ),
              code: ({ node, className, children, ...props }) => {
                const match = /language-(\w+)/.exec(className || "")
                return match ? (
                  <code className={className} {...props}>
                    {children}
                  </code>
                ) : (
                  <code className="rounded-sm bg-muted px-1 py-0.5 font-mono text-sm" {...props}>
                    {children}
                  </code>
                )
              },
            }}
          >
            {message.content}
          </ReactMarkdown>
        )}
      </div>

      {isUser && (
        <div className="flex h-8 w-8 shrink-0 select-none items-center justify-center rounded-md border bg-background">
          <User className="h-4 w-4" />
        </div>
      )}
    </div>
  )
}

