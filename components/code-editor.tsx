"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Copy, Check } from "lucide-react"

interface CodeEditorProps {
  htmlCode: string
  cssCode: string
  jsCode: string
  setHtmlCode: (code: string) => void
  setCssCode: (code: string) => void
  setJsCode: (code: string) => void
}

export function CodeEditor({ htmlCode, cssCode, jsCode, setHtmlCode, setCssCode, setJsCode }: CodeEditorProps) {
  const [activeTab, setActiveTab] = useState("html")
  const [copied, setCopied] = useState<string | null>(null)

  const copyToClipboard = (code: string, type: string) => {
    navigator.clipboard.writeText(code)
    setCopied(type)
    setTimeout(() => setCopied(null), 2000)
  }

  return (
    <div className="h-full flex flex-col">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="border-b px-4">
          <TabsList className="mt-2">
            <TabsTrigger value="html" className={cn(htmlCode ? "" : "text-muted-foreground")}>
              HTML
            </TabsTrigger>
            <TabsTrigger value="css" className={cn(cssCode ? "" : "text-muted-foreground")}>
              CSS
            </TabsTrigger>
            <TabsTrigger value="js" className={cn(jsCode ? "" : "text-muted-foreground")}>
              JavaScript
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="html" className="flex-1 p-0 relative">
          <div className="absolute right-2 top-2 z-10">
            <Button variant="ghost" size="icon" onClick={() => copyToClipboard(htmlCode, "html")} disabled={!htmlCode}>
              {copied === "html" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          <textarea
            value={htmlCode}
            onChange={(e) => setHtmlCode(e.target.value)}
            className="w-full h-full p-4 font-mono text-sm bg-background resize-none focus:outline-none"
            placeholder="No HTML code generated yet..."
            spellCheck="false"
          />
        </TabsContent>

        <TabsContent value="css" className="flex-1 p-0 relative">
          <div className="absolute right-2 top-2 z-10">
            <Button variant="ghost" size="icon" onClick={() => copyToClipboard(cssCode, "css")} disabled={!cssCode}>
              {copied === "css" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          <textarea
            value={cssCode}
            onChange={(e) => setCssCode(e.target.value)}
            className="w-full h-full p-4 font-mono text-sm bg-background resize-none focus:outline-none"
            placeholder="No CSS code generated yet..."
            spellCheck="false"
          />
        </TabsContent>

        <TabsContent value="js" className="flex-1 p-0 relative">
          <div className="absolute right-2 top-2 z-10">
            <Button variant="ghost" size="icon" onClick={() => copyToClipboard(jsCode, "js")} disabled={!jsCode}>
              {copied === "js" ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>
          <textarea
            value={jsCode}
            onChange={(e) => setJsCode(e.target.value)}
            className="w-full h-full p-4 font-mono text-sm bg-background resize-none focus:outline-none"
            placeholder="No JavaScript code generated yet..."
            spellCheck="false"
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

