"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Loader2, Send, User, Bot, Copy, Code, Sparkles, Trash2, FileCode, Play, Moon, Sun, Brain, AlertCircle } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { getOllamaAPI, type GenerateRequestOptions } from "@/lib/ollama-api"
import ReactMarkdown from "react-markdown"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { v4 as uuidv4 } from "uuid"
import dynamic from "next/dynamic"
import CodePreview from "@/components/code-preview"
import CodeGenerationTracker, { type GenerationStep } from "@/components/code-generation-tracker"
import CodeGenerationDisplay from "@/components/code-generation-display"
import ReasoningDisplay from "@/components/reasoning-display"
import MashBotDiagnostics from "@/components/mashbot-diagnostics"
import { taskManager } from "@/lib/task-manager"
import { ThinkingProcess } from "@/lib/reasoning-engine"

// Dynamically import EditableCodeBlock with SSR disabled
const EditableCodeBlock = dynamic(
  () => import("@/components/editable-code-block"),
  { ssr: false }
);

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date | string // Allow string for backward compatibility
  pending?: boolean
}

interface EnhancedChatInterfaceProps {
  selectedModel: string
  onCreateFile: (file: {
    id: string
    name: string
    content: string
    language: string
    lastModified: Date
  }) => void
  temperature: number
}

interface CodeFile {
  name: string
  content: string
  language: string
}

export default function EnhancedChatInterface({
  selectedModel,
  onCreateFile,
  temperature,
}: EnhancedChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: uuidv4(),
      role: "assistant",
      content:
        "Hi! I'm your Ollama coding assistant. I can help you build applications, components, and more. Just describe what you want to create, and I'll generate the code for you.",
      timestamp: new Date(),
    },
  ])
  const [input, setInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [chatHistory, setChatHistory] = useState<{ title: string; id: string; preview: string }[]>([])
  const [activeChatId, setActiveChatId] = useState<string | null>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<"chat" | "preview" | "progress" | "code" | "reasoning" | "diagnostics">("chat")
  const [isPreviewFullscreen, setIsPreviewFullscreen] = useState(false)
  const [viewMode, setViewMode] = useState<"chat" | "editor">("chat")
  const [showDiagnostics, setShowDiagnostics] = useState(false)

  // Code generation state
  const [generationSteps, setGenerationSteps] = useState<GenerationStep[]>([])
  const [currentStepId, setCurrentStepId] = useState<string | null>(null)
  const [previewHtml, setPreviewHtml] = useState("")
  const [previewCss, setPreviewCss] = useState("")
  const [previewJs, setPreviewJs] = useState("")
  const [generatedFiles, setGeneratedFiles] = useState<CodeFile[]>([])

  // Reasoning state
  const [currentThinkingProcess, setCurrentThinkingProcess] = useState<ThinkingProcess | undefined>(undefined)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()
  const { theme, setTheme } = useTheme()

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  // Helper function to ensure timestamp is a Date object
  const ensureDate = (timestamp: Date | string): Date => {
    if (timestamp instanceof Date) {
      return timestamp
    }
    // Try to parse the string as a date
    try {
      return new Date(timestamp)
    } catch (e) {
      // If parsing fails, return current date
      return new Date()
    }
  }

  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return

    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: input,
      timestamp: new Date(),
    }

    // Add user message to chat
    setMessages((prev) => [...prev, userMessage])

    // Clear input
    setInput("")

    // Create pending message
    const pendingId = uuidv4()
    const pendingMessage: Message = {
      id: pendingId,
      role: "assistant",
      content: "",
      timestamp: new Date(),
      pending: true,
    }

    setMessages((prev) => [...prev, pendingMessage])
    setIsProcessing(true)

    // Reset thinking process
    setCurrentThinkingProcess(undefined)

    // Reset generation steps if this is a new request
    if (isCodeGenerationRequest(input)) {
      setGenerationSteps([
        {
          id: uuidv4(),
          name: "Analyzing request",
          status: "in-progress",
          description: "Understanding what needs to be built",
        },
        {
          id: uuidv4(),
          name: "Planning structure",
          status: "pending",
          description: "Determining files and components needed",
        },
        {
          id: uuidv4(),
          name: "Generating code",
          status: "pending",
          description: "Writing the implementation",
        },
        {
          id: uuidv4(),
          name: "Finalizing",
          status: "pending",
          description: "Polishing and preparing preview",
        },
      ])
      setCurrentStepId(generationSteps[0]?.id || null)
      setActiveTab("progress")
      setGeneratedFiles([])
    } else {
      // For non-code requests, show reasoning tab
      setActiveTab("reasoning")
    }

    try {
      // Add user message to conversation history in task manager
      taskManager.addToConversationHistory({
        role: 'user',
        content: input
      });

      // Process the message through the reasoning engine
      const task = await taskManager.processUserMessage(input);

      // Store the thinking process for display
      if (task.thinkingProcess) {
        setCurrentThinkingProcess(task.thinkingProcess);
      }

      // Get the response from the task
      const response = taskManager.getTaskResponse(task);
      let fullResponse = response;

      // Update the pending message with the response
      setMessages((prev) =>
        prev.map((msg) => (msg.id === pendingId ? { ...msg, content: fullResponse, pending: false } : msg))
      );

      // Add assistant message to conversation history in task manager
      taskManager.addToConversationHistory({
        role: 'assistant',
        content: fullResponse
      });

      // Extract code for preview if this is a code generation request
      if (isCodeGenerationRequest(input)) {
        extractCodeForPreview(fullResponse);
        extractCodeFiles(fullResponse);

        // Complete all generation steps
        setGenerationSteps((prev) => prev.map((step) => ({ ...step, status: "completed" })));

        // Switch to code tab if we have generated files
        if (generatedFiles.length > 0) {
          setActiveTab("code");
        } else {
          setActiveTab("preview");
        }
      }

      // Save chat to history if this is a new chat
      if (!activeChatId) {
        const newChatId = uuidv4();
        const title = generateChatTitle(userMessage.content);
        setChatHistory((prev) => [
          { id: newChatId, title, preview: userMessage.content.substring(0, 60) + "..." },
          ...prev,
        ]);
        setActiveChatId(newChatId);

        // Save chat to localStorage
        saveChat(newChatId, title, [
          ...messages,
          userMessage,
          { ...pendingMessage, content: fullResponse, pending: false },
        ]);
      } else {
        // Update existing chat
        saveChat(activeChatId, chatHistory.find((chat) => chat.id === activeChatId)?.title || "Chat", [
          ...messages,
          { ...pendingMessage, content: fullResponse, pending: false },
        ]);
      }

      // For legacy code compatibility, we'll keep the old approach for code generation
      if (isCodeGenerationRequest(input)) {
        const ollamaApi = getOllamaAPI('custom', 'http://127.0.0.1:11434');

        // Prepare context from previous messages (limit to last 10 for performance)
        const recentMessages = messages
          .slice(-10)
          .map((msg) => `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`)
          .join("\n\n");

        // Enhanced prompt for code generation
        const systemPrompt = `You are an expert coding assistant that helps users build software.

When asked to create or build something:
1. Analyze the request carefully
2. Plan the structure and components needed
3. Generate complete, working code with proper imports and dependencies
4. Provide clear explanations of how the code works

Use markdown formatting for code blocks with the appropriate language specified.

For web applications or components:
- Separate HTML, CSS, and JavaScript into different code blocks
- Use modern best practices and standards
- Ensure the code is complete and ready to use

For React components:
- Use functional components with hooks
- Include all necessary imports
- Structure the code for readability and maintainability

For backend code:
- Include proper error handling
- Follow security best practices
- Structure the code in a modular way

Always provide complete solutions that can be used immediately.`;

        // Prepare model parameters
        const options: GenerateRequestOptions = {
          model: selectedModel,
          prompt: fullResponse, // Use the response from the reasoning engine
          stream: false,
          options: {
            temperature,
            top_p: 0.9,
            top_k: 40,
            num_predict: 4096,
            presence_penalty: 0.0,
            frequency_penalty: 0.0,
            stop: ["User:", "\n\nUser"],
          },
        };
      }

      // For legacy code compatibility, we'll keep the old approach for code generation
      // This section is now handled by the reasoning engine
    } catch (error) {
      console.error("Error sending message:", error)
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })

      // Remove the pending message
      setMessages((prev) => prev.filter((msg) => msg.id !== pendingId))

      // Update generation steps to show error
      if (isCodeGenerationRequest(input)) {
        setGenerationSteps((prev) =>
          prev.map((step) => (step.status === "in-progress" ? { ...step, status: "error" } : step)),
        )
      }
    } finally {
      setIsProcessing(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const generateChatTitle = (firstMessage: string): string => {
    // Generate a title based on the first user message
    const words = firstMessage.split(" ")
    if (words.length <= 5) return firstMessage
    return words.slice(0, 5).join(" ") + "..."
  }

  const saveChat = (id: string, title: string, messages: Message[]) => {
    try {
      // Create a copy of the messages array to avoid modifying the original
      const messagesToSave = messages.map(msg => {
        // For very large messages, we might want to truncate content to save space
        if (msg.content.length > 100000) {
          return {
            ...msg,
            content: msg.content.substring(0, 100000) + "\n... [Content truncated for storage]",
            originalLength: msg.content.length // Store original length for reference
          };
        }
        return msg;
      });

      // Save to localStorage
      localStorage.setItem(`ollama-ui-chat-${id}`, JSON.stringify({ title, messages: messagesToSave }));

      // Update chat history in memory if needed
      const existingChat = chatHistory.find(chat => chat.id === id);
      if (!existingChat) {
        setChatHistory(prev => [
          {
            id,
            title,
            preview: messages.find(msg => msg.role === "user")?.content.substring(0, 60) + "..." || "..."
          },
          ...prev
        ]);
      }
    } catch (error) {
      console.error("Error saving chat:", error);
      // Handle storage quota exceeded error
      if (error instanceof DOMException && error.name === 'QuotaExceededError') {
        toast({
          title: "Storage limit reached",
          description: "Your chat history storage is full. Try deleting some old chats.",
          variant: "destructive",
        });
      }
    }
  }

  const loadChat = (id: string) => {
    try {
      const chatData = localStorage.getItem(`ollama-ui-chat-${id}`)
      if (chatData) {
        // Parse chat data
        const { messages: chatMessages, title } = JSON.parse(chatData)

        // Reset current UI state before loading new chat
        setPreviewHtml("")
        setPreviewCss("")
        setPreviewJs("")
        setGeneratedFiles([])

        // Ensure all timestamps are Date objects
        const processedMessages = chatMessages.map((msg: Message) => ({
          ...msg,
          timestamp: ensureDate(msg.timestamp),
        }))

        // Load messages
        setMessages(processedMessages)
        setActiveChatId(id)

        // If this is a chat with code, extract code for preview and files
        const lastAssistantMessage = processedMessages
          .filter((msg: Message) => msg.role === "assistant")
          .pop();

        if (lastAssistantMessage && isCodeGenerationRequest(processedMessages[0].content)) {
          // Extract code from the last assistant message
          extractCodeForPreview(lastAssistantMessage.content);
          extractCodeFiles(lastAssistantMessage.content);
        }

        // Go to chat tab by default
        setActiveTab("chat")
      }
    } catch (error) {
      console.error("Error loading chat:", error)
      toast({
        title: "Error",
        description: "Failed to load chat history.",
        variant: "destructive",
      })
    }
  }

  const startNewChat = () => {
    setMessages([
      {
        id: uuidv4(),
        role: "assistant",
        content:
          "Hi! I'm Mash Bot, your frontend development assistant. I specialize in building frontends that communicate with AI models via Ollama or Hugging Face. Just describe the frontend you want to create, and I'll generate the complete code for you. Try asking me to build a React dashboard that connects to an Ollama model, or a chat interface that uses Hugging Face's API.",
        timestamp: new Date(),
      },
    ])
    setActiveChatId(null)
    setIsMobileMenuOpen(false)
    setGenerationSteps([])
    setCurrentStepId(null)
    setPreviewHtml("")
    setPreviewCss("")
    setPreviewJs("")
    setGeneratedFiles([])
    setCurrentThinkingProcess(undefined)
    setActiveTab("chat")
  }

  const deleteChat = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      localStorage.removeItem(`ollama-ui-chat-${id}`)
      setChatHistory((prev) => prev.filter((chat) => chat.id !== id))

      if (activeChatId === id) {
        startNewChat()
      }
    } catch (error) {
      console.error("Error deleting chat:", error)
    }
  }

  const isCodeGenerationRequest = (text: string): boolean => {
    const codeGenerationKeywords = [
      "create",
      "build",
      "generate",
      "make",
      "develop",
      "implement",
      "code",
      "component",
      "application",
      "app",
      "website",
      "page",
      "function",
      "class",
      "react",
      "vue",
      "angular",
      "html",
      "css",
      "javascript",
      "typescript",
    ]

    const lowerText = text.toLowerCase()
    return codeGenerationKeywords.some((keyword) => lowerText.includes(keyword))
  }

  const extractCodeForPreview = (text: string) => {
    console.log("Extracting code for preview from:", text.substring(0, 100) + "...");

    // Extract HTML - more flexible pattern matching
    const htmlMatches = [...text.matchAll(/```(?:html)\s*\n([\s\S]*?)```/gi)]
    if (htmlMatches.length > 0) {
      // Get the last HTML match for the final preview
      const lastHtmlMatch = htmlMatches[htmlMatches.length - 1]
      console.log("Found HTML:", lastHtmlMatch[1].substring(0, 50) + "...");
      setPreviewHtml(lastHtmlMatch[1])
    } else {
      console.log("No HTML found in the response");
    }

    // Extract CSS - more flexible pattern matching
    const cssMatches = [...text.matchAll(/```(?:css)\s*\n([\s\S]*?)```/gi)]
    if (cssMatches.length > 0) {
      // Get the last CSS match for the final preview
      const lastCssMatch = cssMatches[cssMatches.length - 1]
      console.log("Found CSS:", lastCssMatch[1].substring(0, 50) + "...");
      setPreviewCss(lastCssMatch[1])
    } else {
      console.log("No CSS found in the response");
    }

    // Extract JavaScript - more flexible pattern matching
    const jsMatches = [...text.matchAll(/```(?:javascript|js)\s*\n([\s\S]*?)```/gi)]
    if (jsMatches.length > 0) {
      // Get the last JS match for the final preview
      const lastJsMatch = jsMatches[jsMatches.length - 1]
      const jsContent = lastJsMatch[2] || lastJsMatch[1]; // Handle both pattern groups
      console.log("Found JS:", jsContent.substring(0, 50) + "...");
      setPreviewJs(jsContent)
    } else {
      console.log("No JavaScript found in the response");
    }

    // If we found some code, also store it in the generated files
    if (htmlMatches.length > 0 || cssMatches.length > 0 || jsMatches.length > 0) {
      const files: CodeFile[] = [];

      if (htmlMatches.length > 0) {
        files.push({
          name: "index.html",
          content: htmlMatches[htmlMatches.length - 1][1],
          language: "html"
        });
      }

      if (cssMatches.length > 0) {
        files.push({
          name: "styles.css",
          content: cssMatches[cssMatches.length - 1][1],
          language: "css"
        });
      }

      if (jsMatches.length > 0) {
        const jsContent = jsMatches[jsMatches.length - 1][2] || jsMatches[jsMatches.length - 1][1];
        files.push({
          name: "script.js",
          content: jsContent,
          language: "javascript"
        });
      }

      if (files.length > 0) {
        setGeneratedFiles(prevFiles => {
          // Only replace files if we don't have any yet
          if (prevFiles.length === 0) {
            return files;
          }
          return prevFiles;
        });
      }
    }
  }

  // For extracting code from an entire conversation for the preview
  const extractFinalCodeForPreview = () => {
    // Get the last assistant message that contains code
    const assistantMessagesWithCode = messages
      .filter(msg => msg.role === "assistant" && !msg.pending)
      .filter(msg => {
        return msg.content.includes("```html") ||
               msg.content.includes("```css") ||
               msg.content.includes("```javascript") ||
               msg.content.includes("```js")
      })

    if (assistantMessagesWithCode.length === 0) return;

    // Extract code from the last message with code
    const lastMessageWithCode = assistantMessagesWithCode[assistantMessagesWithCode.length - 1];
    extractCodeForPreview(lastMessageWithCode.content);
  }

  const extractCodeFiles = (text: string) => {
    // Extract all code blocks with language
    const codeBlockRegex = /```(\w+)\n([\s\S]*?)```/g
    const matches = [...text.matchAll(codeBlockRegex)]

    if (matches.length === 0) return

    const newFiles: CodeFile[] = []
    const fileNames = new Set<string>()

    matches.forEach((match, index) => {
      const language = match[1]
      const content = match[2]

      // Try to determine a reasonable filename based on content and language
      let fileName = ""

      // Look for component or class names in React/JS/TS files
      if (["javascript", "typescript", "jsx", "tsx", "js", "ts"].includes(language)) {
        const componentMatch = content.match(/function\s+([A-Z][a-zA-Z0-9]*)/)
        const classMatch = content.match(/class\s+([A-Z][a-zA-Z0-9]*)/)
        const constMatch = content.match(/const\s+([A-Z][a-zA-Z0-9]*)\s*=/)

        if (componentMatch) {
          fileName = `${componentMatch[1]}.${language === "typescript" || language === "tsx" ? "tsx" : "jsx"}`
        } else if (classMatch) {
          fileName = `${classMatch[1]}.${language === "typescript" || language === "tsx" ? "ts" : "js"}`
        } else if (constMatch) {
          fileName = `${constMatch[1]}.${language === "typescript" || language === "tsx" ? "ts" : "js"}`
        } else {
          fileName = `script-${index + 1}.${language === "typescript" || language === "tsx" ? "ts" : "js"}`
        }
      } else if (language === "html") {
        fileName = "index.html"
      } else if (language === "css") {
        fileName = "styles.css"
      } else {
        // Default filename based on language
        fileName = `file-${index + 1}.${language}`
      }

      // Ensure unique filenames
      if (fileNames.has(fileName)) {
        let counter = 1
        let newName = fileName
        while (fileNames.has(newName)) {
          const parts = fileName.split(".")
          const ext = parts.pop()
          newName = `${parts.join(".")}-${counter}.${ext}`
          counter++
        }
        fileName = newName
      }

      fileNames.add(fileName)

      newFiles.push({
        name: fileName,
        content: content,
        language: language,
      })
    })

    if (newFiles.length > 0) {
      setGeneratedFiles(newFiles)
    }
  }

  const extractAndCreateFile = (content: string) => {
    // Extract code blocks from markdown
    const codeBlockRegex = /```(\w+)?\s*\n([\s\S]*?)\n```/g
    const matches = [...content.matchAll(codeBlockRegex)]

    if (matches.length === 0) {
      toast({
        title: "No code found",
        description: "No code blocks were found in the message.",
        variant: "destructive",
      })
      return
    }

    // Use the last code block
    const lastMatch = matches[matches.length - 1]
    const language = lastMatch[1] || "text"
    const code = lastMatch[2]

    // Map language to file extension
    const languageToExt: Record<string, string> = {
      javascript: "js",
      typescript: "ts",
      jsx: "jsx",
      tsx: "tsx",
      python: "py",
      html: "html",
      css: "css",
      json: "json",
      java: "java",
      cpp: "cpp",
      c: "c",
      csharp: "cs",
      go: "go",
      rust: "rs",
      ruby: "rb",
      php: "php",
      swift: "swift",
      kotlin: "kt",
      text: "txt",
    }

    const ext = languageToExt[language] || "txt"
    const fileName = `generated-${new Date().toISOString().slice(0, 10)}.${ext}`

    onCreateFile({
      id: uuidv4(),
      name: fileName,
      content: code,
      language,
      lastModified: new Date(),
    })

    toast({
      title: "File created",
      description: `Created ${fileName} from the code block.`,
    })
  }

  const handleApplyAllCode = () => {
    generatedFiles.forEach((file) => {
      onCreateFile({
        id: uuidv4(),
        name: file.name,
        content: file.content,
        language: file.language,
        lastModified: new Date(),
      })
    })

    toast({
      title: "Code applied",
      description: `${generatedFiles.length} files have been created in the editor`,
    })
  }

  // Handle code update from editable code blocks
  const handleCodeUpdate = (messageId: string, language: string, newCode: string) => {
    setMessages((prev) =>
      prev.map((msg) => {
        if (msg.id === messageId) {
          // Replace the code block in the message content
          const updatedContent = msg.content.replace(
            new RegExp(`\`\`\`${language}\n[\\s\\S]*?\n\`\`\``),
            `\`\`\`${language}\n${newCode}\n\`\`\``,
          )
          return { ...msg, content: updatedContent }
        }
        return msg
      }),
    )
  }

  // Load chat history from localStorage on component mount
  useEffect(() => {
    try {
      const chatIds = Object.keys(localStorage)
        .filter((key) => key.startsWith("ollama-ui-chat-"))
        .map((key) => key.replace("ollama-ui-chat-", ""))

      const loadedHistory = chatIds
        .map((id) => {
          const chatData = localStorage.getItem(`ollama-ui-chat-${id}`)
          if (!chatData) return null

          const { title, messages } = JSON.parse(chatData)
          const preview =
            messages.find((msg: Message) => msg.role === "user")?.content.substring(0, 60) + "..." || "..."

          return { id, title, preview }
        })
        .filter(Boolean)

      setChatHistory(loadedHistory)
    } catch (error) {
      console.error("Error loading chat history:", error)
    }
  }, [])

  // Add this function after the extractCodeFiles function
  const processMessageContent = (content: string): string => {
    if (!content) return content;

    // Remove code blocks
    let processedContent = content.replace(/```[\s\S]*?```/g, '');

    // Remove thinking/planning lines and sections
    processedContent = processedContent.replace(/^(Thinking|Planning|Step \d+:|I'll|Let me|First|Next|Now|Finally).*$/gm, '');

    // Remove numbered steps
    processedContent = processedContent.replace(/^\d+\.\s+.*$/gm, '');

    // Clean up extra whitespace
    processedContent = processedContent.replace(/\n{3,}/g, '\n\n');
    processedContent = processedContent.trim();

    return processedContent;
  };

  // Custom renderer for code blocks in markdown
  const components: Partial<Components> = {
    code({ node, inline, className, children, ...props }) {
      const match = /language-(\w+)/.exec(className || "");

      // Return null for code blocks (non-inline) to hide them from chat view
      // They will still be extracted for Code and Preview tabs
      if (!inline) {
        return null;
      }

      return inline ? (
        <code className={className} {...props}>
          {children}
        </code>
      ) : null;
    },
  };

  return (
    <div className="flex h-full flex-col">
      {/* Top navigation with view mode toggle */}
      <div className="border-b p-2 flex justify-between items-center bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            className="md:hidden rounded-full"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            <Bot className="h-5 w-5" />
          </Button>
          <h2 className="text-lg font-medium truncate max-w-[150px] md:max-w-full flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-green-500"></div>
            <span className="font-bold text-primary">Mash Bot</span> - {activeChatId ? chatHistory.find((chat) => chat.id === activeChatId)?.title || "Chat" : "New Chat"}
          </h2>
        </div>

        <div className="flex items-center gap-4">
          {/* View mode toggle */}
          <div className="bg-muted/30 p-1 rounded-full">
            <Button
              variant={viewMode === "chat" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("chat")}
              className="rounded-full text-xs px-3"
            >
              <Bot className="h-3 w-3 mr-1" />
              Chat
            </Button>
            <Button
              variant={viewMode === "editor" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("editor")}
              className="rounded-full text-xs px-3"
            >
              <Code className="h-3 w-3 mr-1" />
              Editor
            </Button>
          </div>

          <div className="text-xs text-muted-foreground flex items-center gap-1 bg-muted/30 px-2 py-1 rounded-full">
            <div className="w-1.5 h-1.5 rounded-full bg-blue-500"></div>
            <span className="font-medium">{selectedModel}</span>
          </div>

          {/* Diagnostics button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setActiveTab("diagnostics")}
            className="rounded-full h-8 w-8"
            title="Ollama Diagnostics"
          >
            <AlertCircle className="h-4 w-4" />
          </Button>

          {/* Theme toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => theme === "dark" ? setTheme("light") : setTheme("dark")}
            className="rounded-full h-8 w-8"
          >
            {theme === "dark" ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
          </Button>
        </div>
      </div>

      <div className="flex flex-1 h-[calc(100%-50px)] overflow-hidden">
      {/* Chat history sidebar */}
      <div
        className={cn(
          "w-full md:w-64 border-r bg-background flex-shrink-0 h-full overflow-hidden transition-all",
          isMobileMenuOpen ? "block absolute z-10 h-full w-full md:w-64 md:relative" : "hidden md:block",
        )}
      >
          <div className="p-4 border-b bg-muted/10">
          <Button onClick={startNewChat} className="w-full rounded-full">
            <Sparkles className="mr-2 h-4 w-4" />
            New Chat
          </Button>
        </div>
        <ScrollArea className="h-[calc(100%-65px)]">
          <div className="p-2 space-y-2">
            {chatHistory.length === 0 ? (
              <div className="text-center text-muted-foreground p-4">No chat history yet</div>
            ) : (
              chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  onClick={() => {
                    loadChat(chat.id)
                    setIsMobileMenuOpen(false)
                  }}
                  className={cn(
                      "p-3 rounded-xl cursor-pointer hover:bg-muted/80 flex justify-between items-start group border border-transparent",
                      activeChatId === chat.id && "bg-muted/60 border-muted-foreground/20",
                  )}
                >
                  <div className="flex-1 overflow-hidden">
                    <div className="font-medium truncate">{chat.title}</div>
                    <div className="text-xs text-muted-foreground truncate">{chat.preview}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="opacity-0 group-hover:opacity-100 h-6 w-6 rounded-full"
                    onClick={(e) => deleteChat(chat.id, e)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

        {/* Main content area */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
          {viewMode === "chat" ? (
            /* Chat view */
            <div className="flex-1 flex flex-col h-full overflow-hidden">
              <Tabs value={activeTab} onValueChange={(value) => {
                // When switching to preview tab, refresh the code preview with final code
                if (value === "preview") {
                  extractFinalCodeForPreview();
                }
                setActiveTab(value as any);
              }} className="flex-1 flex flex-col">
                <div className="border-b px-2 md:px-4 bg-muted/10">
            <TabsList className="h-10 rounded-full">
              <TabsTrigger value="chat" className="flex items-center rounded-full text-xs md:text-sm">
                <Bot className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Chat</span>
              </TabsTrigger>
              <TabsTrigger value="reasoning" className="flex items-center rounded-full text-xs md:text-sm">
                <Brain className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Reasoning</span>
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center rounded-full text-xs md:text-sm">
                <Code className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Code</span>
              </TabsTrigger>
              <TabsTrigger value="preview" className="flex items-center rounded-full text-xs md:text-sm">
                <Play className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Preview</span>
              </TabsTrigger>
              <TabsTrigger value="progress" className="flex items-center rounded-full text-xs md:text-sm">
                <FileCode className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Progress</span>
              </TabsTrigger>
              <TabsTrigger value="diagnostics" className="flex items-center rounded-full text-xs md:text-sm">
                <AlertCircle className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
                <span className="hidden sm:inline">Diagnostics</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="chat" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
                  <ScrollArea className="flex-1 p-4 bg-gradient-to-b from-background to-muted/10">
              <div className="space-y-4 max-w-3xl mx-auto flex flex-col">
                {messages.map((message) => (
                  <div
                    key={message.id}
                    className={cn("flex items-start gap-3", message.role === "user" ? "self-end" : "self-start")}
                  >
                    {message.role === "assistant" && (
                            <Avatar className="h-8 w-8 mt-1 border-2 border-primary/10">
                        <Bot className="h-5 w-5" />
                      </Avatar>
                    )}
                    <Card
                      className={cn(
                              "p-4 max-w-[80%] rounded-xl transition-all duration-200",
                              message.role === "user"
                                ? "bg-primary text-primary-foreground shadow-md"
                                : "bg-muted shadow-sm hover:shadow-md",
                              message.pending && "animate-pulse"
                      )}
                    >
                      {message.role === "assistant" ? (
                        <div className="prose prose-sm dark:prose-invert max-w-none">
                          {message.pending ? (
                                  <div className="flex flex-col gap-2">
                                    <div className="flex items-center gap-2 mb-1">
                                      <div className="flex gap-1">
                                        <div className="h-2 w-2 rounded-full bg-primary/60 animate-bounce" style={{ animationDelay: "0ms" }}></div>
                                        <div className="h-2 w-2 rounded-full bg-primary/60 animate-bounce" style={{ animationDelay: "150ms" }}></div>
                                        <div className="h-2 w-2 rounded-full bg-primary/60 animate-bounce" style={{ animationDelay: "300ms" }}></div>
                              </div>
                                      <span className="text-xs text-muted-foreground">Generating response...</span>
                                    </div>
                              {message.content && (
                                      <div className="transition-all duration-500 ease-in-out prose prose-sm dark:prose-invert">
                                        <ReactMarkdown components={components}>
                                          {processMessageContent(message.content)}
                                        </ReactMarkdown>
                                      </div>
                              )}
                            </div>
                          ) : (
                            <div className="relative group">
                                    <div className="prose prose-sm dark:prose-invert">
                                      <ReactMarkdown components={components}>
                                        {processMessageContent(message.content)}
                                      </ReactMarkdown>
                                    </div>
                              <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 rounded-full"
                                  onClick={() => {
                                    navigator.clipboard.writeText(message.content)
                                    toast({
                                      title: "Copied to clipboard",
                                      description: "Message content copied to clipboard",
                                    })
                                  }}
                                >
                                  <Copy className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-8 w-8 rounded-full"
                                  onClick={() => extractAndCreateFile(message.content)}
                                >
                                  <Code className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div className="whitespace-pre-wrap">{message.content}</div>
                      )}
                      <div
                        className={cn(
                          "text-xs mt-2",
                          message.role === "user" ? "text-primary-foreground/70" : "text-muted-foreground",
                        )}
                      >
                        {ensureDate(message.timestamp).toLocaleTimeString()}
                      </div>
                    </Card>
                    {message.role === "user" && (
                            <Avatar className="h-8 w-8 mt-1 border-2 border-primary/10">
                        <User className="h-5 w-5" />
                      </Avatar>
                    )}
                  </div>
                ))}
                <div ref={messagesEndRef} />
              </div>

              {/* Floating notification for code preview */}
              {activeTab === "chat" && (previewHtml || previewCss || previewJs) && (
                <div className="fixed bottom-24 right-4 z-50">
                  <Button
                    onClick={() => setActiveTab("preview")}
                    className="shadow-lg bg-primary text-primary-foreground hover:bg-primary/90 rounded-xl flex items-center gap-2 px-4 py-3 animate-pulse"
                  >
                    <Play className="h-4 w-4" />
                    <span>View Code Preview</span>
                  </Button>
                </div>
              )}
            </ScrollArea>

                  <div className="p-2 md:p-4 border-t bg-gradient-to-b from-muted/10 to-background">
              <div className="max-w-3xl mx-auto">
                <div className="flex gap-2">
                  <Textarea
                    ref={inputRef}
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder="Ask me to build something for you..."
                          className="min-h-[60px] resize-none rounded-xl text-sm md:text-base shadow-sm"
                    disabled={isProcessing}
                  />
                  <Button
                    onClick={handleSendMessage}
                    disabled={!input.trim() || isProcessing}
                          className="self-end rounded-full shadow-sm"
                    size="icon"
                  >
                    {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                  </Button>
                </div>
                <div className="text-xs text-muted-foreground mt-2 hidden md:block">
                  Press Enter to send, Shift+Enter for new line
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="code" className="flex-1 p-0 m-0 overflow-hidden">
            <div className="h-full p-4">
              <CodeGenerationDisplay
                files={generatedFiles}
                isGenerating={isProcessing}
                onApplyAllCode={handleApplyAllCode}
              />
            </div>
          </TabsContent>

          <TabsContent value="preview" className="flex-1 p-0 m-0 overflow-hidden">
            <div className="h-full p-4">
              <CodePreview
                html={previewHtml}
                css={previewCss}
                js={previewJs}
                isFullscreen={isPreviewFullscreen}
                onToggleFullscreen={() => setIsPreviewFullscreen(!isPreviewFullscreen)}
              />
            </div>
          </TabsContent>

          <TabsContent value="progress" className="flex-1 p-0 m-0 overflow-auto">
            <div className="p-4">
              <CodeGenerationTracker steps={generationSteps} currentStepId={currentStepId} />
            </div>
          </TabsContent>

          <TabsContent value="reasoning" className="flex-1 p-0 m-0 overflow-hidden">
            <ReasoningDisplay thinkingProcess={currentThinkingProcess} isProcessing={isProcessing} />
          </TabsContent>

          <TabsContent value="diagnostics" className="flex-1 p-0 m-0 overflow-hidden">
            <MashBotDiagnostics
              selectedModel={selectedModel}
              onClose={() => setActiveTab("chat")}
            />
          </TabsContent>
        </Tabs>
            </div>
          ) : (
            /* Editor view */
            <div className="flex-1 grid grid-cols-1 md:grid-cols-2 h-full overflow-hidden">
              <div className="h-full overflow-auto border-r">
                <div className="p-4 h-full">
                  {generatedFiles.length > 0 ? (
                    <div className="space-y-4">
                      {generatedFiles.map((file) => (
                        <EditableCodeBlock
                          key={file.name}
                          language={file.language}
                          code={file.content}
                          onCodeChange={(newCode) => {
                            // Update the file content when code changes
                            setGeneratedFiles(prev =>
                              prev.map(f => f.name === file.name ? {...f, content: newCode} : f)
                            )
                          }}
                          onSave={(newCode) => {
                            // Apply changes to both generated files and preview
                            setGeneratedFiles(prev =>
                              prev.map(f => f.name === file.name ? {...f, content: newCode} : f)
                            )

                            // Update preview if this is HTML/CSS/JS file
                            if (file.language === 'html') {
                              setPreviewHtml(newCode)
                            } else if (file.language === 'css') {
                              setPreviewCss(newCode)
                            } else if (file.language === 'javascript' || file.language === 'js') {
                              setPreviewJs(newCode)
                            }

                            toast({
                              title: "Code saved",
                              description: `Changes to ${file.name} have been saved and applied to preview`,
                            })
                          }}
                          onRun={() => {
                            // Update preview with current code
                            if (file.language === 'html') {
                              setPreviewHtml(file.content)
                            } else if (file.language === 'css') {
                              setPreviewCss(file.content)
                            } else if (file.language === 'javascript' || file.language === 'js') {
                              setPreviewJs(file.content)
                            }
                          }}
                        />
                      ))}
                      <Button
                        onClick={handleApplyAllCode}
                        className="w-full rounded-lg mt-4"
                      >
                        Apply All Code to Editor
                      </Button>
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center p-6 border rounded-lg bg-muted/10">
                        <Code className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                        <h3 className="text-lg font-medium">No code generated yet</h3>
                        <p className="text-sm text-muted-foreground mt-2 mb-4">
                          Ask the AI to generate some code in the chat interface, and it will appear here for editing.
                        </p>
                        <Button onClick={() => setViewMode("chat")} className="rounded-lg">
                          Go to Chat
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
              <div className="h-full overflow-hidden bg-muted/10">
                <div className="p-4 h-full">
                  <CodePreview
                    html={previewHtml}
                    css={previewCss}
                    js={previewJs}
                    isFullscreen={isPreviewFullscreen}
                    onToggleFullscreen={() => setIsPreviewFullscreen(!isPreviewFullscreen)}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

