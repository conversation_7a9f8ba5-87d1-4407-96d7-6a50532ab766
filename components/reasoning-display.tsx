"use client"

import { useState } from "react"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { ThinkingProcess, ThinkingStage } from "@/lib/reasoning-engine"
import { TaskType } from "@/lib/task-manager"
import { 
  Brain, 
  CheckCircle2, 
  ChevronDown, 
  ChevronRight, 
  CircleDashed, 
  CircleDot, 
  Code2, 
  Database, 
  FileCode, 
  Lightbulb, 
  ListChecks, 
  MessageSquare, 
  Server, 
  Settings, 
  Sparkles 
} from "lucide-react"
import { cn } from "@/lib/utils"

interface ReasoningDisplayProps {
  thinkingProcess?: ThinkingProcess
  isProcessing: boolean
}

export default function ReasoningDisplay({ thinkingProcess, isProcessing }: ReasoningDisplayProps) {
  const [expandedSteps, setExpandedSteps] = useState<Record<string, boolean>>({})
  const [activeTab, setActiveTab] = useState<"reasoning" | "plan" | "summary">("reasoning")

  // Toggle a step's expanded state
  const toggleStep = (stage: string) => {
    setExpandedSteps(prev => ({
      ...prev,
      [stage]: !prev[stage]
    }))
  }

  // Get icon for thinking stage
  const getStageIcon = (stage: ThinkingStage) => {
    switch (stage) {
      case ThinkingStage.IntentUnderstanding:
        return <Lightbulb className="h-4 w-4" />
      case ThinkingStage.TaskClassification:
        return <ListChecks className="h-4 w-4" />
      case ThinkingStage.ExecutionPlanning:
        return <FileCode className="h-4 w-4" />
      case ThinkingStage.ResponseFormulation:
        return <MessageSquare className="h-4 w-4" />
      case ThinkingStage.ErrorRecovery:
        return <Settings className="h-4 w-4" />
      default:
        return <CircleDot className="h-4 w-4" />
    }
  }

  // Get icon for task type
  const getTaskTypeIcon = (taskType: TaskType) => {
    switch (taskType) {
      case TaskType.Conversation:
        return <MessageSquare className="h-4 w-4" />
      case TaskType.DatasetManagement:
        return <Database className="h-4 w-4" />
      case TaskType.ModelTraining:
        return <Brain className="h-4 w-4" />
      case TaskType.ModelDeployment:
        return <Server className="h-4 w-4" />
      case TaskType.ModelInference:
        return <Sparkles className="h-4 w-4" />
      case TaskType.SystemCheck:
        return <Settings className="h-4 w-4" />
      default:
        return <CircleDashed className="h-4 w-4" />
    }
  }

  // Get human-readable name for thinking stage
  const getStageName = (stage: ThinkingStage): string => {
    switch (stage) {
      case ThinkingStage.IntentUnderstanding:
        return "Understanding Intent"
      case ThinkingStage.TaskClassification:
        return "Classifying Task"
      case ThinkingStage.ExecutionPlanning:
        return "Planning Execution"
      case ThinkingStage.ResponseFormulation:
        return "Formulating Response"
      case ThinkingStage.ErrorRecovery:
        return "Error Recovery"
      default:
        return stage
    }
  }

  // Get human-readable name for task type
  const getTaskTypeName = (taskType: TaskType): string => {
    switch (taskType) {
      case TaskType.Conversation:
        return "General Conversation"
      case TaskType.DatasetManagement:
        return "Dataset Management"
      case TaskType.ModelTraining:
        return "Model Training"
      case TaskType.ModelDeployment:
        return "Model Deployment"
      case TaskType.ModelInference:
        return "Model Inference"
      case TaskType.SystemCheck:
        return "System Check"
      default:
        return "Unknown Task"
    }
  }

  // Calculate overall progress percentage
  const calculateProgress = (): number => {
    if (!thinkingProcess || thinkingProcess.steps.length === 0) return 0
    
    const totalSteps = 4 // Intent, Classification, Planning, Response
    const completedSteps = thinkingProcess.steps.length
    
    return Math.min(Math.round((completedSteps / totalSteps) * 100), 100)
  }

  // Generate a summary of the thinking process
  const generateSummary = (): string => {
    if (!thinkingProcess) return "No thinking process available."
    
    let summary = `The AI analyzed your request "${thinkingProcess.userMessage.substring(0, 50)}${thinkingProcess.userMessage.length > 50 ? '...' : ''}" and identified it as a ${getTaskTypeName(thinkingProcess.taskType)} task.\n\n`
    
    // Add intent understanding if available
    const intentStep = thinkingProcess.steps.find(step => step.stage === ThinkingStage.IntentUnderstanding)
    if (intentStep) {
      // Extract a concise version of the intent (first 1-2 sentences)
      const intentText = intentStep.reasoning.split('.')[0] + '.'
      summary += `Intent: ${intentText}\n\n`
    }
    
    // Add task classification if available
    const classificationStep = thinkingProcess.steps.find(step => step.stage === ThinkingStage.TaskClassification)
    if (classificationStep) {
      // Extract the classification decision
      const classificationLines = classificationStep.reasoning.split('\n')
      const classificationText = classificationLines.find(line => 
        line.toLowerCase().includes('classify') || 
        line.toLowerCase().includes('category') ||
        line.toLowerCase().includes('task type')
      ) || classificationLines[classificationLines.length - 1]
      
      summary += `Classification: ${classificationText}\n\n`
    }
    
    // Add execution plan if available
    const planStep = thinkingProcess.steps.find(step => step.stage === ThinkingStage.ExecutionPlanning)
    if (planStep) {
      summary += `Execution Plan: The AI created a ${planStep.reasoning.length} character plan to fulfill your request.\n\n`
    }
    
    // Add confidence information if available
    if (thinkingProcess.confidence !== undefined) {
      const confidencePercent = Math.round(thinkingProcess.confidence * 100)
      summary += `Confidence: ${confidencePercent}% confident in understanding and executing your request.`
    }
    
    return summary
  }

  if (!thinkingProcess) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center p-6 border rounded-lg bg-muted/10">
          <Brain className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium">No reasoning data available</h3>
          <p className="text-sm text-muted-foreground mt-2">
            Ask a question to see the AI's reasoning process
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col">
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
        <div className="border-b px-4 bg-muted/10">
          <TabsList className="h-10 rounded-full">
            <TabsTrigger value="reasoning" className="flex items-center rounded-full text-xs md:text-sm">
              <Brain className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Reasoning</span>
            </TabsTrigger>
            <TabsTrigger value="plan" className="flex items-center rounded-full text-xs md:text-sm">
              <FileCode className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Plan</span>
            </TabsTrigger>
            <TabsTrigger value="summary" className="flex items-center rounded-full text-xs md:text-sm">
              <ListChecks className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Summary</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="reasoning" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              {/* Progress indicator */}
              <div className="mb-6">
                <div className="flex justify-between items-center mb-2">
                  <div className="text-sm font-medium">Reasoning Progress</div>
                  <div className="text-sm text-muted-foreground">{calculateProgress()}%</div>
                </div>
                <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-primary transition-all duration-500 ease-out"
                    style={{ width: `${calculateProgress()}%` }}
                  ></div>
                </div>
              </div>

              {/* Task classification result */}
              <Card className="p-4 border-primary/20 bg-primary/5">
                <div className="flex items-center gap-2 text-sm font-medium mb-2">
                  <div className="flex items-center gap-2">
                    {getTaskTypeIcon(thinkingProcess.taskType)}
                    <span>Task Type: {getTaskTypeName(thinkingProcess.taskType)}</span>
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  This request has been classified as a {getTaskTypeName(thinkingProcess.taskType).toLowerCase()} task.
                </div>
              </Card>

              {/* Thinking steps */}
              <div className="space-y-3">
                {thinkingProcess.steps.map((step, index) => (
                  <Card 
                    key={index} 
                    className={cn(
                      "border overflow-hidden transition-all duration-200",
                      expandedSteps[step.stage] ? "shadow-md" : "shadow-sm"
                    )}
                  >
                    <div 
                      className="p-3 flex justify-between items-center cursor-pointer hover:bg-muted/50"
                      onClick={() => toggleStep(step.stage)}
                    >
                      <div className="flex items-center gap-2">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-primary/10 text-primary">
                          {getStageIcon(step.stage)}
                        </div>
                        <div className="font-medium text-sm">{getStageName(step.stage)}</div>
                        {step.confidence !== undefined && (
                          <div className="text-xs px-2 py-0.5 rounded-full bg-muted">
                            {Math.round(step.confidence * 100)}% confidence
                          </div>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="text-xs text-muted-foreground">
                          {step.reasoning.length} chars
                        </div>
                        {expandedSteps[step.stage] ? (
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        ) : (
                          <ChevronRight className="h-4 w-4 text-muted-foreground" />
                        )}
                      </div>
                    </div>
                    
                    {expandedSteps[step.stage] && (
                      <div className="px-4 pb-4 pt-2 border-t bg-muted/5">
                        <div className="text-xs text-muted-foreground mb-2">
                          Prompt: {step.prompt.substring(0, 100)}{step.prompt.length > 100 ? '...' : ''}
                        </div>
                        <div className="whitespace-pre-wrap text-sm bg-muted/10 p-3 rounded-md">
                          {step.reasoning}
                        </div>
                      </div>
                    )}
                  </Card>
                ))}
                
                {/* Show pending steps if still processing */}
                {isProcessing && (
                  <Card className="border border-dashed p-3">
                    <div className="flex items-center gap-2">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-muted/30">
                        <CircleDashed className="h-4 w-4 animate-spin" />
                      </div>
                      <div className="font-medium text-sm text-muted-foreground">Processing...</div>
                    </div>
                  </Card>
                )}
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="plan" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              {/* Task plan from execution planning step */}
              {thinkingProcess.taskPlan ? (
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <FileCode className="h-5 w-5 text-primary" />
                    <h3 className="font-medium">Execution Plan</h3>
                  </div>
                  <div className="whitespace-pre-wrap text-sm">
                    {thinkingProcess.taskPlan}
                  </div>
                </Card>
              ) : (
                <Card className="p-4 text-center">
                  <div className="flex flex-col items-center gap-2 mb-3">
                    <FileCode className="h-8 w-8 text-muted-foreground" />
                    <h3 className="font-medium">No detailed plan available</h3>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {isProcessing 
                      ? "The AI is still working on creating a plan..." 
                      : "No execution plan was generated for this request."}
                  </div>
                </Card>
              )}

              {/* Execution steps based on the plan */}
              {thinkingProcess.taskPlan && (
                <div className="mt-6">
                  <h3 className="text-sm font-medium mb-3">Execution Steps</h3>
                  <div className="space-y-2">
                    {thinkingProcess.taskPlan.split('\n')
                      .filter(line => line.trim().length > 0)
                      .filter(line => /^\d+\./.test(line) || line.toLowerCase().includes('step'))
                      .map((step, index) => (
                        <div key={index} className="flex items-start gap-2 p-2 rounded-md bg-muted/10">
                          <div className="flex-shrink-0 mt-0.5">
                            {isProcessing && index === 0 ? (
                              <CircleDashed className="h-4 w-4 text-primary animate-spin" />
                            ) : index < 2 ? (
                              <CheckCircle2 className="h-4 w-4 text-primary" />
                            ) : (
                              <CircleDot className="h-4 w-4 text-muted-foreground" />
                            )}
                          </div>
                          <div className="text-sm">{step}</div>
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="summary" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <Brain className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">Reasoning Summary</h3>
                </div>
                <div className="whitespace-pre-wrap text-sm">
                  {generateSummary()}
                </div>
              </Card>

              {/* Confidence score */}
              {thinkingProcess.confidence !== undefined && (
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Sparkles className="h-5 w-5 text-primary" />
                    <h3 className="font-medium">Confidence Assessment</h3>
                  </div>
                  <div className="mb-3">
                    <div className="flex justify-between items-center mb-2">
                      <div className="text-sm">Overall Confidence</div>
                      <div className="text-sm font-medium">{Math.round(thinkingProcess.confidence * 100)}%</div>
                    </div>
                    <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
                      <div 
                        className={cn(
                          "h-full transition-all duration-500 ease-out",
                          thinkingProcess.confidence < 0.4 ? "bg-red-500" :
                          thinkingProcess.confidence < 0.7 ? "bg-yellow-500" : "bg-green-500"
                        )}
                        style={{ width: `${Math.round(thinkingProcess.confidence * 100)}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {thinkingProcess.confidence < 0.4 
                      ? "Low confidence. The AI may not fully understand your request or may need more information."
                      : thinkingProcess.confidence < 0.7
                      ? "Moderate confidence. The AI understands the general intent but may miss some details."
                      : "High confidence. The AI understands your request well and can provide a reliable response."}
                  </div>
                </Card>
              )}

              {/* Duration information */}
              {thinkingProcess.duration !== undefined && (
                <div className="text-xs text-muted-foreground text-center">
                  Reasoning completed in {(thinkingProcess.duration / 1000).toFixed(2)} seconds
                </div>
              )}
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
