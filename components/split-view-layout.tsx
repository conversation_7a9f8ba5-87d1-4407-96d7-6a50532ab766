"use client"

import type { ReactNode } from "react"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"

interface SplitViewLayoutProps {
  chatPanel: ReactNode
  codePanel: ReactNode
}

export function SplitViewLayout({ chatPanel, codePanel }: SplitViewLayoutProps) {
  return (
    <div className="h-screen flex flex-col">
      <ResizablePanelGroup direction="horizontal" className="flex-1">
        <ResizablePanel defaultSize={40} minSize={30}>
          {chatPanel}
        </ResizablePanel>
        <ResizableHandle withHandle />
        <ResizablePanel defaultSize={60} minSize={30}>
          {codePanel}
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}

