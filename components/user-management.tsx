"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import { Users, UserPlus, ChevronDown } from "lucide-react"
import { v4 as uuidv4 } from "uuid"

export interface UserProfile {
  id: string
  name: string
  avatar?: string
  createdAt: Date | string
  lastActive: Date | string
}

interface UserManagementProps {
  currentUser: UserProfile | null
  onUserChange: (user: UserProfile) => void
}

export function UserManagement({ currentUser, onUserChange }: UserManagementProps) {
  const [users, setUsers] = useState<UserProfile[]>([])
  const [newUserName, setNewUserName] = useState("")
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const { toast } = useToast()
  const [isClient, setIsClient] = useState(false)

  // Set isClient to true once component mounts
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Load users from localStorage
  useEffect(() => {
    if (!isClient) return

    try {
      const storedUsers = localStorage.getItem("nero-dev-users")
      if (storedUsers) {
        const parsedUsers = JSON.parse(storedUsers)
        if (Array.isArray(parsedUsers) && parsedUsers.length > 0) {
          setUsers(parsedUsers)

          // If no current user is set, use the first user
          if (!currentUser && parsedUsers.length > 0) {
            onUserChange(parsedUsers[0])
          }
        } else {
          createDefaultUser()
        }
      } else {
        createDefaultUser()
      }
    } catch (error) {
      console.error("Error loading users:", error)
      createDefaultUser()
    }
  }, [isClient, currentUser, onUserChange])

  // Add this function to create a default user
  const createDefaultUser = () => {
    const defaultUser: UserProfile = {
      id: uuidv4(),
      name: "Default User",
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString(),
    }
    setUsers([defaultUser])

    try {
      localStorage.setItem("nero-dev-users", JSON.stringify([defaultUser]))
      onUserChange(defaultUser)
    } catch (error) {
      console.error("Error creating default user:", error)
    }
  }

  const createUser = () => {
    if (!newUserName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a user name",
        variant: "destructive",
      })
      return
    }

    const newUser: UserProfile = {
      id: uuidv4(),
      name: newUserName.trim(),
      createdAt: new Date().toISOString(),
      lastActive: new Date().toISOString(),
    }

    const updatedUsers = [...users, newUser]
    setUsers(updatedUsers)
    localStorage.setItem("nero-dev-users", JSON.stringify(updatedUsers))

    onUserChange(newUser)
    setNewUserName("")
    setIsCreateDialogOpen(false)

    toast({
      title: "User created",
      description: `User "${newUserName}" has been created and selected`,
    })
  }

  const switchUser = (user: UserProfile) => {
    // Update last active timestamp
    const updatedUser = {
      ...user,
      lastActive: new Date().toISOString(),
    }

    // Update the user in the list
    const updatedUsers = users.map((u) => (u.id === user.id ? updatedUser : u))

    setUsers(updatedUsers)
    localStorage.setItem("nero-dev-users", JSON.stringify(updatedUsers))

    onUserChange(updatedUser)

    toast({
      title: "User switched",
      description: `Switched to user "${user.name}"`,
    })
  }

  // Format date for display
  const formatDate = (dateString: Date | string): string => {
    const date = typeof dateString === "string" ? new Date(dateString) : dateString
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Get initials for avatar
  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  if (!isClient) {
    return null
  }

  return (
    <div className="flex items-center">
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="flex items-center gap-2 rounded-full">
            <Avatar className="h-6 w-6">
              {currentUser?.avatar ? (
                <AvatarImage src={currentUser.avatar} alt={currentUser?.name} />
              ) : (
                <AvatarFallback>{currentUser?.name ? getInitials(currentUser.name) : "U"}</AvatarFallback>
              )}
            </Avatar>
            <span className="max-w-[100px] truncate">{currentUser?.name || "Select User"}</span>
            <ChevronDown className="h-4 w-4 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[200px]">
          <DropdownMenuLabel>
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              <span>Users</span>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          {users.map((user) => (
            <DropdownMenuItem
              key={user.id}
              onClick={() => switchUser(user)}
              className="flex items-center gap-2 cursor-pointer"
            >
              <Avatar className="h-6 w-6">
                {user.avatar ? (
                  <AvatarImage src={user.avatar} alt={user.name} />
                ) : (
                  <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                )}
              </Avatar>
              <div className="flex-1 overflow-hidden">
                <div className="font-medium truncate">{user.name}</div>
                <div className="text-xs text-muted-foreground">Active: {formatDate(user.lastActive)}</div>
              </div>
              {currentUser?.id === user.id && <div className="h-2 w-2 rounded-full bg-green-500"></div>}
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <DropdownMenuItem onSelect={(e) => e.preventDefault()} className="flex items-center gap-2 cursor-pointer">
                <UserPlus className="h-4 w-4" />
                <span>Create New User</span>
              </DropdownMenuItem>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px] rounded-xl">
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
                <DialogDescription>
                  Create a new user profile to manage separate workspaces and chat histories.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label className="text-right">Name</Label>
                  <Input
                    id="name"
                    value={newUserName}
                    onChange={(e) => setNewUserName(e.target.value)}
                    className="col-span-3 rounded-full"
                    placeholder="Enter user name"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" onClick={createUser} className="rounded-full">
                  Create User
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  )
}

// Helper component for labels
function Label({ className, ...props }: React.HTMLAttributes<HTMLLabelElement>) {
  return (
    <label
      className={`text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 ${className}`}
      {...props}
    />
  )
}

