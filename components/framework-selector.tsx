"use client"

import { Check, ChevronDown } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface FrameworkSelectorProps {
  selectedFramework: string
  setSelectedFramework: (framework: string) => void
}

export function FrameworkSelector({ selectedFramework, setSelectedFramework }: FrameworkSelectorProps) {
  const frameworks = [
    { id: "react", name: "React" },
    { id: "nextjs", name: "Next.js" },
    { id: "vue", name: "Vue.js" },
    { id: "bootstrap", name: "Bootstrap" },
    { id: "flutter", name: "Flutter" },
  ]

  const selectedFrameworkName = frameworks.find((f) => f.id === selectedFramework)?.name || "Select Framework"

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-1">
          {selectedFrameworkName}
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuGroup>
          {frameworks.map((framework) => (
            <DropdownMenuItem
              key={framework.id}
              onClick={() => setSelectedFramework(framework.id)}
              className="flex items-center justify-between"
            >
              {framework.name}
              {selectedFramework === framework.id && <Check className="h-4 w-4" />}
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

