"use client"

import { But<PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Check, ChevronDown } from "lucide-react"
import { cn } from "@/lib/utils"

interface ModelSelectorProps {
  selectedModel: string
  setSelectedModel: (model: string) => void
  availableModels: string[]
}

export default function ModelSelector({ selectedModel, setSelectedModel, availableModels }: ModelSelectorProps) {
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="w-[180px] justify-between rounded-full">
          {selectedModel || "Select model"}
          <ChevronDown className="ml-2 h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[180px] max-h-[300px] overflow-y-auto">
        {availableModels.length === 0 ? (
          <div className="px-2 py-1.5 text-sm text-muted-foreground text-center">No models found</div>
        ) : (
          availableModels.map((model) => (
            <DropdownMenuItem
              key={model}
              onClick={() => setSelectedModel(model)}
              className={cn("flex items-center justify-between", selectedModel === model && "font-medium")}
            >
              {model}
              {selectedModel === model && <Check className="ml-2 h-4 w-4" />}
            </DropdownMenuItem>
          ))
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

