"use client"

import React, { useState, useEffect } from "react"
import { ResizablePanel, ResizablePanelGroup, ResizableHandle } from "@/components/ui/resizable"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { PlusCircle, Save, Trash2, FileCode, Settings } from "lucide-react"
import { useTheme } from "next-themes"
import dynamic from 'next/dynamic'
import { formatCode } from "@/lib/code-formatter"
import type * as Monaco from "monaco-editor"

// Dynamically import Monaco editor with SSR disabled
const MonacoEditor = dynamic(
  () => import('@monaco-editor/react'),
  { ssr: false }
);

// Dynamically import monaco-editor with SSR disabled
const monaco = dynamic(
  () => import('monaco-editor').then((mod) => mod),
  { ssr: false }
) as typeof Monaco;

interface EditorInterfaceProps {
  files: any[]
  setFiles: (files: any[]) => void
  settings: any
  onSettingsChange: (settings: any) => void
}

export default function EditorInterface({ files, setFiles, settings, onSettingsChange }: EditorInterfaceProps) {
  const [activeFile, setActiveFile] = useState<string | null>(null)
  const [editorContent, setEditorContent] = useState("")
  const [editorRef, setEditorRef] = useState<any>(null)
  const { toast } = useToast()
  const { theme } = useTheme()

  // Set the first file as active when files change
  useEffect(() => {
    if (files.length > 0 && !activeFile) {
      setActiveFile(files[0].name)
      setEditorContent(files[0].content)
    } else if (files.length === 0) {
      setActiveFile(null)
      setEditorContent("")
    }
  }, [files, activeFile])

  // Handle file selection
  const handleFileSelect = (fileName: string) => {
    const file = files.find(f => f.name === fileName)
    if (file) {
      setActiveFile(fileName)
      setEditorContent(file.content)
    }
  }

  // Handle editor content change
  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      setEditorContent(value)
      
      // Auto-save if enabled
      if (settings.autoSave && activeFile) {
        saveCurrentFile()
      }
    }
  }

  // Save current file
  const saveCurrentFile = () => {
    if (!activeFile) return
    
    const updatedFiles = files.map(file => 
      file.name === activeFile ? { ...file, content: editorContent } : file
    )
    
    setFiles(updatedFiles)
    
    toast({
      title: "File saved",
      description: `${activeFile} has been saved`,
    })
  }

  // Delete file
  const deleteFile = (fileName: string) => {
    const updatedFiles = files.filter(file => file.name !== fileName)
    setFiles(updatedFiles)
    
    if (activeFile === fileName) {
      setActiveFile(updatedFiles.length > 0 ? updatedFiles[0].name : null)
      setEditorContent(updatedFiles.length > 0 ? updatedFiles[0].content : "")
    }
    
    toast({
      title: "File deleted",
      description: `${fileName} has been deleted`,
    })
  }

  // Format code
  const formatCurrentCode = async () => {
    if (!activeFile) return
    
    const currentFile = files.find(f => f.name === activeFile)
    if (!currentFile) return
    
    try {
      const language = currentFile.language || "javascript"
      const formatted = await formatCode(editorContent, language)
      
      setEditorContent(formatted)
      
      // Update the editor content
      if (editorRef) {
        editorRef.setValue(formatted)
      }
      
      // Save the formatted code
      const updatedFiles = files.map(file => 
        file.name === activeFile ? { ...file, content: formatted } : file
      )
      
      setFiles(updatedFiles)
      
      toast({
        title: "Code formatted",
        description: `${activeFile} has been formatted`,
      })
    } catch (error) {
      toast({
        title: "Format error",
        description: `Error formatting code: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      })
    }
  }

  // Create a new file
  const createNewFile = () => {
    const newFileName = `newfile-${files.length + 1}.js`
    const newFile = {
      name: newFileName,
      content: "// Write your code here\n",
      language: "javascript"
    }
    
    setFiles([...files, newFile])
    setActiveFile(newFileName)
    setEditorContent(newFile.content)
    
    toast({
      title: "New file created",
      description: `${newFileName} has been created`,
    })
  }

  // Handle editor mount
  const handleEditorDidMount = (editor: any) => {
    setEditorRef(editor)
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.CtrlCmd | monaco.KeyCode.KeyS, () => {
      saveCurrentFile()
    })
    
    editor.addCommand(monaco.KeyMod.Alt | monaco.KeyCode.KeyF, () => {
      formatCurrentCode()
    })
  }

  return (
    <div className="h-full">
      <ResizablePanelGroup direction="horizontal">
        {/* File Explorer Panel */}
        <ResizablePanel defaultSize={20} minSize={15} maxSize={30}>
          <Card className="h-full border-0 rounded-none shadow-none">
            <CardHeader className="px-3 py-2 border-b">
              <div className="flex justify-between items-center">
                <CardTitle className="text-sm font-medium">Files</CardTitle>
                <Button variant="ghost" size="icon" onClick={createNewFile} title="New File">
                  <PlusCircle className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-2 overflow-auto">
              {files.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileCode className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No files available</p>
                  <Button 
                    variant="link" 
                    size="sm" 
                    onClick={createNewFile}
                    className="mt-2"
                  >
                    Create new file
                  </Button>
                </div>
              ) : (
                <ul className="space-y-1">
                  {files.map((file) => (
                    <li key={file.name}>
                      <div 
                        className={`
                          flex items-center justify-between px-2 py-1.5 rounded text-sm cursor-pointer
                          ${activeFile === file.name ? 'bg-secondary' : 'hover:bg-secondary/50'}
                        `}
                        onClick={() => handleFileSelect(file.name)}
                      >
                        <span className="truncate flex-1">{file.name}</span>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-6 w-6 opacity-50 hover:opacity-100"
                          onClick={(e) => {
                            e.stopPropagation();
                            deleteFile(file.name);
                          }}
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </Button>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </CardContent>
          </Card>
        </ResizablePanel>
        
        <ResizableHandle withHandle />
        
        {/* Editor Panel */}
        <ResizablePanel defaultSize={80}>
          <Card className="h-full border-0 rounded-none shadow-none">
            <CardHeader className="px-4 py-2 border-b">
              <div className="flex justify-between items-center">
                <CardTitle className="text-sm font-medium">
                  {activeFile || "No file selected"}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={formatCurrentCode}
                    disabled={!activeFile}
                    title="Format Code (Alt+F)"
                  >
                    <Settings className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={saveCurrentFile}
                    disabled={!activeFile}
                    title="Save File (Ctrl+S)"
                  >
                    <Save className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent className="p-0 h-[calc(100%-45px)]">
              {activeFile ? (
                <MonacoEditor
                  height="100%"
                  language={files.find(f => f.name === activeFile)?.language || "javascript"}
                  value={editorContent}
                  theme={theme === "dark" ? "vs-dark" : "light"}
                  onChange={handleEditorChange}
                  onMount={handleEditorDidMount}
                  options={{
                    minimap: { enabled: settings.minimap },
                    fontSize: settings.fontSize,
                    wordWrap: settings.wordWrap ? "on" : "off",
                    lineNumbers: settings.showLineNumbers ? "on" : "off",
                    renderIndentGuides: settings.showIndentGuides,
                    renderWhitespace: settings.showInvisibles ? "all" : "none",
                    tabSize: settings.tabSize,
                    automaticLayout: true,
                    scrollBeyondLastLine: false,
                    smoothScrolling: settings.smoothScrolling,
                    bracketPairColorization: { enabled: true },
                    autoClosingBrackets: settings.autoClosingBrackets ? "always" : "never",
                    autoIndent: settings.autoIndent ? "full" : "none",
                    quickSuggestions: settings.quickSuggestions,
                    guides: {
                      bracketPairs: true,
                      indentation: settings.showIndentGuides,
                    },
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center">
                    <FileCode className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <h3 className="text-lg font-medium mb-2">No file selected</h3>
                    <p className="text-muted-foreground mb-4">
                      Create a new file or select an existing one to get started
                    </p>
                    <Button onClick={createNewFile}>
                      Create new file
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </ResizablePanel>
      </ResizablePanelGroup>
    </div>
  )
}

