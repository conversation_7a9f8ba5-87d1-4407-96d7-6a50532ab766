"use client"

import { useState } from "react"
import { Check, Palette } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"

type ThemeColor = {
  name: string
  value: string
  foreground: string
  background: string
}

const themes: ThemeColor[] = [
  {
    name: "Slate",
    value: "slate",
    foreground: "#f8fafc",
    background: "#0f172a",
  },
  {
    name: "Purple",
    value: "purple",
    foreground: "#faf5ff",
    background: "#581c87",
  },
  {
    name: "Indigo",
    value: "indigo",
    foreground: "#eef2ff",
    background: "#3730a3",
  },
  {
    name: "<PERSON>",
    value: "rose",
    foreground: "#fff1f2",
    background: "#9f1239",
  },
  {
    name: "Green",
    value: "green",
    foreground: "#f0fdf4",
    background: "#166534",
  },
  {
    name: "Orange",
    value: "orange",
    foreground: "#fff7ed",
    background: "#9a3412",
  },
  {
    name: "Cyan",
    value: "cyan",
    foreground: "#ecfeff",
    background: "#155e75",
  },
  {
    name: "Pink",
    value: "pink",
    foreground: "#fdf2f8",
    background: "#9d174d",
  },
]

interface ThemeSelectorProps {
  currentTheme: string
  onThemeChange: (theme: string) => void
}

export function ThemeSelector({ currentTheme, onThemeChange }: ThemeSelectorProps) {
  const [open, setOpen] = useState(false)

  const selectedTheme = themes.find((theme) => theme.value === currentTheme) || themes[0]

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <DropdownMenu open={open} onOpenChange={setOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon" className="rounded-full">
                <Palette className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[180px] p-2">
              <div className="text-xs font-medium text-muted-foreground mb-2 px-2">Theme Colors</div>
              <div className="grid grid-cols-4 gap-1 mb-2">
                {themes.map((theme) => (
                  <button
                    key={theme.value}
                    className={cn(
                      "h-8 w-8 rounded-full flex items-center justify-center cursor-pointer transition-all",
                      currentTheme === theme.value ? "ring-2 ring-primary ring-offset-2" : "hover:scale-110",
                    )}
                    style={{ backgroundColor: theme.background }}
                    onClick={() => {
                      onThemeChange(theme.value)
                      setOpen(false)
                    }}
                    title={theme.name}
                  >
                    {currentTheme === theme.value && <Check className="h-4 w-4 text-white" />}
                  </button>
                ))}
              </div>
              <div className="text-xs text-muted-foreground px-2">Current: {selectedTheme.name}</div>
            </DropdownMenuContent>
          </DropdownMenu>
        </TooltipTrigger>
        <TooltipContent>
          <p>Change theme color</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

