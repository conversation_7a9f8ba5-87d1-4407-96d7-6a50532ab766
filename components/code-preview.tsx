"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Maximize2, Minimize2, RefreshCw } from "lucide-react"
import { cn } from "@/lib/utils"

interface CodePreviewProps {
  html: string
  css: string
  js: string
  isFullscreen: boolean
  onToggleFullscreen: () => void
}

export default function CodePreview({ html, css, js, isFullscreen, onToggleFullscreen }: CodePreviewProps) {
  const [activeTab, setActiveTab] = useState<"preview" | "html" | "css" | "js">("preview")
  const [isLoading, setIsLoading] = useState(false)
  const iframeRef = useRef<HTMLIFrameElement>(null)

  const refreshPreview = () => {
    setIsLoading(true)
    if (iframeRef.current) {
      const iframe = iframeRef.current
      const doc = iframe.contentDocument || iframe.contentWindow?.document

      if (doc) {
        doc.open()
        doc.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1">
              <style>${css}</style>
            </head>
            <body>
              ${html}
              <script>${js}</script>
            </body>
          </html>
        `)
        doc.close()
      }
    }
    setIsLoading(false)
  }

  // Refresh preview when code changes
  useEffect(() => {
    refreshPreview()
  }, [html, css, js])

  return (
    <Card className={cn("h-full flex flex-col overflow-hidden border-2", 
      isFullscreen && "fixed inset-0 z-50 rounded-none",
      "shadow-lg hover:shadow-xl transition-shadow duration-300"
    )}>
      <CardHeader className="p-2 md:p-4 flex-shrink-0 border-b bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="flex justify-between items-center">
          <div className="flex flex-col">
            <CardTitle className="text-base md:text-lg flex items-center gap-2">
              <div className={cn(
                "w-2 h-2 rounded-full transition-colors", 
                isLoading ? "bg-amber-500" : "bg-green-500"
              )}></div>
              Code Preview
              {isLoading && <span className="text-xs font-normal text-muted-foreground ml-2 animate-pulse">Refreshing...</span>}
            </CardTitle>
            <p className="text-xs text-muted-foreground">Showing final generated code</p>
          </div>
          <div className="flex gap-1 md:gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={refreshPreview}
              disabled={isLoading}
              className="h-7 w-7 md:h-8 md:w-8 p-0 hover:bg-muted/80 transition-all relative overflow-hidden"
            >
              <RefreshCw className={cn(
                "h-3 w-3 md:h-4 md:w-4 transition-all", 
                isLoading && "animate-spin text-amber-500"
              )} />
              {isLoading && (
                <span className="absolute inset-0 bg-primary/5 animate-pulse rounded-full"></span>
              )}
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              onClick={onToggleFullscreen} 
              className="h-7 w-7 md:h-8 md:w-8 p-0 hover:bg-muted/80 transition-all"
            >
              {isFullscreen ? (
                <Minimize2 className="h-3 w-3 md:h-4 md:w-4" />
              ) : (
                <Maximize2 className="h-3 w-3 md:h-4 md:w-4" />
              )}
            </Button>
          </div>
        </div>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="mt-2">
          <TabsList className="h-8 md:h-9 p-1 bg-muted/30 rounded-lg">
            <TabsTrigger value="preview" className="text-xs md:text-sm rounded-md">
              Preview
            </TabsTrigger>
            <TabsTrigger value="html" className="text-xs md:text-sm rounded-md">
              HTML
            </TabsTrigger>
            <TabsTrigger value="css" className="text-xs md:text-sm rounded-md">
              CSS
            </TabsTrigger>
            <TabsTrigger value="js" className="text-xs md:text-sm rounded-md">
              JavaScript
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </CardHeader>
      <CardContent className="p-0 flex-1 overflow-hidden bg-gradient-to-b from-background to-muted/20">
        <TabsContent value="preview" className="h-full m-0 p-0">
          <div className="h-full w-full bg-white relative">
            {(html || css || js) ? (
              <iframe
                ref={iframeRef}
                title="Code Preview"
                className="w-full h-full border-0"
                sandbox="allow-scripts allow-same-origin"
              />
            ) : (
              <div className="absolute inset-0 flex items-center justify-center">
                <p className="text-muted-foreground text-sm">No code to preview yet</p>
              </div>
            )}
          </div>
        </TabsContent>
        <TabsContent value="html" className="h-full m-0 p-0">
          <pre className="h-full p-4 overflow-auto bg-muted/10 text-sm">
            <code>{html || "// No HTML code to display"}</code>
          </pre>
        </TabsContent>
        <TabsContent value="css" className="h-full m-0 p-0">
          <pre className="h-full p-4 overflow-auto bg-muted/10 text-sm">
            <code>{css || "/* No CSS code to display */"}</code>
          </pre>
        </TabsContent>
        <TabsContent value="js" className="h-full m-0 p-0">
          <pre className="h-full p-4 overflow-auto bg-muted/10 text-sm">
            <code>{js || "// No JavaScript code to display"}</code>
          </pre>
        </TabsContent>
      </CardContent>
    </Card>
  )
}

