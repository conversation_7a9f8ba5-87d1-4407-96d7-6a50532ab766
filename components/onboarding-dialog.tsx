"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogDescription, <PERSON>alog<PERSON>ooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Check, ChevronRight, Code, MessageSquare, Settings, ExternalLink } from "lucide-react"

interface OnboardingDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export default function OnboardingDialog({ open, onOpenChange }: OnboardingDialogProps) {
  const [activeTab, setActiveTab] = useState("welcome")
  const [currentStep, setCurrentStep] = useState(1)
  const totalSteps = 4

  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1)

      // Change tab based on step
      if (currentStep === 1) setActiveTab("connect")
      if (currentStep === 2) setActiveTab("chat")
      if (currentStep === 3) setActiveTab("code")
    } else {
      onOpenChange(false)
    }
  }

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)

      // Change tab based on step
      if (currentStep === 2) setActiveTab("welcome")
      if (currentStep === 3) setActiveTab("connect")
      if (currentStep === 4) setActiveTab("chat")
    }
  }

  const handleTabChange = (value: string) => {
    setActiveTab(value)

    // Update step based on tab
    if (value === "welcome") setCurrentStep(1)
    if (value === "connect") setCurrentStep(2)
    if (value === "chat") setCurrentStep(3)
    if (value === "code") setCurrentStep(4)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
          <DialogTitle className="text-2xl">Welcome to Ollama UI Builder</DialogTitle>
          <DialogDescription>
            Let's get you started with building applications using AI
          </DialogDescription>
          </DialogHeader>

        <div className="py-4">
          <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid grid-cols-4 mb-8">
              <TabsTrigger value="welcome" className="flex items-center gap-2">
                <span className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm">
                  1
                </span>
                Welcome
              </TabsTrigger>
              <TabsTrigger value="connect" className="flex items-center gap-2">
                <span className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm">
                  2
                </span>
                Connect
              </TabsTrigger>
              <TabsTrigger value="chat" className="flex items-center gap-2">
                <span className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm">
                  3
                </span>
                Chat
              </TabsTrigger>
              <TabsTrigger value="code" className="flex items-center gap-2">
                <span className="h-6 w-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm">
                  4
                </span>
                Code
              </TabsTrigger>
            </TabsList>

            <TabsContent value="welcome">
              <Card>
                <CardHeader>
                  <CardTitle>Welcome to Ollama UI Builder</CardTitle>
                  <CardDescription>
                    Your AI-powered code generation and development environment
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold flex items-center">
                        <MessageSquare className="mr-2 h-5 w-5" />
                        Interactive Chat
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Chat with AI models to generate code, get help, or solve coding problems
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold flex items-center">
                        <Code className="mr-2 h-5 w-5" />
                        Code Generation
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Generate complete applications, components, or code snippets with a simple prompt
                      </p>
                    </div>
                    <div className="space-y-2">
                      <h3 className="text-lg font-semibold flex items-center">
                        <Settings className="mr-2 h-5 w-5" />
                        Customizable
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        Configure your environment, connection settings, and editor preferences
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
                </TabsContent>

            <TabsContent value="connect">
              <Card>
                <CardHeader>
                  <CardTitle>Connect to Ollama</CardTitle>
                  <CardDescription>
                    Set up the connection to your Ollama instance
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Connection Requirements</h3>

                    <div className="space-y-2 pl-5">
                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                          <Check className="h-4 w-4 text-green-500" />
                        </div>
                        <p>Install Ollama on your system</p>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                          <Check className="h-4 w-4 text-green-500" />
                        </div>
                        <p>Start the Ollama service</p>
                      </div>

                      <div className="flex items-center gap-2">
                        <div className="h-6 w-6 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                          <Check className="h-4 w-4 text-green-500" />
                        </div>
                        <p>Pull at least one model (e.g., llama2, codellama, mistral)</p>
                      </div>
                    </div>

                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md">
                      <h4 className="font-medium mb-2">Terminal Commands</h4>
                      <div className="font-mono text-sm space-y-2">
                        <div className="bg-gray-200 dark:bg-gray-700 p-2 rounded">
                          # Install Ollama (from ollama.ai)<br />
                          # Start the service<br />
                          ollama serve
                        </div>
                        <div className="bg-gray-200 dark:bg-gray-700 p-2 rounded">
                          # Pull a model<br />
                          ollama pull codellama
                    </div>
                      </div>
                    </div>

                    <p className="flex items-center text-sm">
                      <ExternalLink className="h-4 w-4 mr-1" />
                      <a
                        href="https://ollama.ai/download"
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-500 hover:underline"
                      >
                        Download Ollama from ollama.ai
                      </a>
                    </p>
                  </div>
                </CardContent>
              </Card>
                </TabsContent>

            <TabsContent value="chat">
              <Card>
                <CardHeader>
                  <CardTitle>Using the Chat Interface</CardTitle>
                  <CardDescription>
                    Generate code and get help using natural language
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Getting Started with Chat</h3>

                    <div className="space-y-4">
                      <div className="flex gap-4 items-start">
                        <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm shrink-0">
                          1
                        </div>
                        <div>
                          <h4 className="font-medium">Select a Model</h4>
                          <p className="text-sm text-muted-foreground">
                            Choose from available models in the dropdown menu at the top of the page
                          </p>
                        </div>
                      </div>

                      <div className="flex gap-4 items-start">
                        <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm shrink-0">
                          2
                        </div>
                        <div>
                          <h4 className="font-medium">Ask for Code</h4>
                          <p className="text-sm text-muted-foreground">
                            Type your request in the chat input, such as:
                          </p>
                          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md mt-2 text-sm">
                            "Create a React login form with email validation and password strength meter"
                          </div>
                        </div>
                      </div>

                      <div className="flex gap-4 items-start">
                        <div className="h-8 w-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm shrink-0">
                          3
                        </div>
                        <div>
                          <h4 className="font-medium">Review and Export</h4>
                          <p className="text-sm text-muted-foreground">
                            Review the generated code and export files to your editor or use them directly in your project
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
                </TabsContent>

            <TabsContent value="code">
              <Card>
                <CardHeader>
                  <CardTitle>Code Editor</CardTitle>
                  <CardDescription>
                    Work with the files generated by AI
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold">Editor Features</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-2">
                        <h4 className="font-medium">File Management</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>Create new files</li>
                          <li>Edit existing files</li>
                          <li>Delete files</li>
                          <li>Download files or entire projects</li>
                        </ul>
                      </div>

                      <div className="space-y-2">
                        <h4 className="font-medium">Editor Features</h4>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          <li>Syntax highlighting</li>
                          <li>Auto-completion</li>
                          <li>Code formatting</li>
                          <li>Line numbers and indentation guides</li>
                        </ul>
                      </div>
                    </div>

                    <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded-md">
                      <h4 className="font-medium mb-2">Pro Tips</h4>
                      <ul className="text-sm space-y-2">
                        <li>Switch between Chat and Editor tabs to iterate on your project</li>
                        <li>Customize the editor appearance in Settings</li>
                        <li>Use the chat to ask for improvements or changes to your code</li>
                        <li>Create multiple files to build complete applications</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
                </TabsContent>
              </Tabs>
            </div>

        <DialogFooter className="flex items-center justify-between pt-2">
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handlePrevStep}
              disabled={currentStep === 1}
            >
              Previous
            </Button>
            <Button
              variant="secondary"
              onClick={() => onOpenChange(false)}
            >
              Skip
            </Button>
          </div>
          <div className="text-sm text-muted-foreground">
            Step {currentStep} of {totalSteps}
          </div>
          <Button onClick={handleNextStep}>
            {currentStep < totalSteps ? "Next" : "Get Started"}
            {currentStep < totalSteps && <ChevronRight className="ml-2 h-4 w-4" />}
          </Button>
        </DialogFooter>
        </DialogContent>
      </Dialog>
  )
}

