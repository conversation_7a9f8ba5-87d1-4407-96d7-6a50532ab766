"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ConnectionTroubleshooter } from "@/components/connection-troubleshooter"
import { ThemeToggle } from "@/components/theme-toggle"
import { Paintbrush } from "lucide-react"

interface SettingsPanelProps {
  apiUrl: string
  setApiUrl: (url: string) => void
  apiKey: string
  setApiKey: (key: string) => void
  connectionMode: "local" | "auto" | "custom"
  setConnectionMode: (mode: "local" | "auto" | "custom") => void
  temperature: number
  setTemperature: (temp: number) => void
  maxTokens: number
  setMaxTokens: (tokens: number) => void
  selectedModel: string
  setSelectedModel: (model: string) => void
  selectedFramework: string
  setSelectedFramework: (framework: string) => void
  availableModels: string[]
  enableLogging: boolean
  setEnableLogging: (enable: boolean) => void
  primaryColor: string
  setPrimaryColor: (color: string) => void
  accentColor: string
  setAccentColor: (color: string) => void
  onSave: () => void
}

const colorOptions = [
  { value: "slate", label: "Slate" },
  { value: "zinc", label: "Zinc" },
  { value: "stone", label: "Stone" },
  { value: "gray", label: "Gray" },
  { value: "neutral", label: "Neutral" },
  { value: "red", label: "Red" },
  { value: "rose", label: "Rose" },
  { value: "orange", label: "Orange" },
  { value: "amber", label: "Amber" },
  { value: "yellow", label: "Yellow" },
  { value: "lime", label: "Lime" },
  { value: "green", label: "Green" },
  { value: "emerald", label: "Emerald" },
  { value: "teal", label: "Teal" },
  { value: "cyan", label: "Cyan" },
  { value: "sky", label: "Sky" },
  { value: "blue", label: "Blue" },
  { value: "indigo", label: "Indigo" },
  { value: "violet", label: "Violet" },
  { value: "purple", label: "Purple" },
  { value: "fuchsia", label: "Fuchsia" },
  { value: "pink", label: "Pink" },
]

const frameworkOptions = [
  { value: "react", label: "React" },
  { value: "nextjs", label: "Next.js" },
  { value: "vue", label: "Vue" },
  { value: "svelte", label: "Svelte" },
  { value: "vanilla", label: "Vanilla JS" },
]

export function SettingsPanel({
  apiUrl,
  setApiUrl,
  apiKey,
  setApiKey,
  connectionMode,
  setConnectionMode,
  temperature,
  setTemperature,
  maxTokens,
  setMaxTokens,
  selectedModel,
  setSelectedModel,
  selectedFramework,
  setSelectedFramework,
  availableModels,
  enableLogging,
  setEnableLogging,
  primaryColor,
  setPrimaryColor,
  accentColor,
  setAccentColor,
  onSave,
}: SettingsPanelProps) {
  const [activeTab, setActiveTab] = useState("connection")

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="grid grid-cols-3 mb-4">
        <TabsTrigger value="connection">Connection</TabsTrigger>
        <TabsTrigger value="generation">Generation</TabsTrigger>
        <TabsTrigger value="appearance">Appearance</TabsTrigger>
      </TabsList>

      <TabsContent value="connection" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Connection Settings</CardTitle>
            <CardDescription>Configure how to connect to your Ollama instance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-3">
              <Label>Connection Mode</Label>
              <RadioGroup
                value={connectionMode}
                onValueChange={(value) => setConnectionMode(value as "local" | "auto" | "custom")}
                className="grid grid-cols-1 gap-2"
              >
                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="local" id="local" className="mt-1" />
                  <div>
                    <Label htmlFor="local" className="cursor-pointer font-medium">
                      Local
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Connect to Ollama running on localhost (http://localhost:11434)
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="auto" id="auto" className="mt-1" />
                  <div>
                    <Label htmlFor="auto" className="cursor-pointer font-medium">
                      Auto
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Automatically detect the Ollama URL from environment variables or use default
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-2">
                  <RadioGroupItem value="custom" id="custom" className="mt-1" />
                  <div>
                    <Label htmlFor="custom" className="cursor-pointer font-medium">
                      Custom
                    </Label>
                    <p className="text-sm text-muted-foreground">Specify a custom URL for your Ollama instance</p>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {connectionMode === "custom" && (
              <div className="space-y-2">
                <Label htmlFor="api-url">API URL</Label>
                <Input
                  id="api-url"
                  value={apiUrl}
                  onChange={(e) => setApiUrl(e.target.value)}
                  placeholder="https://your-ollama-instance.com"
                />
                <p className="text-sm text-muted-foreground">
                  Enter the full URL including protocol (http:// or https://)
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="api-key">API Key (Optional)</Label>
              <Input
                id="api-key"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                type="password"
                placeholder="Your API key (if required)"
              />
              <p className="text-sm text-muted-foreground">
                Only required if your Ollama instance uses API key authentication
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="logging" checked={enableLogging} onCheckedChange={setEnableLogging} />
              <Label htmlFor="logging">Enable Debug Logging</Label>
            </div>

            <div className="pt-2">
              <ConnectionTroubleshooter apiUrl={apiUrl} connectionMode={connectionMode} />
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="generation" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Generation Settings</CardTitle>
            <CardDescription>Configure model and generation parameters</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="model">Model</Label>
              <Select value={selectedModel} onValueChange={setSelectedModel}>
                <SelectTrigger id="model">
                  <SelectValue placeholder="Select a model" />
                </SelectTrigger>
                <SelectContent>
                  {availableModels.length > 0 ? (
                    availableModels.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))
                  ) : (
                    <SelectItem value="none" disabled>
                      No models available
                    </SelectItem>
                  )}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">Select the Ollama model to use for code generation</p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="framework">Default Framework</Label>
              <Select value={selectedFramework} onValueChange={setSelectedFramework}>
                <SelectTrigger id="framework">
                  <SelectValue placeholder="Select a framework" />
                </SelectTrigger>
                <SelectContent>
                  {frameworkOptions.map((framework) => (
                    <SelectItem key={framework.value} value={framework.value}>
                      {framework.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-sm text-muted-foreground">Select the default framework for code generation</p>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="temperature">Temperature: {temperature.toFixed(1)}</Label>
              </div>
              <Slider
                id="temperature"
                min={0}
                max={2}
                step={0.1}
                value={[temperature]}
                onValueChange={(value) => setTemperature(value[0])}
              />
              <p className="text-sm text-muted-foreground">
                Lower values produce more deterministic outputs, higher values more creative
              </p>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between">
                <Label htmlFor="max-tokens">Max Tokens: {maxTokens}</Label>
              </div>
              <Slider
                id="max-tokens"
                min={256}
                max={4096}
                step={256}
                value={[maxTokens]}
                onValueChange={(value) => setMaxTokens(value[0])}
              />
              <p className="text-sm text-muted-foreground">Maximum number of tokens to generate in the response</p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="appearance" className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Appearance Settings</CardTitle>
            <CardDescription>Customize the look and feel of the UI</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <Label>Theme Mode</Label>
              <ThemeToggle />
            </div>

            <div className="space-y-2">
              <Label htmlFor="primary-color">Primary Color</Label>
              <div className="flex items-center gap-2">
                <Paintbrush className="h-4 w-4 text-muted-foreground" />
                <Select value={primaryColor} onValueChange={setPrimaryColor}>
                  <SelectTrigger id="primary-color" className="flex-1">
                    <SelectValue placeholder="Select a color" />
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map((color) => (
                      <SelectItem key={color.value} value={color.value}>
                        {color.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="accent-color">Accent Color</Label>
              <div className="flex items-center gap-2">
                <Paintbrush className="h-4 w-4 text-muted-foreground" />
                <Select value={accentColor} onValueChange={setAccentColor}>
                  <SelectTrigger id="accent-color" className="flex-1">
                    <SelectValue placeholder="Select a color" />
                  </SelectTrigger>
                  <SelectContent>
                    {colorOptions.map((color) => (
                      <SelectItem key={color.value} value={color.value}>
                        {color.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="pt-4">
              <div className="grid grid-cols-2 gap-2">
                <div className={`p-4 rounded-md bg-primary text-primary-foreground text-center`}>Primary</div>
                <div className={`p-4 rounded-md bg-secondary text-secondary-foreground text-center`}>Secondary</div>
                <div className={`p-4 rounded-md bg-accent text-accent-foreground text-center`}>Accent</div>
                <div className={`p-4 rounded-md bg-muted text-muted-foreground text-center`}>Muted</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <div className="mt-4">
        <Button onClick={onSave} className="w-full">
          Save Changes
        </Button>
      </div>
    </Tabs>
  )
}

