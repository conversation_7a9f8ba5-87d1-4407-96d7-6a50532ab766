"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { <PERSON>lider } from "@/components/ui/slider"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"

const modelParametersSchema = z.object({
  temperature: z.number().min(0).max(2).step(0.01),
  topP: z.number().min(0).max(1).step(0.01),
  topK: z.number().int().min(1).max(100),
  maxTokens: z.number().int().min(1).max(8192),
  presencePenalty: z.number().min(-2).max(2).step(0.01),
  frequencyPenalty: z.number().min(-2).max(2).step(0.01),
  stop: z.array(z.string()),
})

type ModelParametersValues = z.infer<typeof modelParametersSchema>

interface ModelParametersDialogProps {
  parameters: {
    temperature: number
    topP: number
    topK: number
    maxTokens: number
    presencePenalty: number
    frequencyPenalty: number
    stop: string[]
  }
  onUpdateParameters: (parameters: any) => void
  children: React.ReactNode
}

export function ModelParametersDialog({ parameters, onUpdateParameters, children }: ModelParametersDialogProps) {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()

  const form = useForm<ModelParametersValues>({
    resolver: zodResolver(modelParametersSchema),
    defaultValues: {
      temperature: parameters.temperature,
      topP: parameters.topP,
      topK: parameters.topK,
      maxTokens: parameters.maxTokens,
      presencePenalty: parameters.presencePenalty,
      frequencyPenalty: parameters.frequencyPenalty,
      stop: parameters.stop,
    },
  })

  function onSubmit(data: ModelParametersValues) {
    onUpdateParameters(data)

    toast({
      title: "Model parameters updated",
      description: "Your model parameters have been saved",
    })

    setOpen(false)
  }

  const handleStopSequencesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value
    const stopSequences = value
      .split(",")
      .map((s) => s.trim())
      .filter(Boolean)
    form.setValue("stop", stopSequences)
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Model Parameters</DialogTitle>
          <DialogDescription>Adjust parameters to control the model's behavior</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 py-4">
            <FormField
              control={form.control}
              name="temperature"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Temperature: {field.value.toFixed(2)}</FormLabel>
                  <FormControl>
                    <Slider
                      min={0}
                      max={2}
                      step={0.01}
                      defaultValue={[field.value]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <FormDescription>Controls randomness (0 = deterministic, 2 = very random)</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="topP"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Top-P: {field.value.toFixed(2)}</FormLabel>
                  <FormControl>
                    <Slider
                      min={0}
                      max={1}
                      step={0.01}
                      defaultValue={[field.value]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <FormDescription>Controls diversity via nucleus sampling</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="topK"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Top-K: {field.value}</FormLabel>
                  <FormControl>
                    <Slider
                      min={1}
                      max={100}
                      step={1}
                      defaultValue={[field.value]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <FormDescription>Limits vocabulary to top K tokens</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="maxTokens"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max Tokens: {field.value}</FormLabel>
                  <FormControl>
                    <Slider
                      min={1}
                      max={8192}
                      step={1}
                      defaultValue={[field.value]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <FormDescription>Maximum number of tokens to generate</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="presencePenalty"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Presence Penalty: {field.value.toFixed(2)}</FormLabel>
                    <FormControl>
                      <Slider
                        min={-2}
                        max={2}
                        step={0.01}
                        defaultValue={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                    </FormControl>
                    <FormDescription>Penalizes repeated tokens</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="frequencyPenalty"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Frequency Penalty: {field.value.toFixed(2)}</FormLabel>
                    <FormControl>
                      <Slider
                        min={-2}
                        max={2}
                        step={0.01}
                        defaultValue={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                    </FormControl>
                    <FormDescription>Penalizes frequent tokens</FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormItem>
              <FormLabel>Stop Sequences</FormLabel>
              <FormControl>
                <Input
                  placeholder="Enter comma-separated stop sequences"
                  defaultValue={parameters.stop.join(", ")}
                  onChange={handleStopSequencesChange}
                />
              </FormControl>
              <FormDescription>Sequences that will cause the model to stop generating</FormDescription>
              <FormMessage />
            </FormItem>

            <DialogFooter>
              <Button type="submit">Save Parameters</Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

