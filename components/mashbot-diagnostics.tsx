"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import {
  AlertCircle,
  AlertTriangle,
  CheckCircle2,
  ChevronRight,
  Clock,
  Database,
  Loader2,
  RefreshCw,
  Server,
  Settings,
  Sparkles,
  Wrench
} from "lucide-react"
import { cn } from "@/lib/utils"
import { mashBotDiagnosticService, type MashBotConnectionStatus } from "@/lib/mashbot-diagnostic-service"

interface MashBotDiagnosticsProps {
  selectedModel: string
  onClose?: () => void
}

export default function MashBotDiagnostics({ selectedModel, onClose }: MashBotDiagnosticsProps) {
  const [isRunningDiagnostics, setIsRunningDiagnostics] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<MashBotConnectionStatus | null>(null)
  const [detailedDiagnostics, setDetailedDiagnostics] = useState<any>(null)
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [activeTab, setActiveTab] = useState<"status" | "details" | "suggestions">("status")

  // Run basic connection check on mount
  useEffect(() => {
    checkConnection()
  }, [])

  // Check basic connection status
  const checkConnection = async () => {
    try {
      setIsRunningDiagnostics(true)
      const status = await mashBotDiagnosticService.checkConnection(selectedModel)
      setConnectionStatus(status)

      // If not connected, also get suggestions
      if (!status.connected) {
        const suggestions = mashBotDiagnosticService.getTroubleshootingSuggestions({
          connectionStatus: 'disconnected',
          errors: [{ message: status.error }],
          modelName: selectedModel
        })
        setSuggestions(suggestions)
      }
    } catch (error) {
      console.error("Error checking connection:", error)
      setConnectionStatus({
        connected: false,
        error: error instanceof Error ? error.message : "Unknown error checking connection"
      })
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  // Run detailed diagnostics
  const runDetailedDiagnostics = async () => {
    try {
      setIsRunningDiagnostics(true)
      setActiveTab("details")

      const diagnostics = await mashBotDiagnosticService.getDetailedDiagnostics(selectedModel)
      setDetailedDiagnostics(diagnostics)

      // Get troubleshooting suggestions
      const suggestions = mashBotDiagnosticService.getTroubleshootingSuggestions({
        ...diagnostics,
        modelName: selectedModel
      })
      setSuggestions(suggestions)
    } catch (error) {
      console.error("Error running diagnostics:", error)
      setDetailedDiagnostics({
        connectionStatus: 'error',
        error: error instanceof Error ? error.message : "Unknown error running diagnostics",
        timestamp: new Date().toISOString()
      })
    } finally {
      setIsRunningDiagnostics(false)
    }
  }

  return (
    <div className="flex flex-col h-full">
      <div className="border-b p-4 flex justify-between items-center bg-muted/10">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-primary" />
          <h2 className="text-lg font-medium">Mash Bot Diagnostics</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={checkConnection}
            disabled={isRunningDiagnostics}
            className="rounded-full"
          >
            {isRunningDiagnostics ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
          {onClose && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="rounded-full"
            >
              Close
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="flex-1 flex flex-col">
        <div className="border-b px-4 bg-muted/10">
          <TabsList className="h-10 rounded-full">
            <TabsTrigger value="status" className="flex items-center rounded-full text-xs md:text-sm">
              <Server className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Status</span>
            </TabsTrigger>
            <TabsTrigger value="details" className="flex items-center rounded-full text-xs md:text-sm">
              <Database className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Details</span>
            </TabsTrigger>
            <TabsTrigger value="suggestions" className="flex items-center rounded-full text-xs md:text-sm">
              <Wrench className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" />
              <span className="hidden sm:inline">Suggestions</span>
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="status" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              <Card className="p-6">
                <div className="flex flex-col items-center justify-center text-center">
                  {isRunningDiagnostics ? (
                    <>
                      <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                      <h3 className="text-lg font-medium mb-2">Checking Mash Bot Connection</h3>
                      <p className="text-sm text-muted-foreground">
                        Testing connection to model API...
                      </p>
                    </>
                  ) : connectionStatus ? (
                    connectionStatus.connected ? (
                      <>
                        <CheckCircle2 className="h-12 w-12 text-green-500 mb-4" />
                        <h3 className="text-lg font-medium mb-2">Mash Bot is Connected</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          Successfully connected to model API at {connectionStatus.details?.apiUrl}
                        </p>
                        <div className="flex flex-wrap gap-2 justify-center">
                          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
                            <Clock className="h-3 w-3" />
                            <span>{connectionStatus.details?.responseTime}ms response time</span>
                          </Badge>
                          <Badge variant="outline" className="flex items-center gap-1 px-3 py-1">
                            <Database className="h-3 w-3" />
                            <span>{connectionStatus.details?.models?.length || 0} models available</span>
                          </Badge>
                        </div>
                      </>
                    ) : (
                      <>
                        <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
                        <h3 className="text-lg font-medium mb-2">Connection Failed</h3>
                        <p className="text-sm text-muted-foreground mb-4">
                          {connectionStatus.error || "Could not connect to Mash Bot API"}
                        </p>
                        <Alert variant="destructive" className="text-left">
                          <AlertTriangle className="h-4 w-4" />
                          <AlertTitle>Troubleshooting Required</AlertTitle>
                          <AlertDescription>
                            Check that Mash Bot service is running and accessible at {connectionStatus.details?.apiUrl || "the configured URL"}
                          </AlertDescription>
                        </Alert>
                      </>
                    )
                  ) : (
                    <>
                      <AlertTriangle className="h-12 w-12 text-yellow-500 mb-4" />
                      <h3 className="text-lg font-medium mb-2">Connection Status Unknown</h3>
                      <p className="text-sm text-muted-foreground">
                        Click "Check Connection" to test the Mash Bot API connection
                      </p>
                    </>
                  )}
                </div>
              </Card>

              <div className="flex justify-center mt-6">
                <Button
                  onClick={runDetailedDiagnostics}
                  disabled={isRunningDiagnostics}
                  className="rounded-full"
                >
                  {isRunningDiagnostics ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Sparkles className="h-4 w-4 mr-2" />
                  )}
                  Run Detailed Diagnostics
                </Button>
              </div>
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="details" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              {isRunningDiagnostics ? (
                <Card className="p-6 text-center">
                  <Loader2 className="h-12 w-12 text-primary animate-spin mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">Running Detailed Diagnostics</h3>
                  <p className="text-sm text-muted-foreground">
                    This may take a few moments...
                  </p>
                </Card>
              ) : detailedDiagnostics ? (
                <>
                  <Card className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <Server className="h-5 w-5 text-primary" />
                        <h3 className="font-medium">Connection Status</h3>
                      </div>
                      <Badge
                        className={cn(
                          detailedDiagnostics.connectionStatus === 'connected' ? "bg-green-500" :
                          detailedDiagnostics.connectionStatus === 'disconnected' ? "bg-red-500" :
                          "bg-yellow-500"
                        )}
                      >
                        {detailedDiagnostics.connectionStatus}
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">API URL:</span>
                        <span>{detailedDiagnostics.apiUrl}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Timestamp:</span>
                        <span>{new Date(detailedDiagnostics.timestamp).toLocaleString()}</span>
                      </div>
                      {detailedDiagnostics.connectionDetails?.responseTime && (
                        <div className="flex justify-between">
                          <span className="text-muted-foreground">Response Time:</span>
                          <span>{detailedDiagnostics.connectionDetails.responseTime}ms</span>
                        </div>
                      )}
                    </div>
                  </Card>

                  {detailedDiagnostics.models && detailedDiagnostics.models.length > 0 && (
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-4">
                        <Database className="h-5 w-5 text-primary" />
                        <h3 className="font-medium">Available Models</h3>
                      </div>
                      <div className="space-y-2">
                        {detailedDiagnostics.models.map((model: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-2 rounded-md bg-muted/30">
                            <div className="flex items-center gap-2">
                              <Badge variant="outline" className="font-mono text-xs">
                                {model.name}
                              </Badge>
                            </div>
                            <div className="text-xs text-muted-foreground">
                              {model.size ? `${(model.size / (1024 * 1024 * 1024)).toFixed(1)} GB` : ''}
                            </div>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}

                  {detailedDiagnostics.modelDetails && (
                    <Card className="p-4">
                      <div className="flex items-center gap-2 mb-4">
                        <Sparkles className="h-5 w-5 text-primary" />
                        <h3 className="font-medium">Model Details: {selectedModel}</h3>
                      </div>
                      <div className="space-y-2 text-sm">
                        {Object.entries(detailedDiagnostics.modelDetails).map(([key, value]: [string, any]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-muted-foreground">{key}:</span>
                            <span className="font-mono text-xs max-w-[60%] truncate">
                              {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                            </span>
                          </div>
                        ))}
                      </div>
                    </Card>
                  )}

                  {detailedDiagnostics.simpleGenerationTest && (
                    <Card className="p-4">
                      <div className="flex items-center justify-between mb-4">
                        <div className="flex items-center gap-2">
                          <Sparkles className="h-5 w-5 text-primary" />
                          <h3 className="font-medium">Generation Test</h3>
                        </div>
                        <Badge
                          className={cn(
                            detailedDiagnostics.simpleGenerationTest.success ? "bg-green-500" : "bg-red-500"
                          )}
                        >
                          {detailedDiagnostics.simpleGenerationTest.success ? "Success" : "Failed"}
                        </Badge>
                      </div>

                      {detailedDiagnostics.simpleGenerationTest.success ? (
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-muted-foreground">Response Time:</span>
                            <span>{detailedDiagnostics.simpleGenerationTest.responseTime}ms</span>
                          </div>
                          <Separator className="my-2" />
                          <div>
                            <div className="text-muted-foreground mb-1">Response:</div>
                            <div className="p-2 bg-muted/30 rounded-md text-xs">
                              {detailedDiagnostics.simpleGenerationTest.response}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="text-sm text-red-500">
                          {detailedDiagnostics.simpleGenerationTest.error}
                        </div>
                      )}
                    </Card>
                  )}

                  {detailedDiagnostics.errors && detailedDiagnostics.errors.length > 0 && (
                    <Card className="p-4 border-red-200">
                      <div className="flex items-center gap-2 mb-4">
                        <AlertCircle className="h-5 w-5 text-red-500" />
                        <h3 className="font-medium text-red-500">Errors</h3>
                      </div>
                      <div className="space-y-2">
                        {detailedDiagnostics.errors.map((error: any, index: number) => (
                          <Alert key={index} variant="destructive" className="text-sm">
                            <div className="font-medium">{error.component || "Error"}:</div>
                            <div className="text-xs mt-1">{error.message}</div>
                          </Alert>
                        ))}
                      </div>
                    </Card>
                  )}
                </>
              ) : (
                <Card className="p-6 text-center">
                  <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium mb-2">No Diagnostic Data</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Click "Run Detailed Diagnostics" to gather information about your Mash Bot setup
                  </p>
                  <Button
                    onClick={runDetailedDiagnostics}
                    disabled={isRunningDiagnostics}
                    className="rounded-full"
                  >
                    <Sparkles className="h-4 w-4 mr-2" />
                    Run Detailed Diagnostics
                  </Button>
                </Card>
              )}
            </div>
          </ScrollArea>
        </TabsContent>

        <TabsContent value="suggestions" className="flex-1 p-0 m-0 overflow-hidden flex flex-col">
          <ScrollArea className="flex-1 p-4">
            <div className="space-y-4 max-w-3xl mx-auto">
              <Card className="p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Wrench className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">Troubleshooting Suggestions</h3>
                </div>

                {suggestions.length > 0 ? (
                  <div className="space-y-2">
                    {suggestions.map((suggestion, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                        <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                        <div className="text-sm">{suggestion}</div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center p-4">
                    <p className="text-sm text-muted-foreground">
                      {connectionStatus?.connected
                        ? "No issues detected. Your Mash Bot connection appears to be working correctly."
                        : "Run diagnostics to get troubleshooting suggestions."}
                    </p>
                  </div>
                )}
              </Card>

              <Card className="p-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="h-5 w-5 text-primary" />
                  <h3 className="font-medium">General Troubleshooting Tips</h3>
                </div>

                <div className="space-y-2">
                  <div className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                    <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <div className="text-sm">Make sure Mash Bot service is installed and running on your system.</div>
                  </div>
                  <div className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                    <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <div className="text-sm">Check that the selected model is available in the Models tab.</div>
                  </div>
                  <div className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                    <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <div className="text-sm">If a model is missing, you can add it in the Models management section.</div>
                  </div>
                  <div className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                    <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <div className="text-sm">Restart the Mash Bot service if you're experiencing connection issues.</div>
                  </div>
                  <div className="flex items-start gap-2 p-2 rounded-md bg-muted/30">
                    <ChevronRight className="h-4 w-4 text-primary flex-shrink-0 mt-0.5" />
                    <div className="text-sm">Check your system resources. Mash Bot requires sufficient RAM and disk space for model operations.</div>
                  </div>
                </div>
              </Card>
            </div>
          </ScrollArea>
        </TabsContent>
      </Tabs>
    </div>
  )
}
