"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, XCircle, ArrowRight, RefreshCw, ExternalLink, Download, X } from "lucide-react"
import { createOllamaAPI } from "@/lib/ollama-api"
import { useToast } from "@/hooks/use-toast"

interface DeployedConnectionGuideProps {
  onSettingsChange: (settings: any) => void
  onDismiss: () => void
}

export default function DeployedConnectionGuide({
  onSettingsChange,
  onDismiss,
}: DeployedConnectionGuideProps) {
  const [activeTab, setActiveTab] = useState("guide")
  const [checking, setChecking] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<"unchecked" | "success" | "error">("unchecked")
  const [errorDetails, setErrorDetails] = useState("")
  const { toast } = useToast()

  const checkConnection = async () => {
    setChecking(true)
    setConnectionStatus("unchecked")
    setErrorDetails("")

    try {
      // Always try to connect to localhost when checking from the guide
      const ollamaApi = createOllamaAPI("local")
      const response = await ollamaApi.listModels()

      if (response && response.models) {
        setConnectionStatus("success")
        toast({
          title: "Connection Successful",
          description: `Successfully connected to your local Ollama. Found ${response.models.length} models.`,
        })

        // Automatically update settings to use local connection
        onSettingsChange({
          connectionMode: "local",
          apiUrl: "http://localhost:11434",
        })
      } else {
        setConnectionStatus("error")
        setErrorDetails("Connected to API but received unexpected response format.")
      }
    } catch (error) {
      setConnectionStatus("error")
      if (error instanceof Error) {
        setErrorDetails(error.message)
      } else {
        setErrorDetails("Unknown error occurred")
      }
    } finally {
      setChecking(false)
    }
  }

  return (
    <Card className="w-full max-w-3xl mx-auto relative">
      <Button
        variant="ghost"
        size="icon"
        className="absolute right-2 top-2"
        onClick={onDismiss}
        aria-label="Close"
      >
        <X className="h-4 w-4" />
      </Button>
      <CardHeader>
        <CardTitle>Connect to Your Local Ollama</CardTitle>
        <CardDescription>
          This web app needs to connect to Ollama running on your computer
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="guide">Connection Guide</TabsTrigger>
            <TabsTrigger value="install">Install Ollama</TabsTrigger>
            <TabsTrigger value="troubleshoot">Troubleshooting</TabsTrigger>
          </TabsList>

          <TabsContent value="guide" className="space-y-4 pt-4">
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Important</AlertTitle>
              <AlertDescription>
                This web app runs in your browser but needs to connect to Ollama running on your computer.
                Follow these steps to connect.
              </AlertDescription>
            </Alert>

            <div className="space-y-4 mt-4">
              <div className="space-y-2">
                <h3 className="font-medium">Step 1: Make sure Ollama is running</h3>
                <p className="text-sm text-muted-foreground">
                  Ollama should be running on your computer at http://localhost:11434
                </p>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Step 2: Allow browser connection to localhost</h3>
                <p className="text-sm text-muted-foreground">
                  Your browser needs permission to connect to your local Ollama server.
                  This is a security feature of modern browsers.
                </p>
                <div className="bg-muted p-3 rounded-md">
                  <p className="text-sm mb-2">For Chrome/Edge/Brave users:</p>
                  <ol className="list-decimal list-inside text-sm space-y-1">
                    <li>Install the <a href="https://chromewebstore.google.com/detail/cors-unblock/lfhmikememgdcahcdlaciloancbhjino" target="_blank" className="text-blue-500 hover:underline">CORS Unblock extension</a></li>
                    <li>Click the extension icon in your toolbar and enable it</li>
                    <li>Refresh this page</li>
                  </ol>
                </div>
              </div>

              <div className="space-y-2">
                <h3 className="font-medium">Step 3: Test the connection</h3>
                <Button onClick={checkConnection} disabled={checking}>
                  {checking ? (
                    <>
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      Testing Connection...
                    </>
                  ) : (
                    <>Test Connection</>
                  )}
                </Button>
              </div>

              {connectionStatus === "success" && (
                <Alert variant="default" className="bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  <AlertTitle>Connection Successful!</AlertTitle>
                  <AlertDescription>
                    Successfully connected to your local Ollama. You can now use the application.
                  </AlertDescription>
                </Alert>
              )}

              {connectionStatus === "error" && (
                <Alert variant="destructive">
                  <XCircle className="h-4 w-4 mr-2" />
                  <AlertTitle>Connection Failed</AlertTitle>
                  <AlertDescription className="space-y-2">
                    <p>Unable to connect to your local Ollama. Error: {errorDetails}</p>
                    <p className="cursor-pointer text-sm hover:underline" onClick={() => setActiveTab("troubleshoot")}>
                      <span className="flex items-center">
                        View troubleshooting steps <ArrowRight className="ml-1 h-3 w-3" />
                      </span>
                    </p>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </TabsContent>

          <TabsContent value="install" className="space-y-4 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Installing Ollama</h3>

              <p className="text-sm text-muted-foreground">
                If you haven't installed Ollama yet, follow these steps:
              </p>

              <div className="space-y-4">
                <div className="space-y-2">
                  <h4 className="font-medium">1. Download Ollama</h4>
                  <div className="flex flex-wrap gap-2">
                    <Button variant="outline" size="sm" onClick={() => window.open("https://ollama.com/download/windows", "_blank")}>
                      <Download className="mr-2 h-4 w-4" />
                      Windows
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => window.open("https://ollama.com/download/mac", "_blank")}>
                      <Download className="mr-2 h-4 w-4" />
                      macOS
                    </Button>
                    <Button variant="outline" size="sm" onClick={() => window.open("https://ollama.com/download/linux", "_blank")}>
                      <Download className="mr-2 h-4 w-4" />
                      Linux
                    </Button>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">2. Install and run Ollama</h4>
                  <p className="text-sm text-muted-foreground">
                    Follow the installation instructions for your operating system.
                  </p>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium">3. Pull a model</h4>
                  <p className="text-sm text-muted-foreground">
                    After installing, open a terminal or command prompt and run:
                  </p>
                  <div className="bg-muted p-2 rounded-md text-xs font-mono">
                    ollama pull llama3
                  </div>
                </div>
              </div>

              <Button variant="outline" className="w-full mt-4" onClick={() => window.open("https://github.com/ollama/ollama", "_blank")}>
                <ExternalLink className="mr-2 h-4 w-4" />
                Visit Ollama GitHub for more information
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="troubleshoot" className="space-y-4 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Troubleshooting Connection Issues</h3>

              <div className="space-y-3">
                <div className="space-y-1">
                  <h4 className="font-medium">1. Verify Ollama is running</h4>
                  <p className="text-sm text-muted-foreground">
                    Make sure the Ollama application is running on your computer.
                  </p>
                </div>

                <div className="space-y-1">
                  <h4 className="font-medium">2. Check CORS extension</h4>
                  <p className="text-sm text-muted-foreground">
                    Ensure your CORS extension is enabled. Try a different CORS extension if the first one doesn't work.
                  </p>
                </div>

                <div className="space-y-1">
                  <h4 className="font-medium">3. Firewall issues</h4>
                  <p className="text-sm text-muted-foreground">
                    Your firewall might be blocking access to port 11434. Check your firewall settings.
                  </p>
                </div>

                <div className="space-y-1">
                  <h4 className="font-medium">4. Try a different browser</h4>
                  <p className="text-sm text-muted-foreground">
                    Some browsers handle local connections differently. Try Chrome if you're using another browser.
                  </p>
                </div>

                <div className="space-y-1">
                  <h4 className="font-medium">5. Restart your computer</h4>
                  <p className="text-sm text-muted-foreground">
                    Sometimes a simple restart can fix connection issues.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div className="flex gap-2">
          {connectionStatus === "success" ? (
            <Button onClick={onDismiss}>Continue to Application</Button>
          ) : (
            <>
              <Button variant="outline" onClick={() => setActiveTab(activeTab === "guide" ? "troubleshoot" : "guide")}>
                {activeTab === "guide" ? "View Troubleshooting" : "Back to Guide"}
              </Button>
              <Button variant="secondary" onClick={onDismiss}>
                Skip for Now
              </Button>
            </>
          )}
        </div>
        <Button variant="outline" onClick={checkConnection} disabled={checking}>
          {checking ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : null}
          Test Connection
        </Button>
      </CardFooter>
    </Card>
  )
}
