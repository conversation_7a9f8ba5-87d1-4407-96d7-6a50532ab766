"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Copy, Check, Download, Play, Pencil, Save, X } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { useTheme } from "next-themes"
import { formatCode } from "@/lib/code-formatter"
import dynamic from "next/dynamic"
import type * as Monaco from "monaco-editor"
import {
  <PERSON><PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"

// Dynamically import Monaco editor with SSR disabled
const Editor = dynamic(() => import("@monaco-editor/react"), { ssr: false })

// Dynamically import monaco-editor with SSR disabled
const monaco = dynamic(() => 
  import("monaco-editor").then((mod) => mod),
  { ssr: false }
) as typeof Monaco

interface EditableCodeBlockProps {
  language: string
  code: string
  onCodeChange?: (newCode: string) => void
  onSave?: (code: string) => void
  onRun?: (code: string) => void
  readOnly?: boolean
}

export default function EditableCodeBlock({
  language,
  code,
  onCodeChange,
  onSave,
  onRun,
  readOnly = false,
}: EditableCodeBlockProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [currentCode, setCurrentCode] = useState(code)
  const [copied, setCopied] = useState(false)
  const editorRef = useRef<any>(null)
  const { toast } = useToast()
  const { theme } = useTheme()

  // Update code when prop changes
  useEffect(() => {
    setCurrentCode(code)
  }, [code])

  const handleEditorDidMount = (editor: any) => {
    editorRef.current = editor
    
    // Add keyboard shortcuts
    editor.addCommand(monaco.KeyMod.WinCtrl | monaco.KeyCode.KeyS, () => {
      if (isEditing && onSave) {
        handleSave();
      }
    });
    
    editor.addCommand(monaco.KeyMod.WinCtrl | monaco.KeyCode.KeyF, () => {
      handleFormat();
    });
    
    // Set focus to the editor
    editor.focus();
  }

  const handleCopy = () => {
    navigator.clipboard.writeText(currentCode)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
    toast({
      title: "Copied to clipboard",
      description: "Code has been copied to clipboard",
    })
  }

  const handleDownload = () => {
    const blob = new Blob([currentCode], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `code.${language}`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast({
      title: "Downloaded",
      description: `Code has been downloaded as code.${language}`,
    })
  }

  const handleFormat = async () => {
    try {
      const formatted = await formatCode(currentCode, language)
      setCurrentCode(formatted)
      if (editorRef.current) {
        editorRef.current.setValue(formatted)
      }
      toast({
        title: "Code formatted",
        description: "Your code has been formatted",
      })
    } catch (error) {
      toast({
        title: "Format error",
        description: `Error formatting code: ${error instanceof Error ? error.message : String(error)}`,
        variant: "destructive",
      })
    }
  }

  const handleSave = () => {
    if (onSave) {
      onSave(currentCode)
    }
    setIsEditing(false)
    toast({
      title: "Code saved",
      description: "Your changes have been saved",
    })
  }

  const handleCancel = () => {
    setCurrentCode(code)
    setIsEditing(false)
  }

  const handleRun = () => {
    if (onRun) {
      onRun(currentCode)
    }
  }

  return (
    <Card className="my-4 overflow-hidden rounded-lg border-2 shadow-md hover:shadow-lg transition-all duration-300">
      <div className="flex items-center justify-between p-2 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 border-b">
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 rounded-full bg-blue-500"></div>
          <span className="text-xs font-medium px-2 py-1 rounded-full bg-muted/80">{language}</span>
        </div>
        <div className="flex items-center gap-1">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={handleFormat} className="h-8 w-8 rounded-full">
                  <Play className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Format Code (Ctrl+F)</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={handleCopy} className="h-8 w-8 rounded-full">
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Copy to Clipboard</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={handleDownload} className="h-8 w-8 rounded-full">
                  <Download className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom">
                <p>Download File</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
          
          {onSave && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={handleSave} className="h-8 w-8 rounded-full">
                    <Save className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Save Changes (Ctrl+S)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          
          {onRun && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={handleRun} className="h-8 w-8 rounded-full">
                    <Play className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p>Run Code</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </div>
      <div className="relative">
        <Editor
          height="300px"
          language={language}
          value={currentCode}
          theme={theme === "dark" ? "vs-dark" : "light"}
          onChange={(value) => {
            if (value !== undefined) {
              setCurrentCode(value)
              if (onCodeChange) {
                onCodeChange(value)
              }
            }
          }}
          onMount={handleEditorDidMount}
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            wordWrap: "on",
            scrollBeyondLastLine: false,
            lineNumbers: "on",
            automaticLayout: true,
            tabSize: 2,
            quickSuggestions: true,
            folding: true,
            autoIndent: "full",
            formatOnPaste: true,
            formatOnType: true,
            suggestOnTriggerCharacters: true,
            acceptSuggestionOnEnter: "on",
            cursorBlinking: "smooth",
            renderLineHighlight: "all",
            snippetSuggestions: "inline",
            colorDecorators: true,
            bracketPairColorization: { enabled: true },
            guides: { bracketPairs: true, indentation: true },
            readOnly: readOnly,
          }}
        />
      </div>
    </Card>
  )
}

