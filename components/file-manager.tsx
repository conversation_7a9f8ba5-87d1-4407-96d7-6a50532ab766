"use client"

import type React from "react"

import { useState, useRef, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetDescription, SheetHeader, Sheet<PERSON><PERSON>le, SheetTrigger } from "@/components/ui/sheet"
import { FolderOpen, FileUp, FileDown, Plus, Search } from "lucide-react"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuSeparator,
  ContextMenuTrigger,
} from "@/components/ui/context-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { v4 as uuidv4 } from "uuid"
import { cn } from "@/lib/utils"

interface FileItem {
  id: string
  name: string
  content: string
  language: string
  path?: string
  lastModified: Date
}

interface FileManagerProps {
  files: FileItem[]
  currentFileId: string
  onSelectFile: (fileId: string) => void
  onCreateFile: (file: FileItem) => void
  onUpdateFile: (fileId: string, updates: Partial<FileItem>) => void
  onDeleteFile: (fileId: string) => void
}

export function FileManager({
  files,
  currentFileId,
  onSelectFile,
  onCreateFile,
  onUpdateFile,
  onDeleteFile,
}: FileManagerProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [fileName, setFileName] = useState("")
  const [fileLanguage, setFileLanguage] = useState("javascript")
  const [searchQuery, setSearchQuery] = useState("")
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false)
  const [renamingFile, setRenamingFile] = useState<FileItem | null>(null)
  const [newFileName, setNewFileName] = useState("")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { toast } = useToast()

  const filteredFiles = useMemo(
    () =>
      files.filter(
        (file) =>
          file.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          file.content.toLowerCase().includes(searchQuery.toLowerCase()),
      ),
    [files, searchQuery],
  )

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = event.target.files
    if (fileList && fileList.length > 0) {
      Array.from(fileList).forEach((file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          const content = e.target?.result as string
          const extension = file.name.split(".").pop() || ""
          const language = mapExtensionToLanguage(extension)

          const newFile: FileItem = {
            id: uuidv4(),
            name: file.name,
            content,
            language,
            lastModified: new Date(),
          }

          onCreateFile(newFile)

          toast({
            title: "File uploaded",
            description: `${file.name} has been uploaded and opened`,
          })
        }
        reader.readAsText(file)
      })

      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }
    }
  }

  const mapExtensionToLanguage = (extension: string): string => {
    const extensionMap: Record<string, string> = {
      js: "javascript",
      ts: "typescript",
      jsx: "javascript",
      tsx: "typescript",
      py: "python",
      html: "html",
      css: "css",
      json: "json",
      md: "markdown",
      java: "java",
      c: "c",
      cpp: "cpp",
      cs: "csharp",
      go: "go",
      rs: "rust",
      rb: "ruby",
      php: "php",
      sql: "sql",
      sh: "shell",
      yaml: "yaml",
      yml: "yaml",
    }

    return extensionMap[extension.toLowerCase()] || "plaintext"
  }

  const createNewFile = () => {
    if (!fileName.trim()) {
      toast({
        title: "Error",
        description: "Please enter a file name",
        variant: "destructive",
      })
      return
    }

    const newFileName = fileName.includes(".") ? fileName : `${fileName}.${getDefaultExtension(fileLanguage)}`

    const newFile: FileItem = {
      id: uuidv4(),
      name: newFileName,
      content: "",
      language: fileLanguage,
      lastModified: new Date(),
    }

    onCreateFile(newFile)
    setFileName("")

    toast({
      title: "File created",
      description: `${newFileName} has been created`,
    })
  }

  const getDefaultExtension = (language: string): string => {
    const languageMap: Record<string, string> = {
      javascript: "js",
      typescript: "ts",
      python: "py",
      html: "html",
      css: "css",
      json: "json",
      markdown: "md",
      java: "java",
      c: "c",
      cpp: "cpp",
      csharp: "cs",
      go: "go",
      rust: "rs",
      ruby: "rb",
      php: "php",
      sql: "sql",
      shell: "sh",
      yaml: "yml",
    }

    return languageMap[language] || "txt"
  }

  const duplicateFile = (file: FileItem) => {
    const nameParts = file.name.split(".")
    const extension = nameParts.pop() || ""
    const baseName = nameParts.join(".")
    const newName = `${baseName} (copy).${extension}`

    const newFile: FileItem = {
      id: uuidv4(),
      name: newName,
      content: file.content,
      language: file.language,
      lastModified: new Date(),
    }

    onCreateFile(newFile)

    toast({
      title: "File duplicated",
      description: `${file.name} has been duplicated as ${newName}`,
    })
  }

  const startRenameFile = (file: FileItem) => {
    setRenamingFile(file)
    setNewFileName(file.name)
    setIsRenameDialogOpen(true)
  }

  const completeRenameFile = () => {
    if (!renamingFile || !newFileName.trim()) return

    onUpdateFile(renamingFile.id, { name: newFileName })

    toast({
      title: "File renamed",
      description: `File has been renamed to ${newFileName}`,
    })

    setIsRenameDialogOpen(false)
    setRenamingFile(null)
    setNewFileName("")
  }

  const downloadFile = (file: FileItem) => {
    const blob = new Blob([file.content], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = file.name
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)

    toast({
      title: "File downloaded",
      description: `${file.name} has been downloaded`,
    })
  }

  const downloadAllFiles = () => {
    if (files.length === 0) return

    // For multiple files, create a zip file
    // This is a simplified version - in a real app, you'd use a library like JSZip
    files.forEach((file) => {
      const blob = new Blob([file.content], { type: "text/plain" })
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = file.name
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    })

    toast({
      title: "Files downloaded",
      description: `${files.length} files have been downloaded`,
    })
  }

  return (
    <>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" size="icon" className="rounded-full">
            <FolderOpen className="h-4 w-4" />
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-80 sm:w-96 rounded-r-2xl">
          <SheetHeader>
            <SheetTitle>File Manager</SheetTitle>
            <SheetDescription>Manage your code files</SheetDescription>
          </SheetHeader>

          <div className="mt-6 space-y-4">
            <div className="flex gap-2">
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept=".js,.ts,.jsx,.tsx,.py,.html,.css,.json,.txt,.md,.java,.c,.cpp,.cs,.go,.rs,.rb,.php,.sql,.sh,.yaml,.yml"
                onChange={handleFileUpload}
                className="hidden"
                id="file-upload"
              />
              <Button
                variant="outline"
                size="sm"
                className="flex-1 rounded-full"
                onClick={() => fileInputRef.current?.click()}
              >
                <FileUp className="h-4 w-4 mr-2" />
                Upload
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="flex-1 rounded-full"
                onClick={downloadAllFiles}
                disabled={files.length === 0}
              >
                <FileDown className="h-4 w-4 mr-2" />
                Download All
              </Button>
            </div>

            <div className="flex gap-2">
              <Input
                placeholder="Search files..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1 rounded-full"
                prefix={<Search className="h-4 w-4 mr-2 text-muted-foreground" />}
              />
            </div>

            <div className="flex gap-2">
              <Input
                placeholder="new-file.js"
                value={fileName}
                onChange={(e) => setFileName(e.target.value)}
                className="flex-1 rounded-full"
              />
              <Select value={fileLanguage} onValueChange={setFileLanguage}>
                <SelectTrigger className="w-[120px] rounded-full">
                  <SelectValue placeholder="Language" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="javascript">JavaScript</SelectItem>
                  <SelectItem value="typescript">TypeScript</SelectItem>
                  <SelectItem value="python">Python</SelectItem>
                  <SelectItem value="html">HTML</SelectItem>
                  <SelectItem value="css">CSS</SelectItem>
                  <SelectItem value="json">JSON</SelectItem>
                  <SelectItem value="markdown">Markdown</SelectItem>
                  <SelectItem value="java">Java</SelectItem>
                  <SelectItem value="c">C</SelectItem>
                  <SelectItem value="cpp">C++</SelectItem>
                  <SelectItem value="csharp">C#</SelectItem>
                  <SelectItem value="go">Go</SelectItem>
                  <SelectItem value="rust">Rust</SelectItem>
                  <SelectItem value="ruby">Ruby</SelectItem>
                  <SelectItem value="php">PHP</SelectItem>
                  <SelectItem value="sql">SQL</SelectItem>
                  <SelectItem value="shell">Shell</SelectItem>
                  <SelectItem value="yaml">YAML</SelectItem>
                </SelectContent>
              </Select>
              <Button size="icon" onClick={createNewFile} className="rounded-full">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="border rounded-xl overflow-hidden">
              <div className="py-2 px-4 border-b bg-muted/40 font-medium flex justify-between items-center">
                <span>Files ({filteredFiles.length})</span>
                {searchQuery && (
                  <Button variant="ghost" size="sm" onClick={() => setSearchQuery("")} className="rounded-full">
                    Clear
                  </Button>
                )}
              </div>
              {filteredFiles.length === 0 ? (
                <div className="py-8 text-center text-muted-foreground">
                  {searchQuery ? "No matching files found." : "No files yet. Upload or create a new file."}
                </div>
              ) : (
                <ul className="py-2 max-h-[400px] overflow-y-auto">
                  {filteredFiles.map((file) => (
                    <ContextMenu key={file.id}>
                      <ContextMenuTrigger>
                        <li
                          className={cn(
                            "py-1 px-4 cursor-pointer hover:bg-muted/40 flex justify-between items-center",
                            currentFileId === file.id ? "bg-muted/60 font-medium" : "",
                          )}
                          onClick={() => onSelectFile(file.id)}
                        >
                          <span className="truncate">{file.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {file.lastModified.toLocaleTimeString()}
                          </span>
                        </li>
                      </ContextMenuTrigger>
                      <ContextMenuContent>
                        <ContextMenuItem onClick={() => onSelectFile(file.id)}>Open</ContextMenuItem>
                        <ContextMenuItem onClick={() => downloadFile(file)}>Download</ContextMenuItem>
                        <ContextMenuItem onClick={() => duplicateFile(file)}>Duplicate</ContextMenuItem>
                        <ContextMenuItem onClick={() => startRenameFile(file)}>Rename</ContextMenuItem>
                        <ContextMenuSeparator />
                        <ContextMenuItem
                          onClick={() => onDeleteFile(file.id)}
                          className="text-destructive focus:text-destructive"
                        >
                          Delete
                        </ContextMenuItem>
                      </ContextMenuContent>
                    </ContextMenu>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </SheetContent>
      </Sheet>

      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-[425px] rounded-2xl">
          <DialogHeader>
            <DialogTitle>Rename File</DialogTitle>
            <DialogDescription>Enter a new name for the file</DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <Input
              value={newFileName}
              onChange={(e) => setNewFileName(e.target.value)}
              placeholder="Enter new file name"
              className="w-full rounded-full"
            />
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setIsRenameDialogOpen(false)
                setRenamingFile(null)
              }}
              className="rounded-full"
            >
              Cancel
            </Button>
            <Button onClick={completeRenameFile} className="rounded-full">
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}

