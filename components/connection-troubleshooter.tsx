"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, XCircle, ArrowRight, RefreshCw, ExternalLink } from "lucide-react"
import { createOllamaAPI } from "@/lib/ollama-api"
import { useToast } from "@/hooks/use-toast"

interface ConnectionTroubleshooterProps {
  apiUrl: string
  connectionMode: "local" | "auto" | "custom"
  apiKey?: string
  onSettingsChange: (settings: any) => void
  onShowDeployedGuide?: () => void
  isDeployed?: boolean
}

export default function ConnectionTroubleshooter({
  apiUrl,
  connectionMode,
  apiKey,
  onSettingsChange,
  onShowDeployedGuide,
  isDeployed = false,
}: ConnectionTroubleshooterProps) {
  const [checking, setChecking] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<"unchecked" | "success" | "error">("unchecked")
  const [activeTab, setActiveTab] = useState("diagnose")
  const [errorDetails, setErrorDetails] = useState("")
  const { toast } = useToast()

  const resolvedApiUrl = connectionMode === "auto"
    ? `http://${window.location.hostname}:11434`
    : connectionMode === "local"
      ? "http://localhost:11434"
      : apiUrl

  const checkConnection = async () => {
    setChecking(true)
    setConnectionStatus("unchecked")
    setErrorDetails("")

    try {
      const ollamaApi = createOllamaAPI(connectionMode, apiUrl, apiKey)
      const response = await ollamaApi.listModels()

      if (response && response.models) {
        setConnectionStatus("success")
        toast({
          title: "Connection Successful",
          description: `Successfully connected to Ollama API. Found ${response.models.length} models.`,
        })
      } else {
        setConnectionStatus("error")
        setErrorDetails("Connected to API but received unexpected response format.")
      }
    } catch (error) {
      setConnectionStatus("error")
      if (error instanceof Error) {
        setErrorDetails(error.message)
      } else {
        setErrorDetails("Unknown error occurred")
      }
    } finally {
      setChecking(false)
    }
  }

  const switchToLocalhost = () => {
    onSettingsChange({
      connectionMode: "local",
      apiUrl: "http://localhost:11434",
    })
    toast({
      title: "Settings Updated",
      description: "Connection mode set to localhost (127.0.0.1:11434).",
    })
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Connection Troubleshooter</CardTitle>
        <CardDescription>
          Diagnose and fix issues connecting to your Ollama instance
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="diagnose">Diagnose</TabsTrigger>
            <TabsTrigger value="solutions">Solutions</TabsTrigger>
          </TabsList>
          <TabsContent value="diagnose" className="space-y-4 pt-4">
            <div className="flex flex-col space-y-2">
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium">Current Connection:</div>
                <div className="text-sm font-mono bg-muted px-2 py-1 rounded">{resolvedApiUrl}</div>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium">Connection Mode:</div>
                <div className="text-sm capitalize">{connectionMode}</div>
              </div>
              <div className="flex justify-between items-center">
                <div className="text-sm font-medium">API Key:</div>
                <div className="text-sm">{apiKey ? "Configured" : "Not configured"}</div>
              </div>
          </div>

            <div className="flex flex-col space-y-4 mt-6">
              <Button onClick={checkConnection} disabled={checking}>
                {checking ? (
              <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Checking Connection...
              </>
            ) : (
                  <>Test Connection</>
            )}
          </Button>

              {connectionStatus === "success" && (
                <Alert variant="default" className="bg-green-50 text-green-800 border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-900">
                  <CheckCircle2 className="h-4 w-4 mr-2" />
                  <AlertTitle>Connection Successful</AlertTitle>
                      <AlertDescription>
                    Successfully connected to the Ollama API. Your configuration is working correctly.
                      </AlertDescription>
                    </Alert>
                  )}

              {connectionStatus === "error" && (
                    <Alert variant="destructive">
                  <XCircle className="h-4 w-4 mr-2" />
                  <AlertTitle>Connection Failed</AlertTitle>
                  <AlertDescription className="space-y-2">
                    <p>Unable to connect to Ollama API. Error: {errorDetails}</p>
                    <p className="cursor-pointer text-sm hover:underline" onClick={() => setActiveTab("solutions")}>
                      <span className="flex items-center">
                        View solutions <ArrowRight className="ml-1 h-3 w-3" />
                      </span>
                    </p>
                      </AlertDescription>
                    </Alert>
                  )}
            </div>
          </TabsContent>

          <TabsContent value="solutions" className="space-y-6 pt-4">
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Common Issues and Solutions</h3>

              <div className="space-y-1">
                <h4 className="font-medium">1. Ollama is not running</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Ensure that Ollama is installed and the service is running on your machine.
                </p>
                <div className="bg-muted p-2 rounded-md text-xs font-mono mb-4">
                  # Windows (PowerShell as Admin):<br />
                  Start-Process "ollama" -ArgumentList "serve"<br /><br />
                  # Mac/Linux:<br />
                  ollama serve
                </div>
              </div>

              <div className="space-y-1">
                <h4 className="font-medium">2. Wrong Connection URL</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  Ensure you're using the correct connection URL. For local installations, try:
                </p>
                <div className="flex flex-wrap gap-2 mb-4">
                  <Button size="sm" variant="outline" onClick={switchToLocalhost}>
                    Switch to localhost (127.0.0.1:11434)
                  </Button>

                  {isDeployed && onShowDeployedGuide && (
                    <Button size="sm" variant="outline" onClick={onShowDeployedGuide}>
                      Show Connection Guide
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-1">
                <h4 className="font-medium">3. Network/Firewall Issues</h4>
                <p className="text-sm text-muted-foreground">
                  Your firewall might be blocking the connection to Ollama. Make sure port 11434 is allowed.
                </p>
                            </div>

              <div className="space-y-1">
                <h4 className="font-medium">4. CORS Issues</h4>
                <p className="text-sm text-muted-foreground">
                  If you're seeing CORS errors, you may need to configure Ollama to allow cross-origin requests.
                </p>
                        </div>

              <div className="space-y-1">
                <h4 className="font-medium">5. No Models Installed</h4>
                <p className="text-sm text-muted-foreground mb-2">
                  You might need to pull models to your Ollama instance:
                </p>
                <div className="bg-muted p-2 rounded-md text-xs font-mono">
                  # Pull a model:<br />
                  ollama pull llama2<br />
                  # or<br />
                  ollama pull mistral
                </div>
              </div>
        </div>

            <Button variant="outline" className="w-full" onClick={() => window.open("https://github.com/ollama/ollama/blob/main/docs/troubleshooting.md", "_blank")}>
              <ExternalLink className="mr-2 h-4 w-4" />
              View Ollama Troubleshooting Guide
            </Button>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <Button variant="outline" onClick={() => setActiveTab(activeTab === "diagnose" ? "solutions" : "diagnose")}>
          {activeTab === "diagnose" ? "View Solutions" : "Back to Diagnostics"}
        </Button>
        <Button onClick={checkConnection} disabled={checking}>
          {checking ? <RefreshCw className="mr-2 h-4 w-4 animate-spin" /> : null}
          Test Connection
        </Button>
      </CardFooter>
    </Card>
  )
}

