"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  <PERSON>alogHeader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON>rigger,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Plus, Trash2, Edit } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"
import { v4 as uuidv4 } from "uuid"

const templateFormSchema = z.object({
  name: z.string().min(1, { message: "Template name is required" }),
  template: z.string().min(10, { message: "Template must be at least 10 characters" }),
})

type TemplateFormValues = z.infer<typeof templateFormSchema>

interface PromptTemplate {
  id: string
  name: string
  template: string
}

interface PromptTemplateDialogProps {
  templates: PromptTemplate[]
  selectedTemplate: string
  onSelectTemplate: (templateId: string) => void
  onUpdateTemplates: (templates: PromptTemplate[]) => void
  children: React.ReactNode
}

export function PromptTemplateDialog({
  templates,
  selectedTemplate,
  onSelectTemplate,
  onUpdateTemplates,
  children,
}: PromptTemplateDialogProps) {
  const [open, setOpen] = useState(false)
  const [activeTab, setActiveTab] = useState<"select" | "create" | "edit">("select")
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null)
  const { toast } = useToast()

  const form = useForm<TemplateFormValues>({
    resolver: zodResolver(templateFormSchema),
    defaultValues: {
      name: "",
      template: "",
    },
  })

  const handleSelectTemplate = (templateId: string) => {
    onSelectTemplate(templateId)
    setOpen(false)
  }

  const handleCreateTemplate = (data: TemplateFormValues) => {
    const newTemplate: PromptTemplate = {
      id: uuidv4(),
      name: data.name,
      template: data.template,
    }

    onUpdateTemplates([...templates, newTemplate])

    toast({
      title: "Template created",
      description: `Template "${data.name}" has been created`,
    })

    form.reset()
    setActiveTab("select")
  }

  const handleEditTemplate = (data: TemplateFormValues) => {
    if (!editingTemplate) return

    const updatedTemplates = templates.map((template) =>
      template.id === editingTemplate.id ? { ...template, name: data.name, template: data.template } : template,
    )

    onUpdateTemplates(updatedTemplates)

    toast({
      title: "Template updated",
      description: `Template "${data.name}" has been updated`,
    })

    form.reset()
    setEditingTemplate(null)
    setActiveTab("select")
  }

  const handleDeleteTemplate = (templateId: string) => {
    // Don't delete if it's the last template
    if (templates.length <= 1) {
      toast({
        title: "Cannot delete template",
        description: "You must have at least one template",
        variant: "destructive",
      })
      return
    }

    const updatedTemplates = templates.filter((template) => template.id !== templateId)
    onUpdateTemplates(updatedTemplates)

    // If the deleted template was selected, select the first available template
    if (templateId === selectedTemplate) {
      onSelectTemplate(updatedTemplates[0].id)
    }

    toast({
      title: "Template deleted",
      description: "The template has been deleted",
    })
  }

  const startEditTemplate = (template: PromptTemplate) => {
    setEditingTemplate(template)
    form.reset({
      name: template.name,
      template: template.template,
    })
    setActiveTab("edit")
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Prompt Templates</DialogTitle>
          <DialogDescription>Select, create, or edit prompt templates for code analysis</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as any)} className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="select">Select Template</TabsTrigger>
            <TabsTrigger value="create">Create New</TabsTrigger>
            <TabsTrigger value="edit" disabled={!editingTemplate}>
              Edit Template
            </TabsTrigger>
          </TabsList>

          <TabsContent value="select" className="py-4">
            <RadioGroup defaultValue={selectedTemplate} className="space-y-4">
              {templates.map((template) => (
                <div key={template.id} className="flex items-start space-x-2 border rounded-md p-3">
                  <RadioGroupItem
                    value={template.id}
                    id={template.id}
                    onClick={() => handleSelectTemplate(template.id)}
                  />
                  <div className="flex-1 space-y-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor={template.id} className="font-medium">
                        {template.name}
                      </Label>
                      <div className="flex space-x-2">
                        <Button variant="ghost" size="icon" onClick={() => startEditTemplate(template)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="icon" onClick={() => handleDeleteTemplate(template.id)}>
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground max-h-32 overflow-y-auto">
                      <pre className="whitespace-pre-wrap">
                        {template.template.substring(0, 200)}
                        {template.template.length > 200 ? "..." : ""}
                      </pre>
                    </div>
                  </div>
                </div>
              ))}
            </RadioGroup>

            <div className="mt-4">
              <Button variant="outline" onClick={() => setActiveTab("create")} className="w-full">
                <Plus className="h-4 w-4 mr-2" />
                Create New Template
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="create" className="py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleCreateTemplate)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Code Review" {...field} />
                      </FormControl>
                      <FormDescription>A descriptive name for your template</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter your prompt template here..."
                          className="min-h-[200px] font-mono"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Use &#123;&#123;fileName&#125;&#125;, &#123;&#123;language&#125;&#125;, and
                        &#123;&#123;code&#125;&#125; as placeholders
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button type="button" variant="outline" onClick={() => setActiveTab("select")}>
                    Cancel
                  </Button>
                  <Button type="submit">Create Template</Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>

          <TabsContent value="edit" className="py-4">
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleEditTemplate)} className="space-y-4">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Name</FormLabel>
                      <FormControl>
                        <Input placeholder="e.g., Code Review" {...field} />
                      </FormControl>
                      <FormDescription>A descriptive name for your template</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="template"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Template Content</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter your prompt template here..."
                          className="min-h-[200px] font-mono"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Use &#123;&#123;fileName&#125;&#125;, &#123;&#123;language&#125;&#125;, and
                        &#123;&#123;code&#125;&#125; as placeholders
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => {
                      setEditingTemplate(null)
                      setActiveTab("select")
                    }}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">Update Template</Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

