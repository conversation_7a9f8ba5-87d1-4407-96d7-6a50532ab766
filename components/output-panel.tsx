"use client"

import { useState, useRef, useEffect, useMemo } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { Loader2, Copy, Download, Check, Code } from "lucide-react"
import { cn } from "@/lib/utils"
import ReactMarkdown from "react-markdown"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { vscDarkPlus, vs } from "react-syntax-highlighter/dist/esm/styles/prism"
import { useTheme } from "next-themes"

interface OutputPanelProps {
  output: string
  logs: string[]
  isProcessing: boolean
  onCopyOutput: () => void
  onDownloadOutput: () => void
  onApplyCode?: () => void
}

export default function OutputPanel({
  output,
  logs,
  isProcessing,
  onCopyOutput,
  onDownloadOutput,
  onApplyCode,
}: OutputPanelProps) {
  const [activeTab, setActiveTab] = useState<string>("result")
  const [copied, setCopied] = useState(false)
  const logsEndRef = useRef<HTMLDivElement>(null)
  const { theme } = useTheme()

  // Auto-scroll logs to bottom
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [logs])

  // Reset copied state after 2 seconds
  useEffect(() => {
    if (copied) {
      const timeout = setTimeout(() => {
        setCopied(false)
      }, 2000)
      return () => clearTimeout(timeout)
    }
  }, [copied])

  const handleCopy = () => {
    onCopyOutput()
    setCopied(true)
  }

  // Custom renderer for code blocks in markdown
  const components = useMemo(
    () => ({
      code({ node, inline, className, children, ...props }: any) {
        const match = /language-(\w+)/.exec(className || "")
        return !inline && match ? (
          <SyntaxHighlighter style={theme === "dark" ? vscDarkPlus : vs} language={match[1]} PreTag="div" {...props}>
            {String(children).replace(/\n$/, "")}
          </SyntaxHighlighter>
        ) : (
          <code className={cn("bg-muted px-1 py-0.5 rounded text-sm", className)} {...props}>
            {children}
          </code>
        )
      },
    }),
    [theme],
  )

  return (
    <div className="flex flex-col h-full">
      <div className="p-4 border-b bg-background/80 backdrop-blur-sm flex justify-between items-center">
        <h2 className="text-sm font-medium">Output</h2>
        <div className="flex gap-2">
          {onApplyCode && (
            <Button variant="outline" size="sm" onClick={onApplyCode} disabled={isProcessing} className="rounded-full">
              <Code className="h-4 w-4 mr-2" />
              Apply Code
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={handleCopy}
            disabled={isProcessing || !output}
            className="rounded-full"
          >
            {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
            {copied ? "Copied" : "Copy"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={onDownloadOutput}
            disabled={isProcessing || !output}
            className="rounded-full"
          >
            <Download className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <div className="border-b px-4">
          <TabsList className="h-10 rounded-full">
            <TabsTrigger value="result" className="rounded-full">
              Result
            </TabsTrigger>
            <TabsTrigger value="logs" className="rounded-full">
              Logs
            </TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="result" className="flex-1 p-0 m-0 overflow-auto">
          <div className="p-4 h-full overflow-auto">
            {isProcessing ? (
              <div className="flex items-center gap-2 text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Processing with Ollama...
              </div>
            ) : output ? (
              <div className="prose prose-sm dark:prose-invert max-w-none">
                <ReactMarkdown components={components}>{output}</ReactMarkdown>
              </div>
            ) : (
              <span className="text-muted-foreground">Run your code to see results</span>
            )}
          </div>
        </TabsContent>

        <TabsContent value="logs" className="flex-1 p-0 m-0 overflow-auto">
          <div className="p-4 font-mono text-sm whitespace-pre-wrap h-full overflow-auto">
            {logs.length === 0 ? (
              <span className="text-muted-foreground">No logs yet</span>
            ) : (
              <>
                {logs.map((log, index) => (
                  <div key={index} className="mb-1">
                    {log}
                  </div>
                ))}
                <div ref={logsEndRef} />
              </>
            )}
            {isProcessing && (
              <div className="flex items-center gap-2 text-muted-foreground mt-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Processing...
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}

