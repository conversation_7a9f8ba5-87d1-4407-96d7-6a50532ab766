"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from "@/contexts/auth-context"
import { Users, ChevronDown, LogOut } from "lucide-react"
import type { User } from "@/contexts/auth-context"

export function UserSwitcher() {
  const { user, login, logout, getAllUsers } = useAuth()
  const { toast } = useToast()
  const [isOpen, setIsOpen] = useState(false)

  const users = getAllUsers()

  // Get initials for avatar
  const getInitials = (name: string): string => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .substring(0, 2)
  }

  // Format date for display
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString)
    return date.toLocaleDateString(undefined, {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const switchUser = async (selectedUser: User) => {
    if (selectedUser.id === user?.id) return

    // We need to "login" as this user
    const result = await login(selectedUser.email, "") // Password is not used here since we're bypassing normal auth

    if (result.success) {
      toast({
        title: "User switched",
        description: `Switched to ${selectedUser.name}'s account`,
      })
    } else {
      toast({
        title: "Switch failed",
        description: "Failed to switch user. Please try again.",
        variant: "destructive",
      })
    }

    setIsOpen(false)
  }

  const handleLogout = () => {
    logout()
    toast({
      title: "Logged out",
      description: "You have been logged out successfully.",
    })
    setIsOpen(false)
  }

  if (!user) return null

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="flex items-center gap-2 rounded-full">
          <Users className="h-4 w-4" />
          <span className="max-w-[100px] truncate hidden md:inline-block">Switch User</span>
          <ChevronDown className="h-4 w-4 opacity-50" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-[250px]">
        <DropdownMenuLabel>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span>Available Accounts</span>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        {users.map((account) => (
          <DropdownMenuItem
            key={account.id}
            onClick={() => switchUser(account)}
            className="flex items-center gap-2 cursor-pointer"
          >
            <Avatar className="h-6 w-6">
              {account.avatar ? (
                <AvatarImage src={account.avatar} alt={account.name} />
              ) : (
                <AvatarFallback>{getInitials(account.name)}</AvatarFallback>
              )}
            </Avatar>
            <div className="flex-1 overflow-hidden">
              <div className="font-medium truncate">{account.name}</div>
              <div className="text-xs text-muted-foreground truncate">{account.email}</div>
            </div>
            {user.id === account.id && <div className="h-2 w-2 rounded-full bg-green-500"></div>}
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator />

        <DropdownMenuItem
          onClick={handleLogout}
          className="flex items-center gap-2 cursor-pointer text-destructive focus:text-destructive"
        >
          <LogOut className="h-4 w-4" />
          <span>Logout</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

