"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  <PERSON>alogFooter,
  DialogHeader,
  DialogT<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { Slider } from "@/components/ui/slider"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Settings, Info, Minus, Plus, Code, ExternalLink, Github, AlertCircle } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useToast } from "@/hooks/use-toast"
import { <PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>ontent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { cn } from "@/lib/utils"
import { ThemeSelector } from "@/components/theme-selector"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

const apiFormSchema = z.object({
  connectionMode: z.enum(["local", "auto", "custom"]),
  apiUrl: z.string().min(1, { message: "API URL is required" }),
  apiKey: z.string().optional(),
})

const appearanceFormSchema = z.object({
  darkMode: z.boolean().default(true),
  fontSize: z.number().min(10).max(24),
  minimap: z.boolean().default(false),
  wordWrap: z.boolean().default(true),
  tabSize: z.number().min(1).max(8),
  showLineNumbers: z.boolean().default(true),
  showIndentGuides: z.boolean().default(true),
  showInvisibles: z.boolean().default(false),
  autoClosingBrackets: z.boolean().default(true),
  autoIndent: z.boolean().default(true),
  smoothScrolling: z.boolean().default(true),
  quickSuggestions: z.boolean().default(true),
  autoSave: z.boolean().default(true),
  formatOnSave: z.boolean().default(true),
})

type ApiFormValues = z.infer<typeof apiFormSchema>
type AppearanceFormValues = z.infer<typeof appearanceFormSchema>

interface SettingsDialogProps {
  settings: {
    connectionMode?: "local" | "auto" | "custom"
    apiUrl?: string
    apiKey?: string
    darkMode: boolean
    fontSize: number
    minimap: boolean
    wordWrap: boolean
    tabSize: number
    showLineNumbers: boolean
    showIndentGuides: boolean
    showInvisibles: boolean
    autoClosingBrackets: boolean
    autoIndent: boolean
    smoothScrolling: boolean
    quickSuggestions: boolean
    autoSave: boolean
    formatOnSave: boolean
    themeColor?: string
  }
  onSettingsChange: (settings: any) => void
  showButton?: boolean
  onShowDeployedGuide?: () => void
  isDeployed?: boolean
}

export function SettingsDialog({ settings, onSettingsChange, showButton, onShowDeployedGuide, isDeployed = false }: SettingsDialogProps) {
  const [open, setOpen] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const { toast } = useToast()
  const [theme, setTheme] = useState(settings.themeColor || "slate")

  // Mount detection to prevent hydration issues
  useEffect(() => {
    setIsMounted(true)
  }, [])

  // Determine the connection mode based on the apiUrl
  const determineConnectionMode = (apiUrl: string): "local" | "auto" | "custom" => {
    if (apiUrl === "http://localhost:11434") return "local"
    if (apiUrl === "auto") return "auto"
    return "custom"
  }

  const apiForm = useForm<ApiFormValues>({
    resolver: zodResolver(apiFormSchema),
    defaultValues: {
      connectionMode: settings?.connectionMode || determineConnectionMode(settings?.apiUrl || "auto"),
      apiUrl: settings?.apiUrl || "auto",
      apiKey: settings?.apiKey || "",
    },
  })

  // Watch for connection mode changes to update the API URL field
  const connectionMode = apiForm.watch("connectionMode")

  useEffect(() => {
    if (connectionMode === "local") {
      apiForm.setValue("apiUrl", "http://localhost:11434")
    } else if (connectionMode === "auto") {
      apiForm.setValue("apiUrl", "auto")
    }
    // If custom, leave the URL as is
  }, [connectionMode, apiForm])

  const appearanceForm = useForm<AppearanceFormValues>({
    resolver: zodResolver(appearanceFormSchema),
    defaultValues: {
      darkMode: settings?.darkMode ?? true,
      fontSize: settings?.fontSize ?? 14,
      minimap: settings?.minimap ?? false,
      wordWrap: settings?.wordWrap ?? true,
      tabSize: settings?.tabSize ?? 2,
      showLineNumbers: settings?.showLineNumbers ?? true,
      showIndentGuides: settings?.showIndentGuides ?? true,
      showInvisibles: settings?.showInvisibles ?? false,
      autoClosingBrackets: settings?.autoClosingBrackets ?? true,
      autoIndent: settings?.autoIndent ?? true,
      smoothScrolling: settings?.smoothScrolling ?? true,
      quickSuggestions: settings?.quickSuggestions ?? true,
      autoSave: settings?.autoSave ?? true,
      formatOnSave: settings?.formatOnSave ?? true,
    },
  })

  function onApiSubmit(data: ApiFormValues) {
    onSettingsChange({
      ...settings,
      connectionMode: data.connectionMode,
      apiUrl: data.apiUrl,
      apiKey: data.apiKey,
    })

    toast({
      title: "API settings updated",
      description: "Your API connection settings have been saved",
    })

    setOpen(false)
  }

  function onAppearanceSubmit(data: AppearanceFormValues) {
    onSettingsChange({
      ...settings,
      darkMode: data.darkMode,
      fontSize: data.fontSize,
      minimap: data.minimap,
      wordWrap: data.wordWrap,
      tabSize: data.tabSize,
      showLineNumbers: data.showLineNumbers,
      showIndentGuides: data.showIndentGuides,
      showInvisibles: data.showInvisibles,
      autoClosingBrackets: data.autoClosingBrackets,
      autoIndent: data.autoIndent,
      smoothScrolling: data.smoothScrolling,
      quickSuggestions: data.quickSuggestions,
      autoSave: data.autoSave,
      formatOnSave: data.formatOnSave,
    })

    toast({
      title: "Appearance settings updated",
      description: "Your editor appearance settings have been saved",
    })

    setOpen(false)
  }

  const handleThemeChange = (newTheme: string) => {
    // Update theme class on document
    document.documentElement.classList.forEach(className => {
      if (className.startsWith('theme-')) {
        document.documentElement.classList.remove(className)
      }
    })

    if (newTheme !== "slate") {
      document.documentElement.classList.add(`theme-${newTheme}`)
    }

    setTheme(newTheme)
    onSettingsChange({ themeColor: newTheme })
  }

  // Effect to apply theme on mount
  useEffect(() => {
    if (settings.themeColor && settings.themeColor !== "slate") {
      document.documentElement.classList.add(`theme-${settings.themeColor}`)
    }
    setTheme(settings.themeColor || "slate")
  }, [settings.themeColor])

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      {showButton !== false && (
        <DialogTrigger asChild>
          <Button variant="ghost" size="icon" id="settings-dialog-trigger">
            <Settings className="h-4 w-4" />
            <span className="sr-only">Settings</span>
          </Button>
        </DialogTrigger>
      )}
      <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Settings</DialogTitle>
          <DialogDescription>Configure your Ollama UI Builder preferences.</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="connection" className="w-full">
          <TabsList className="grid w-full grid-cols-4 mb-4">
            <TabsTrigger value="connection">Connection</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="editor">Editor</TabsTrigger>
            <TabsTrigger value="about">About</TabsTrigger>
          </TabsList>

          {/* Connection Settings */}
          <TabsContent value="connection" className="space-y-4 pt-2 pb-4">
            {isDeployed && onShowDeployedGuide && (
              <div className="mb-4">
                <Alert className="bg-blue-50 text-blue-800 border-blue-200 dark:bg-blue-900/20 dark:text-blue-400 dark:border-blue-900">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  <AlertTitle>Deployed Application</AlertTitle>
                  <AlertDescription className="mt-2">
                    <p className="mb-2">This app is running on a deployed server and needs to connect to your local Ollama instance.</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setOpen(false)
                        onShowDeployedGuide()
                      }}
                    >
                      Show Connection Guide
                    </Button>
                  </AlertDescription>
                </Alert>
              </div>
            )}

            <Form {...apiForm}>
              <form onSubmit={apiForm.handleSubmit(onApiSubmit)} className="space-y-6">
                <FormField
                  control={apiForm.control}
                  name="connectionMode"
                  render={({ field }) => (
                    <FormItem className="space-y-3">
                      <FormLabel>Connection Mode</FormLabel>
                      <FormControl>
                        <RadioGroup
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                          className="flex flex-col space-y-1"
                        >
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="local" />
                            </FormControl>
                            <FormLabel className="font-normal">Local (localhost:11434)</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="auto" />
                            </FormControl>
                            <FormLabel className="font-normal">Auto (detect from current host)</FormLabel>
                          </FormItem>
                          <FormItem className="flex items-center space-x-3 space-y-0">
                            <FormControl>
                              <RadioGroupItem value="custom" />
                            </FormControl>
                            <FormLabel className="font-normal">Custom URL</FormLabel>
                          </FormItem>
                        </RadioGroup>
                      </FormControl>
                      <FormDescription>Choose how to connect to the Ollama API</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={apiForm.control}
                  name="apiUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        Ollama API URL
                        <TooltipProvider>
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Info className="h-4 w-4 text-muted-foreground" />
                            </TooltipTrigger>
                            <TooltipContent className="max-w-[300px]">
                              <p>
                                {connectionMode === "local" && "Using localhost:11434 for local Ollama instance"}
                                {connectionMode === "auto" &&
                                  "Automatically detect the server address from current host"}
                                {connectionMode === "custom" && "Specify a custom URL like http://*************:11434"}
                              </p>
                            </TooltipContent>
                          </Tooltip>
                        </TooltipProvider>
                      </FormLabel>
                      <FormControl>
                        <Input
                          placeholder={
                            connectionMode === "local" ? "http://localhost:11434" : "http://your-server:11434"
                          }
                          {...field}
                          disabled={connectionMode !== "custom"}
                          className={cn(connectionMode !== "custom" && "opacity-70")}
                        />
                      </FormControl>
                      <FormDescription>
                        {connectionMode === "custom"
                          ? "Enter the full URL including protocol (http:// or https://)"
                          : `Using ${connectionMode === "local" ? "localhost" : "auto-detection"} mode`}
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={apiForm.control}
                  name="apiKey"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>API Key (Optional)</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="Enter API key if required" {...field} />
                      </FormControl>
                      <FormDescription>
                        Only required if your Ollama instance uses API key authentication
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter className="pt-2">
                  <Button type="submit">Save API Settings</Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>

          {/* Appearance Settings */}
          <TabsContent value="appearance" className="space-y-5 pt-2 pb-4">
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-medium leading-none mb-2">Theme Color</h3>
                <ThemeSelector currentTheme={theme} onThemeChange={handleThemeChange} />
              </div>
              <p className="text-xs text-muted-foreground">
                Select a color theme for the application interface.
              </p>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-sm font-medium leading-none mb-2">Dark Mode</h3>
                <p className="text-xs text-muted-foreground">
                  Switch between light and dark theme.
                </p>
              </div>
              <Switch
                checked={settings.darkMode}
                onCheckedChange={(checked) => onSettingsChange({ darkMode: checked })}
              />
            </div>

            <Form {...appearanceForm}>
              <form onSubmit={appearanceForm.handleSubmit(onAppearanceSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="fontSize"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Font Size: {field.value}px</FormLabel>
                        <FormControl>
                          <Slider
                            min={10}
                            max={24}
                            step={1}
                            defaultValue={[field.value]}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <FormDescription>Font size for the editor (10-24px)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="minimap"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Minimap</FormLabel>
                          <FormDescription>Show code minimap in the editor</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={appearanceForm.control}
                    name="wordWrap"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Word Wrap</FormLabel>
                          <FormDescription>Enable word wrapping in the editor</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={appearanceForm.control}
                  name="tabSize"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tab Size: {field.value} spaces</FormLabel>
                      <FormControl>
                        <Slider
                          min={1}
                          max={8}
                          step={1}
                          defaultValue={[field.value]}
                          onValueChange={(value) => field.onChange(value[0])}
                        />
                      </FormControl>
                      <FormDescription>Number of spaces for each tab</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="showLineNumbers"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Line Numbers</FormLabel>
                          <FormDescription>Show line numbers in the editor</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={appearanceForm.control}
                    name="showIndentGuides"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Indent Guides</FormLabel>
                          <FormDescription>Show vertical indent guides</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="showInvisibles"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Show Invisibles</FormLabel>
                          <FormDescription>Show whitespace characters</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={appearanceForm.control}
                    name="autoClosingBrackets"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Auto-close Brackets</FormLabel>
                          <FormDescription>Automatically close brackets and quotes</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="autoIndent"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Auto Indent</FormLabel>
                          <FormDescription>Automatically indent new lines</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={appearanceForm.control}
                    name="smoothScrolling"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Smooth Scrolling</FormLabel>
                          <FormDescription>Enable smooth scrolling in the editor</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={appearanceForm.control}
                    name="quickSuggestions"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Quick Suggestions</FormLabel>
                          <FormDescription>Show code suggestions as you type</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={appearanceForm.control}
                    name="autoSave"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Auto Save</FormLabel>
                          <FormDescription>Automatically save changes</FormDescription>
                        </div>
                        <FormControl>
                          <Switch checked={field.value} onCheckedChange={field.onChange} />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={appearanceForm.control}
                  name="formatOnSave"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                      <div className="space-y-0.5">
                        <FormLabel>Format on Save</FormLabel>
                        <FormDescription>Automatically format code when saving</FormDescription>
                      </div>
                      <FormControl>
                        <Switch checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                    </FormItem>
                  )}
                />

                <DialogFooter className="pt-2">
                  <Button type="submit">Save Editor Settings</Button>
                </DialogFooter>
              </form>
            </Form>
          </TabsContent>

          {/* Editor Settings */}
          <TabsContent value="editor" className="space-y-5 pt-2 pb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Font Size</h3>
                  <p className="text-xs text-muted-foreground">
                    Editor font size in pixels
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onSettingsChange({ fontSize: Math.max(10, settings.fontSize - 1) })}
                    disabled={settings.fontSize <= 10}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <span className="w-8 text-center">{settings.fontSize}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onSettingsChange({ fontSize: Math.min(30, settings.fontSize + 1) })}
                    disabled={settings.fontSize >= 30}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Tab Size</h3>
                  <p className="text-xs text-muted-foreground">
                    Number of spaces per tab
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onSettingsChange({ tabSize: Math.max(2, settings.tabSize - 1) })}
                    disabled={settings.tabSize <= 2}
                  >
                    <Minus className="h-3 w-3" />
                  </Button>
                  <span className="w-8 text-center">{settings.tabSize}</span>
                  <Button
                    variant="outline"
                    size="icon"
                    className="h-8 w-8"
                    onClick={() => onSettingsChange({ tabSize: Math.min(8, settings.tabSize + 1) })}
                    disabled={settings.tabSize >= 8}
                  >
                    <Plus className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>

            <Separator />

            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Word Wrap</h3>
                  <p className="text-xs text-muted-foreground">
                    Wrap long lines of code
                  </p>
                </div>
                <Switch
                  checked={settings.wordWrap}
                  onCheckedChange={(checked) => onSettingsChange({ wordWrap: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Line Numbers</h3>
                  <p className="text-xs text-muted-foreground">
                    Show line numbers in the editor
                  </p>
                </div>
                <Switch
                  checked={settings.showLineNumbers}
                  onCheckedChange={(checked) => onSettingsChange({ showLineNumbers: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Minimap</h3>
                  <p className="text-xs text-muted-foreground">
                    Show code overview minimap
                  </p>
                </div>
                <Switch
                  checked={settings.minimap}
                  onCheckedChange={(checked) => onSettingsChange({ minimap: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Indent Guides</h3>
                  <p className="text-xs text-muted-foreground">
                    Show vertical indent guides
                  </p>
                </div>
                <Switch
                  checked={settings.showIndentGuides}
                  onCheckedChange={(checked) => onSettingsChange({ showIndentGuides: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Auto Closing Brackets</h3>
                  <p className="text-xs text-muted-foreground">
                    Automatically close brackets and quotes
                  </p>
                </div>
                <Switch
                  checked={settings.autoClosingBrackets}
                  onCheckedChange={(checked) => onSettingsChange({ autoClosingBrackets: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Format on Save</h3>
                  <p className="text-xs text-muted-foreground">
                    Automatically format code when saving
                  </p>
                </div>
                <Switch
                  checked={settings.formatOnSave}
                  onCheckedChange={(checked) => onSettingsChange({ formatOnSave: checked })}
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-sm font-medium leading-none mb-2">Auto Save</h3>
                  <p className="text-xs text-muted-foreground">
                    Automatically save file changes
                  </p>
                </div>
                <Switch
                  checked={settings.autoSave}
                  onCheckedChange={(checked) => onSettingsChange({ autoSave: checked })}
                />
              </div>
            </div>
          </TabsContent>

          {/* About Tab */}
          <TabsContent value="about" className="space-y-5 pt-2 pb-4">
            <div className="text-center">
              <div className="mb-3 inline-flex items-center justify-center w-12 h-12 rounded-full bg-primary/10">
                <Code className="h-6 w-6 text-primary" />
              </div>
              <h3 className="text-lg font-semibold">Ollama UI Builder</h3>
              <p className="text-sm text-muted-foreground mb-4">Version 1.0.0</p>
            </div>

            <Separator />

            <div className="space-y-2">
              <h4 className="font-medium">About</h4>
              <p className="text-sm text-muted-foreground">
                Ollama UI Builder is an open-source interface for interacting with Ollama LLMs.
                It provides a user-friendly way to generate and edit code using local AI models.
              </p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Resources</h4>
              <div className="grid grid-cols-2 gap-2">
                <Button variant="outline" className="justify-start" onClick={() => window.open("https://ollama.com/", "_blank")}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Ollama Website
                </Button>
                <Button variant="outline" className="justify-start" onClick={() => window.open("https://github.com/ollama/ollama", "_blank")}>
                  <Github className="mr-2 h-4 w-4" />
                  GitHub Repository
                </Button>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

