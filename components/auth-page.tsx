"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { LoginForm } from "@/components/login-form"
import { SignupForm } from "@/components/signup-form"
import { Bo<PERSON> } from "lucide-react"

interface AuthPageProps {
  onAuthSuccess: () => void
}

export function AuthPage({ onAuthSuccess }: AuthPageProps) {
  const [isLogin, setIsLogin] = useState(true)

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4 bg-background">
      <div className="w-full max-w-md">
        <div className="mb-8 text-center">
          <div className="flex justify-center mb-4">
            <div className="bg-primary text-primary-foreground p-3 rounded-full">
              <Bot className="h-8 w-8" />
            </div>
          </div>
          <h1 className="text-3xl font-bold">Ollama UI Builder</h1>
          <p className="text-muted-foreground mt-2">Your AI-powered code generation environment</p>
        </div>

        <Card className="border-2">
          <CardContent className="pt-6">
            {isLogin ? (
              <LoginForm onSuccess={onAuthSuccess} onSwitchToSignup={() => setIsLogin(false)} />
            ) : (
              <SignupForm onSuccess={onAuthSuccess} onSwitchToLogin={() => setIsLogin(true)} />
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

