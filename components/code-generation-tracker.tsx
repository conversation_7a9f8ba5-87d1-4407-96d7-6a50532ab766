"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { CheckCircle, Circle, AlertCircle, Clock } from "lucide-react"
import { cn } from "@/lib/utils"

export interface GenerationStep {
  id: string
  name: string
  status: "pending" | "in-progress" | "completed" | "error"
  description: string
}

interface CodeGenerationTrackerProps {
  steps: GenerationStep[]
  currentStepId: string | null
}

export default function CodeGenerationTracker({ steps, currentStepId }: CodeGenerationTrackerProps) {
  return (
    <Card className="h-full overflow-auto">
      <CardHeader className="p-3 md:p-4">
        <CardTitle className="text-base md:text-lg">Code Generation Progress</CardTitle>
      </CardHeader>
      <CardContent className="p-2 md:p-4 pt-0 md:pt-0">
        <div className="space-y-4 md:space-y-6">
          {steps.map((step, index) => (
            <div key={step.id} className="relative">
              {/* Connector line */}
              {index < steps.length - 1 && (
                <div
                  className={cn(
                    "absolute left-3 md:left-3.5 top-6 bottom-0 w-0.5",
                    step.status === "completed" ? "bg-primary" : "bg-muted",
                  )}
                />
              )}

              <div className="flex items-start gap-2 md:gap-4">
                <div className="flex-shrink-0 mt-1">
                  {step.status === "completed" ? (
                    <CheckCircle className="h-6 w-6 md:h-7 md:w-7 text-primary" />
                  ) : step.status === "in-progress" ? (
                    <Circle className="h-6 w-6 md:h-7 md:w-7 text-primary animate-pulse" />
                  ) : step.status === "error" ? (
                    <AlertCircle className="h-6 w-6 md:h-7 md:w-7 text-destructive" />
                  ) : (
                    <Clock className="h-6 w-6 md:h-7 md:w-7 text-muted-foreground" />
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h3
                    className={cn(
                      "text-base md:text-lg font-medium truncate",
                      step.status === "completed" && "text-primary",
                      step.status === "in-progress" && "text-primary",
                      step.status === "error" && "text-destructive",
                    )}
                  >
                    {step.name}
                  </h3>
                  <p className="text-xs md:text-sm text-muted-foreground">{step.description}</p>
                </div>
                <div className="text-xs md:text-sm text-muted-foreground whitespace-nowrap">
                  {step.status === "completed" && "Completed"}
                  {step.status === "in-progress" && "In progress..."}
                  {step.status === "error" && "Error"}
                  {step.status === "pending" && "Pending"}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}

