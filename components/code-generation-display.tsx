"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2, FileCode, Download } from "lucide-react"
import EditableCodeBlock from "@/components/editable-code-block"
import { cn } from "@/lib/utils"

interface CodeFile {
  name: string
  content: string
  language: string
}

interface CodeGenerationDisplayProps {
  files: CodeFile[]
  isGenerating: boolean
  onApplyAllCode: () => void
}

export default function CodeGenerationDisplay({ files, isGenerating, onApplyAllCode }: CodeGenerationDisplayProps) {
  const [activeFile, setActiveFile] = useState<string | null>(files.length > 0 ? files[0].name : null)

  if (isGenerating) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <h3 className="text-lg font-medium">Generating code...</h3>
          <p className="text-muted-foreground">Please wait while we create your files</p>
        </div>
      </div>
    )
  }

  if (files.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <FileCode className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
          <h3 className="text-lg font-medium">No code generated yet</h3>
          <p className="text-muted-foreground">
            Ask the assistant to create something for you, and the generated code will appear here.
          </p>
        </div>
      </div>
    )
  }

  return (
    <Card className="h-full flex flex-col">
      <CardHeader className="p-2 md:p-4 flex-shrink-0 border-b">
        <div className="flex justify-between items-center">
          <CardTitle className="text-base md:text-lg">Generated Files</CardTitle>
          <Button onClick={onApplyAllCode} className="flex items-center gap-1 md:gap-2 text-xs md:text-sm h-8 md:h-9">
            <Download className="h-3 w-3 md:h-4 md:w-4" />
            <span className="hidden sm:inline">Apply All Code</span>
            <span className="sm:hidden">Apply</span>
          </Button>
        </div>
      </CardHeader>
      <CardContent className="p-0 flex-1 flex flex-col md:flex-row overflow-hidden">
        <div className="w-full md:w-64 border-r flex-shrink-0 overflow-auto max-h-[200px] md:max-h-none md:h-auto">
          <div className="p-2">
            <h3 className="text-xs md:text-sm font-medium px-2 md:px-3 py-1 md:py-2">Files ({files.length})</h3>
            <div className="space-y-1">
              {files.map((file) => (
                <button
                  key={file.name}
                  onClick={() => setActiveFile(file.name)}
                  className={cn(
                    "w-full text-left px-2 md:px-3 py-1 md:py-2 rounded-lg text-xs md:text-sm",
                    activeFile === file.name ? "bg-primary text-primary-foreground" : "hover:bg-muted",
                  )}
                >
                  {file.name}
                </button>
              ))}
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-auto p-2 md:p-4">
          {activeFile && (
            <EditableCodeBlock
              language={files.find((f) => f.name === activeFile)?.language || "text"}
              code={files.find((f) => f.name === activeFile)?.content || ""}
              readOnly={false}
            />
          )}
        </div>
      </CardContent>
    </Card>
  )
}

