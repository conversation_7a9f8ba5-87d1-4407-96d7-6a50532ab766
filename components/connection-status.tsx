"use client"

import { useState, useEffect } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ipContent, <PERSON>ltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { <PERSON><PERSON>2, WifiOff, Wifi, Refresh<PERSON><PERSON> } from "lucide-react"
import ConnectionManager from "@/lib/connection-manager"
import { cn } from "@/lib/utils"

interface ConnectionStatusProps {
  className?: string
}

export default function ConnectionStatus({ className }: ConnectionStatusProps) {
  const [status, setStatus] = useState({
    connected: false,
    error: null as string | null,
    lastChecked: null as Date | null,
    models: [] as string[],
    checking: false,
  })

  useEffect(() => {
    const connectionManager = ConnectionManager.getInstance()

    // Add listener for status updates
    const handleStatusChange = (newStatus: any) => {
      setStatus((prev) => ({ ...prev, ...newStatus, checking: false }))
    }

    connectionManager.addConnectionListener(handleStatusChange)

    // Initial check
    setStatus((prev) => ({ ...prev, checking: true }))
    connectionManager.checkConnection().then(() => {
      setStatus((prev) => ({ ...prev, checking: false }))
    })

    // Start periodic checks
    connectionManager.startPeriodicChecks()

    // Cleanup
    return () => {
      connectionManager.removeConnectionListener(handleStatusChange)
      connectionManager.stopPeriodicChecks()
    }
  }, [])

  const handleManualCheck = async () => {
    setStatus((prev) => ({ ...prev, checking: true }))
    await ConnectionManager.getInstance().checkConnection()
    setStatus((prev) => ({ ...prev, checking: false }))
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge
              variant={status.connected ? "default" : "destructive"}
              className="flex items-center gap-1 cursor-help"
            >
              {status.checking ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : status.connected ? (
                <Wifi className="h-3 w-3" />
              ) : (
                <WifiOff className="h-3 w-3" />
              )}
              <span className="hidden sm:inline">
                {status.checking ? "Checking..." : status.connected ? "Connected" : "Disconnected"}
              </span>
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2 max-w-xs">
              <p className="font-medium">
                {status.connected ? "Connected to Ollama API" : "Not connected to Ollama API"}
              </p>
              {status.error && <p className="text-destructive text-xs">{status.error}</p>}
              {status.connected && <p className="text-xs">{status.models.length} models available</p>}
              {status.lastChecked && (
                <p className="text-xs text-muted-foreground">Last checked: {status.lastChecked.toLocaleTimeString()}</p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Button
        variant="ghost"
        size="icon"
        onClick={handleManualCheck}
        disabled={status.checking}
        className="h-6 w-6 rounded-full"
      >
        <RefreshCw className={cn("h-3 w-3", status.checking && "animate-spin")} />
        <span className="sr-only">Refresh connection</span>
      </Button>
    </div>
  )
}

