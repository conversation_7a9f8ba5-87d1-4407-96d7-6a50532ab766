"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Loader2, Send, User, Bot, Co<PERSON>, Code, Sparkles, Trash2 } from "lucide-react"
import { useToast } from "@/hooks/use-toast"
import { getOllamaAPI, type GenerateRequestOptions } from "@/lib/ollama-api"
import ReactMarkdown from "react-markdown"
import { Prism as SyntaxHighlighter } from "react-syntax-highlighter"
import { vscDarkPlus, vs } from "react-syntax-highlighter/dist/esm/styles/prism"
import { useTheme } from "next-themes"
import { cn } from "@/lib/utils"
import { v4 as uuidv4 } from "uuid"

interface Message {
  id: string
  role: "user" | "assistant"
  content: string
  timestamp: Date
  pending?: boolean
}

interface ChatInterfaceProps {
  selectedModel: string
  onCreateFile: (file: {
    id: string
    name: string
    content: string
    language: string
    lastModified: Date
  }) => void
  temperature: number
}

export default function ChatInterface({ selectedModel, onCreateFile, temperature }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: uuidv4(),
      role: "assistant",
      content: "Hi! I'm your Nero Dev coding assistant. How can I help you build something today?",
      timestamp: new Date(),
    },
  ])
  const [input, setInput] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [chatHistory, setChatHistory] = useState<{ title: string; id: string; preview: string }[]>([])
  const [activeChatId, setActiveChatId] = useState<string | null>(null)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)
  const { toast } = useToast()
  const { theme } = useTheme()

  // Scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
  }, [messages])

  // Focus input when component mounts
  useEffect(() => {
    inputRef.current?.focus()
  }, [])

  const handleSendMessage = async () => {
    if (!input.trim() || isProcessing) return

    const userMessage: Message = {
      id: uuidv4(),
      role: "user",
      content: input,
      timestamp: new Date(),
    }

    // Add user message to chat
    setMessages((prev) => [...prev, userMessage])

    // Clear input
    setInput("")

    // Create pending message
    const pendingId = uuidv4()
    const pendingMessage: Message = {
      id: pendingId,
      role: "assistant",
      content: "",
      timestamp: new Date(),
      pending: true,
    }

    setMessages((prev) => [...prev, pendingMessage])
    setIsProcessing(true)

    try {
      const ollamaApi = getOllamaAPI()

      // Prepare context from previous messages (limit to last 10 for performance)
      const recentMessages = messages
        .slice(-10)
        .map((msg) => `${msg.role === "user" ? "User" : "Assistant"}: ${msg.content}`)
        .join("\n\n")

      const prompt = `You are a helpful coding assistant that helps users build software. 
      
Previous conversation:
${recentMessages}

User: ${input}

Provide a helpful, detailed response. If the user asks for code, provide complete, working code examples with explanations. Use markdown formatting for code blocks with the appropriate language specified.`

      let fullResponse = ""

      // Prepare model parameters
      const options: GenerateRequestOptions = {
        model: selectedModel,
        prompt,
        stream: true,
        options: {
          temperature,
          top_p: 0.9,
          top_k: 40,
          num_predict: 4096,
          presence_penalty: 0.0,
          frequency_penalty: 0.0,
          stop: ["User:", "\n\nUser"],
        },
      }

      await ollamaApi.streamCompletion(
        options,
        (chunk) => {
          fullResponse += chunk.response

          // Update the pending message with the current response
          setMessages((prev) =>
            prev.map((msg) => (msg.id === pendingId ? { ...msg, content: fullResponse, pending: true } : msg)),
          )
        },
        (error) => {
          console.error("Error in chat:", error)
          toast({
            title: "Error",
            description: `Failed to get a response: ${error.message}`,
            variant: "destructive",
          })

          // Remove the pending message
          setMessages((prev) => prev.filter((msg) => msg.id !== pendingId))
        },
        () => {
          // Mark message as no longer pending when complete
          setMessages((prev) =>
            prev.map((msg) => (msg.id === pendingId ? { ...msg, content: fullResponse, pending: false } : msg)),
          )

          // Save chat to history if this is a new chat
          if (!activeChatId) {
            const newChatId = uuidv4()
            const title = generateChatTitle(userMessage.content)
            setChatHistory((prev) => [
              { id: newChatId, title, preview: userMessage.content.substring(0, 60) + "..." },
              ...prev,
            ])
            setActiveChatId(newChatId)

            // Save chat to localStorage
            saveChat(newChatId, title, [
              ...messages,
              userMessage,
              { ...pendingMessage, content: fullResponse, pending: false },
            ])
          } else {
            // Update existing chat
            saveChat(activeChatId, chatHistory.find((chat) => chat.id === activeChatId)?.title || "Chat", [
              ...messages,
              { ...pendingMessage, content: fullResponse, pending: false },
            ])
          }
        },
      )
    } catch (error) {
      console.error("Error sending message:", error)
      toast({
        title: "Error",
        description: "Failed to send message. Please try again.",
        variant: "destructive",
      })

      // Remove the pending message
      setMessages((prev) => prev.filter((msg) => msg.id !== pendingId))
    } finally {
      setIsProcessing(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const generateChatTitle = (firstMessage: string): string => {
    // Generate a title based on the first user message
    const words = firstMessage.split(" ")
    if (words.length <= 5) return firstMessage
    return words.slice(0, 5).join(" ") + "..."
  }

  const saveChat = (id: string, title: string, messages: Message[]) => {
    try {
      localStorage.setItem(`chat-${id}`, JSON.stringify({ title, messages }))
    } catch (error) {
      console.error("Error saving chat:", error)
    }
  }

  const loadChat = (id: string) => {
    try {
      const chatData = localStorage.getItem(`chat-${id}`)
      if (chatData) {
        const { messages: chatMessages } = JSON.parse(chatData)
        setMessages(chatMessages)
        setActiveChatId(id)
      }
    } catch (error) {
      console.error("Error loading chat:", error)
      toast({
        title: "Error",
        description: "Failed to load chat history.",
        variant: "destructive",
      })
    }
  }

  const startNewChat = () => {
    setMessages([
      {
        id: uuidv4(),
        role: "assistant",
        content: "Hi! I'm your Nero Dev coding assistant. How can I help you build something today?",
        timestamp: new Date(),
      },
    ])
    setActiveChatId(null)
    setIsMobileMenuOpen(false)
  }

  const deleteChat = (id: string, e: React.MouseEvent) => {
    e.stopPropagation()
    try {
      localStorage.removeItem(`chat-${id}`)
      setChatHistory((prev) => prev.filter((chat) => chat.id !== id))

      if (activeChatId === id) {
        startNewChat()
      }
    } catch (error) {
      console.error("Error deleting chat:", error)
    }
  }

  const extractAndCreateFile = (content: string) => {
    // Extract code blocks from markdown
    const codeBlockRegex = /```(\w+)?\s*\n([\s\S]*?)\n```/g
    const matches = [...content.matchAll(codeBlockRegex)]

    if (matches.length === 0) {
      toast({
        title: "No code found",
        description: "No code blocks were found in the message.",
        variant: "destructive",
      })
      return
    }

    // Use the last code block
    const lastMatch = matches[matches.length - 1]
    const language = lastMatch[1] || "text"
    const code = lastMatch[2]

    // Map language to file extension
    const languageToExt: Record<string, string> = {
      javascript: "js",
      typescript: "ts",
      jsx: "jsx",
      tsx: "tsx",
      python: "py",
      html: "html",
      css: "css",
      json: "json",
      java: "java",
      cpp: "cpp",
      c: "c",
      csharp: "cs",
      go: "go",
      rust: "rs",
      ruby: "rb",
      php: "php",
      swift: "swift",
      kotlin: "kt",
      text: "txt",
    }

    const ext = languageToExt[language] || "txt"
    const fileName = `generated-${new Date().toISOString().slice(0, 10)}.${ext}`

    onCreateFile({
      id: uuidv4(),
      name: fileName,
      content: code,
      language,
      lastModified: new Date(),
    })

    toast({
      title: "File created",
      description: `Created ${fileName} from the code block.`,
    })
  }

  // Load chat history from localStorage on component mount
  useEffect(() => {
    try {
      const chatIds = Object.keys(localStorage)
        .filter((key) => key.startsWith("chat-"))
        .map((key) => key.replace("chat-", ""))

      const loadedHistory = chatIds
        .map((id) => {
          const chatData = localStorage.getItem(`chat-${id}`)
          if (!chatData) return null

          const { title, messages } = JSON.parse(chatData)
          const preview =
            messages.find((msg: Message) => msg.role === "user")?.content.substring(0, 60) + "..." || "..."

          return { id, title, preview }
        })
        .filter(Boolean)

      setChatHistory(loadedHistory)
    } catch (error) {
      console.error("Error loading chat history:", error)
    }
  }, [])

  // Custom renderer for code blocks in markdown
  const components = {
    code({ node, inline, className, children, ...props }: any) {
      const match = /language-(\w+)/.exec(className || "")
      return !inline && match ? (
        <SyntaxHighlighter style={theme === "dark" ? vscDarkPlus : vs} language={match[1]} PreTag="div" {...props}>
          {String(children).replace(/\n$/, "")}
        </SyntaxHighlighter>
      ) : (
        <code className={cn("bg-muted px-1 py-0.5 rounded text-sm", className)} {...props}>
          {children}
        </code>
      )
    },
  }

  return (
    <div className="flex h-full">
      {/* Chat history sidebar */}
      <div
        className={cn(
          "w-64 border-r bg-background flex-shrink-0 h-full overflow-hidden transition-all",
          isMobileMenuOpen ? "block absolute z-10 h-full" : "hidden md:block",
        )}
      >
        <div className="p-4 border-b">
          <Button onClick={startNewChat} className="w-full">
            <Sparkles className="mr-2 h-4 w-4" />
            New Chat
          </Button>
        </div>
        <ScrollArea className="h-[calc(100%-65px)]">
          <div className="p-2 space-y-2">
            {chatHistory.length === 0 ? (
              <div className="text-center text-muted-foreground p-4">No chat history yet</div>
            ) : (
              chatHistory.map((chat) => (
                <div
                  key={chat.id}
                  onClick={() => {
                    loadChat(chat.id)
                    setIsMobileMenuOpen(false)
                  }}
                  className={cn(
                    "p-3 rounded-md cursor-pointer hover:bg-muted flex justify-between items-start group",
                    activeChatId === chat.id && "bg-muted",
                  )}
                >
                  <div className="flex-1 overflow-hidden">
                    <div className="font-medium truncate">{chat.title}</div>
                    <div className="text-xs text-muted-foreground truncate">{chat.preview}</div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="opacity-0 group-hover:opacity-100 h-6 w-6"
                    onClick={(e) => deleteChat(chat.id, e)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))
            )}
          </div>
        </ScrollArea>
      </div>

      {/* Main chat area */}
      <div className="flex-1 flex flex-col h-full overflow-hidden">
        <div className="p-4 border-b flex justify-between items-center">
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden mr-2"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <Bot className="h-5 w-5" />
            </Button>
            <h2 className="text-lg font-medium">
              {activeChatId ? chatHistory.find((chat) => chat.id === activeChatId)?.title || "Chat" : "New Chat"}
            </h2>
          </div>
          <div className="text-sm text-muted-foreground">
            Using model: <span className="font-medium">{selectedModel}</span>
          </div>
        </div>

        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4 max-w-3xl mx-auto">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn("flex items-start gap-3", message.role === "user" ? "justify-end" : "justify-start")}
              >
                {message.role === "assistant" && (
                  <Avatar className="h-8 w-8 mt-1">
                    <Bot className="h-5 w-5" />
                  </Avatar>
                )}
                <Card
                  className={cn(
                    "p-4 max-w-[80%]",
                    message.role === "user" ? "bg-primary text-primary-foreground" : "bg-muted",
                  )}
                >
                  {message.role === "assistant" ? (
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      {message.pending ? (
                        <div className="flex items-center gap-2">
                          <div className="relative">
                            <div className="h-2 w-2 rounded-full bg-muted-foreground animate-ping absolute"></div>
                            <div className="h-2 w-2 rounded-full bg-muted-foreground"></div>
                          </div>
                          <div className="h-2 w-2 rounded-full bg-muted-foreground animate-ping delay-150"></div>
                          <div className="h-2 w-2 rounded-full bg-muted-foreground animate-ping delay-300"></div>
                          {message.content && <ReactMarkdown components={components}>{message.content}</ReactMarkdown>}
                        </div>
                      ) : (
                        <div className="relative group">
                          <ReactMarkdown components={components}>{message.content}</ReactMarkdown>
                          <div className="absolute top-0 right-0 opacity-0 group-hover:opacity-100 transition-opacity">
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => {
                                navigator.clipboard.writeText(message.content)
                                toast({
                                  title: "Copied to clipboard",
                                  description: "Message content copied to clipboard",
                                })
                              }}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8"
                              onClick={() => extractAndCreateFile(message.content)}
                            >
                              <Code className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="whitespace-pre-wrap">{message.content}</div>
                  )}
                  <div
                    className={cn(
                      "text-xs mt-2",
                      message.role === "user" ? "text-primary-foreground/70" : "text-muted-foreground",
                    )}
                  >
                    {message.timestamp.toLocaleTimeString()}
                  </div>
                </Card>
                {message.role === "user" && (
                  <Avatar className="h-8 w-8 mt-1">
                    <User className="h-5 w-5" />
                  </Avatar>
                )}
              </div>
            ))}
            <div ref={messagesEndRef} />
          </div>
        </ScrollArea>

        <div className="p-4 border-t">
          <div className="max-w-3xl mx-auto">
            <div className="flex gap-2">
              <Textarea
                ref={inputRef}
                value={input}
                onChange={(e) => setInput(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Ask me to build something for you..."
                className="min-h-[60px] resize-none"
                disabled={isProcessing}
              />
              <Button onClick={handleSendMessage} disabled={!input.trim() || isProcessing} className="self-end">
                {isProcessing ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
              </Button>
            </div>
            <div className="text-xs text-muted-foreground mt-2">Press Enter to send, Shift+Enter for new line</div>
          </div>
        </div>
      </div>
    </div>
  )
}

