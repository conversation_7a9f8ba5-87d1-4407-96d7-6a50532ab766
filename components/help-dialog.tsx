"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { ExternalLink, HelpCircle } from "lucide-react"

export default function HelpDialog() {
  const [open, setOpen] = useState(false)

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="icon" className="rounded-full">
          <HelpCircle className="h-5 w-5" />
          <span className="sr-only">Help</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Help & Documentation</DialogTitle>
          <DialogDescription>Learn how to use Ollama UI Builder effectively</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="getting-started" className="mt-4">
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="getting-started">Getting Started</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="faq">FAQ</TabsTrigger>
          </TabsList>

          <TabsContent value="getting-started" className="space-y-4 mt-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Installation</h3>
              <p className="text-sm text-muted-foreground">
                Before using Ollama UI Builder, you need to install Ollama on your system:
              </p>
              <div className="rounded-md bg-muted p-3">
                <a
                  href="https://ollama.ai/download"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-blue-500 hover:underline"
                >
                  Download Ollama
                  <ExternalLink className="h-3 w-3" />
                </a>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Connecting to Ollama</h3>
              <p className="text-sm text-muted-foreground">
                Ollama UI Builder connects to your local Ollama instance by default. You can change the connection
                settings by clicking the Settings button in the top right corner.
              </p>
              <div className="rounded-md bg-muted p-3 text-sm">
                <p>
                  Default connection: <code>http://localhost:11434</code>
                </p>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Using Models</h3>
              <p className="text-sm text-muted-foreground">
                To use a model, you first need to pull it using the Ollama CLI:
              </p>
              <div className="rounded-md bg-muted p-3 font-mono text-sm">
                <p>ollama pull llama3</p>
              </div>
              <p className="text-sm text-muted-foreground">
                Once pulled, the model will appear in the dropdown menu in the UI.
              </p>
            </div>
          </TabsContent>

          <TabsContent value="features" className="space-y-4 mt-4">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Chat Interface</h3>
              <p className="text-sm text-muted-foreground">
                The chat interface allows you to interact with Ollama models using natural language. You can ask the
                model to generate code, explain concepts, or help you build applications.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Code Generation</h3>
              <p className="text-sm text-muted-foreground">
                Ollama UI Builder can generate code for various frameworks and languages. Simply describe what you want
                to build, and the model will generate the code for you.
              </p>
              <div className="rounded-md bg-muted p-3 text-sm">
                <p>Example prompt: "Create a responsive navbar with a logo, links, and a mobile menu"</p>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Code Editor</h3>
              <p className="text-sm text-muted-foreground">
                The built-in code editor allows you to edit and preview generated code. You can also create new files,
                save your work, and export it for use in your projects.
              </p>
            </div>

            <div className="space-y-2">
              <h3 className="text-lg font-medium">Multi-Framework Support</h3>
              <p className="text-sm text-muted-foreground">
                Ollama UI Builder supports multiple frameworks and languages, including:
              </p>
              <ul className="list-disc list-inside text-sm text-muted-foreground">
                <li>HTML, CSS, JavaScript</li>
                <li>React</li>
                <li>Next.js</li>
                <li>Flutter</li>
                <li>Bootstrap</li>
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="faq" className="mt-4">
            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger>Do I need an internet connection?</AccordionTrigger>
                <AccordionContent>
                  <p className="text-sm text-muted-foreground">
                    No, Ollama runs locally on your machine. Once you've downloaded the models, you can use Ollama UI
                    Builder without an internet connection.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2">
                <AccordionTrigger>How do I add more models?</AccordionTrigger>
                <AccordionContent>
                  <p className="text-sm text-muted-foreground">
                    You can add more models using the Ollama CLI. Open a terminal and run:
                  </p>
                  <div className="rounded-md bg-muted p-3 font-mono text-sm mt-2">
                    <p>ollama pull model-name</p>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Replace "model-name" with the name of the model you want to add. You can find a list of available
                    models on the{" "}
                    <a
                      href="https://ollama.ai/library"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-500 hover:underline"
                    >
                      Ollama website
                    </a>
                    .
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3">
                <AccordionTrigger>Can I use this for commercial projects?</AccordionTrigger>
                <AccordionContent>
                  <p className="text-sm text-muted-foreground">
                    Yes, you can use the code generated by Ollama UI Builder for commercial projects. However, please
                    check the license of the specific model you're using, as some models may have restrictions.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4">
                <AccordionTrigger>How do I save my work?</AccordionTrigger>
                <AccordionContent>
                  <p className="text-sm text-muted-foreground">
                    Your chat history and generated code are automatically saved in your browser's local storage. You
                    can also export your code by clicking the download button in the code editor.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5">
                <AccordionTrigger>What if I encounter an error?</AccordionTrigger>
                <AccordionContent>
                  <p className="text-sm text-muted-foreground">If you encounter an error, check the following:</p>
                  <ul className="list-disc list-inside text-sm text-muted-foreground mt-2">
                    <li>Make sure Ollama is running on your machine</li>
                    <li>Check your connection settings in the Settings dialog</li>
                    <li>Ensure you have pulled the model you're trying to use</li>
                    <li>Try refreshing the page or restarting Ollama</li>
                  </ul>
                </AccordionContent>
              </AccordionItem>
            </Accordion>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  )
}

