# Advanced Agent Capabilities

This document provides detailed information about the advanced agent capabilities and reasoning engine implemented in the Ollama UI Builder.

## Table of Contents

1. [Overview](#overview)
2. [Reasoning Engine](#reasoning-engine)
3. [Task Management System](#task-management-system)
4. [Agent Services](#agent-services)
5. [Thinking Process](#thinking-process)
6. [Integration with Ollama](#integration-with-ollama)
7. [Use Cases](#use-cases)
8. [Development Guide](#development-guide)

## Overview

The Ollama UI Builder implements an advanced agent architecture that enhances the application's intelligence and capabilities beyond a simple chat interface. This system enables:

- Multi-stage reasoning for complex requests
- Task classification and specialized handling
- Intelligent API orchestration
- Contextual response generation
- Error recovery and graceful fallbacks

The agent system is designed to provide a more natural and effective interaction experience by understanding user intent and taking appropriate actions automatically.

## Reasoning Engine

The core of the agent system is the `ReasoningEngine` class, which implements a step-by-step thinking process for handling user requests.

### Key Components

```typescript
export class ReasoningEngine {
  private llmModel: string = 'qwen2.5:3b';
  private thinkingProcesses: ThinkingProcess[] = [];
  private systemPrompt = `You are an AI assistant specialized in machine learning tasks.
  You analyze user requests carefully, understand intent, and take immediate action.`;

  // Core thinking method
  public async think(userMessage: string, conversationContext?: string): Promise<ThinkingProcess> {
    // Implementation of multi-stage reasoning process
  }

  // Execute individual thinking steps
  private async executeThinkingStep(stage: ThinkingStage, userMessage: string, 
    conversationContext?: string, specificPrompt?: string): Promise<ThinkingStepResult> {
    // Implementation of individual reasoning steps
  }
  
  // Formulate a response based on thinking process and API results
  public async formulateResponse(
    thinkingProcess: ThinkingProcess,
    apiResults: ApiCall[],
    conversationContext?: string
  ): Promise<ThinkingStepResult> {
    // Implementation of response formulation
  }
}
```

### Thinking Stages

The reasoning process is divided into distinct stages:

```typescript
export enum ThinkingStage {
  IntentUnderstanding = 'intent_understanding',
  TaskClassification = 'task_classification',
  ExecutionPlanning = 'execution_planning',
  ResponseFormulation = 'response_formulation',
  ErrorRecovery = 'error_recovery'
}
```

Each stage has a specific purpose:

1. **Intent Understanding**: Analyzes the user's message to identify their primary intent and what they want to accomplish
2. **Task Classification**: Categorizes the request into specific task types (conversation, dataset management, model training, etc.)
3. **Execution Planning**: Determines the necessary steps and API calls to fulfill the request
4. **Response Formulation**: Generates a coherent and helpful response based on the thinking process and API results
5. **Error Recovery**: Handles exceptions and provides graceful fallbacks when things go wrong

## Task Management System

The `TaskManager` class orchestrates the execution of different types of tasks identified by the reasoning engine.

### Task Types

```typescript
export enum TaskType {
  Conversation = 'conversation',
  DatasetManagement = 'dataset',
  ModelTraining = 'training',
  ModelDeployment = 'deployment',
  ModelInference = 'inference',
  SystemCheck = 'system_check',
  Unknown = 'unknown'
}
```

### Task Processing Flow

```typescript
export class TaskManager {
  private tasks: TaskState[] = [];
  private systemStatus: SystemStatus = {
    ollamaConnected: false,
    backendConnected: false,
  };
  private conversationHistory: Message[] = [];

  // Process user messages through the reasoning engine
  public async processUserMessage(userMessage: string): Promise<TaskState> {
    // Create a new task
    const task: TaskState = {
      id: taskId,
      type: TaskType.Unknown,
      status: 'pending',
      reasoning: [],
      apiCalls: [],
      startedAt: new Date(),
    };
    
    // Use the reasoning engine to understand the request
    const thinkingProcess = await reasoningEngine.think(userMessage, conversationContext);
    task.thinkingProcess = thinkingProcess;
    
    // Update task type based on reasoning engine result
    task.type = thinkingProcess.taskType;
    
    // Execute API calls based on the task type
    await this.executeApiCalls(task, userMessage);
    
    // Formulate a response using the reasoning engine
    const responseResult = await reasoningEngine.formulateResponse(
      thinkingProcess, 
      task.apiCalls,
      conversationContext
    );
    
    // Complete the task
    task.status = 'complete';
    task.completedAt = new Date();
    
    return task;
  }
}
```

## Agent Services

The application includes several specialized agent services that work together to provide a comprehensive experience:

### Ollama Service

The `ollama-service.ts` module provides core functions for interacting with Ollama models:

```typescript
// Core functions for Ollama interaction
export async function generateWithOllama(
  model: string,
  prompt: string,
  options: any = {},
  extendedTimeout: boolean = false,
  timeoutMs?: number
): Promise<any> {
  // Implementation of Ollama generation
}

export async function listOllamaModels(): Promise<any> {
  // Implementation of model listing
}

export async function checkOllamaHealth(): Promise<boolean> {
  // Implementation of health check
}
```

### Diagnostic Service

The `OllamaDiagnosticService` provides comprehensive health checks and troubleshooting:

```typescript
export class OllamaDiagnosticService {
  // Check Ollama connection with retry logic
  public async checkConnection(
    modelName: string = 'qwen2.5:3b',
    options?: OllamaDiagnosticOptions
  ): Promise<OllamaConnectionStatus> {
    // Implementation of connection check
  }

  // Get detailed diagnostic information
  public async getDetailedDiagnostics(modelName: string = 'qwen2.5:3b'): Promise<any> {
    // Implementation of detailed diagnostics
  }
}
```

### API Service

The `api-service.ts` module handles backend API interactions:

```typescript
// Backend API interactions
export async function checkBackendConnection(): Promise<boolean> {
  // Implementation of backend connection check
}

export async function listDatasets(): Promise<any> {
  // Implementation of dataset listing
}

export async function listFineTuningJobs(): Promise<any> {
  // Implementation of fine-tuning job listing
}

export async function listModelDeployments(): Promise<any> {
  // Implementation of model deployment listing
}
```

## Thinking Process

The thinking process is a structured approach to handling user requests:

### Process Structure

```typescript
export interface ThinkingProcess {
  userMessage: string;
  steps: ThinkingStepResult[];
  taskType: TaskType;
  startTime: Date;
  endTime?: Date;
  confidence?: number;
}

export interface ThinkingStepResult {
  stage: ThinkingStage;
  prompt: string;
  reasoning: string;
  confidence: number; // 0-1 confidence score
}
```

### Confidence Scoring

The system includes confidence scoring to evaluate the quality of reasoning:

```typescript
private calculateConfidence(reasoning: string, stage: ThinkingStage): number {
  // Base confidence starts at 0.5
  let confidence = 0.5;
  
  // Adjust based on reasoning length (longer reasoning often indicates more thought)
  const length = reasoning.length;
  if (length > 500) confidence += 0.1;
  if (length > 1000) confidence += 0.1;
  
  // Adjust based on stage-specific indicators
  switch (stage) {
    case ThinkingStage.IntentUnderstanding:
      // Check for clear intent identification
      if (reasoning.includes("user wants to") || reasoning.includes("user is asking for")) {
        confidence += 0.1;
      }
      break;
      
    case ThinkingStage.TaskClassification:
      // Check for clear task type identification
      if (reasoning.includes("this is a") && reasoning.includes("task")) {
        confidence += 0.1;
      }
      break;
      
    // Additional stage-specific adjustments...
  }
  
  // Cap confidence between 0 and 1
  return Math.min(Math.max(confidence, 0), 1);
}
```

## Integration with Ollama

The agent system integrates with Ollama through a dedicated service layer:

### Connection Modes

The system supports multiple connection modes:

1. **Local**: Connects to `http://localhost:11434`
2. **Auto**: Automatically detects the server from the current hostname
3. **Custom**: Connects to a user-specified URL

### Model Selection

The agent can work with any Ollama model, with intelligent fallbacks:

```typescript
// Select the best available model for a task
async function selectModelForTask(taskType: TaskType): Promise<string> {
  try {
    const models = await listOllamaModels();
    const availableModels = models.models.map(m => m.name);
    
    // Preferred models for different tasks
    const preferredModels = {
      [TaskType.Conversation]: ['llama3', 'llama2', 'mistral'],
      [TaskType.ModelInference]: ['codellama', 'llama3', 'llama2'],
      // Other task types...
    };
    
    // Find the first available preferred model for this task
    const taskPreferences = preferredModels[taskType] || ['llama2'];
    for (const modelName of taskPreferences) {
      const matchingModel = availableModels.find(m => m.includes(modelName));
      if (matchingModel) return matchingModel;
    }
    
    // Fallback to any available model
    return availableModels[0] || 'llama2';
  } catch (error) {
    console.error('Error selecting model:', error);
    return 'llama2'; // Default fallback
  }
}
```

## Use Cases

The agent system enables several advanced use cases:

### 1. Intelligent Code Generation

The system can generate code with a deeper understanding of requirements:

```typescript
// Example of code generation with reasoning
async function generateCodeWithReasoning(request: string): Promise<CodeGenerationResult> {
  // Create a task for code generation
  const task = await taskManager.processUserMessage(request);
  
  // Extract code from the response
  const code = extractCodeFromResponse(task.response);
  
  // Return both the code and the reasoning process
  return {
    code,
    reasoning: task.reasoning,
    confidence: task.thinkingProcess?.confidence || 0.5
  };
}
```

### 2. System Status Monitoring

The agent can intelligently diagnose and report on system status:

```typescript
// Example of system status check
async function checkSystemStatus(): Promise<SystemStatusReport> {
  const ollamaStatus = await checkOllamaHealth();
  const backendStatus = await checkBackendConnection();
  
  // Generate a report with the reasoning engine
  const statusMessage = await reasoningEngine.formulateStatusReport({
    ollamaConnected: ollamaStatus,
    backendConnected: backendStatus
  });
  
  return {
    ollamaConnected: ollamaStatus,
    backendConnected: backendStatus,
    statusMessage
  };
}
```

### 3. Multi-step Workflows

The agent can handle complex multi-step workflows:

```typescript
// Example of a multi-step workflow
async function handleDataAnalysisWorkflow(dataDescription: string): Promise<WorkflowResult> {
  // Step 1: Understand the data
  const dataAnalysisTask = await taskManager.processUserMessage(
    `Analyze this data: ${dataDescription}`
  );
  
  // Step 2: Generate visualization code
  const visualizationTask = await taskManager.processUserMessage(
    `Generate visualization code for this data: ${dataDescription}`
  );
  
  // Step 3: Provide insights
  const insightsTask = await taskManager.processUserMessage(
    `What insights can be drawn from this data: ${dataDescription}`
  );
  
  // Combine the results
  return {
    analysis: dataAnalysisTask.response,
    visualizationCode: extractCodeFromResponse(visualizationTask.response),
    insights: insightsTask.response
  };
}
```

## Development Guide

### Extending the Agent System

To extend the agent system with new capabilities:

1. **Add new task types** in the `TaskType` enum
2. **Implement task handlers** in the `executeApiCalls` method of `TaskManager`
3. **Create specialized prompts** for the reasoning engine
4. **Add new API services** as needed

### Creating Specialized Agents

You can create specialized agents for specific domains:

```typescript
// Example of creating a specialized agent
function createCodeGenerationAgent(language: string): Agent {
  const systemPrompt = `You are an expert ${language} developer.
You write clean, efficient, and well-documented code.
Always follow best practices for ${language} development.
Include comprehensive comments and error handling.`;

  return {
    name: `${language} Code Expert`,
    systemPrompt,
    model: selectBestModelForLanguage(language),
    temperature: 0.3
  };
}

// Using a specialized agent
async function generateSpecializedCode(request: string, language: string): Promise<string> {
  const agent = createCodeGenerationAgent(language);
  
  const response = await generateWithOllama(
    agent.model,
    request,
    {
      system: agent.systemPrompt,
      temperature: agent.temperature
    }
  );
  
  return response.response;
}
```

### Best Practices

When working with the agent system:

1. **Use the reasoning engine** for complex tasks that require multi-step thinking
2. **Leverage task classification** to route requests to the appropriate handlers
3. **Provide rich context** to improve reasoning quality
4. **Monitor confidence scores** to identify potential issues
5. **Implement graceful fallbacks** for when things go wrong
