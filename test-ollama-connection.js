// Simple script to test Ollama connection

const http = require('http');

console.log('Testing connection to Ollama at http://127.0.0.1:11434...');
console.log('This script will help diagnose connection issues with Ollama');
console.log('-------------------------------------------------------');

// Print environment information
console.log('Environment information:');
console.log(`Node.js version: ${process.version}`);
console.log(`Platform: ${process.platform}`);
console.log(`Architecture: ${process.arch}`);
console.log('-------------------------------------------------------');

const req = http.request({
  method: 'GET',
  hostname: '127.0.0.1',
  port: 11434,
  path: '/api/tags',
  timeout: 5000
}, (res) => {
  console.log(`Status: ${res.statusCode} ${res.statusMessage}`);

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    if (res.statusCode === 200) {
      console.log('Connection successful!');
      try {
        const parsed = JSON.parse(data);
        console.log(`Found ${parsed.models?.length || 0} models`);
        if (parsed.models?.length > 0) {
          console.log('Available models:');
          parsed.models.forEach(model => {
            console.log(`- ${model.name}`);
          });
        }
      } catch (e) {
        console.log('Response data:', data);
      }
    } else {
      console.log('Connection failed with status code:', res.statusCode);
      console.log('Response data:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('Connection error:', error.message);
  if (error.code === 'ECONNREFUSED') {
    console.log('\nTroubleshooting tips:');
    console.log('1. Make sure Ollama is running');
    console.log('2. Check if Ollama is listening on 127.0.0.1:11434');
    console.log('3. Check if there are any firewall rules blocking the connection');
    console.log('4. Try restarting Ollama');
  }
});

req.on('timeout', () => {
  console.error('Connection timed out');
  req.destroy();
});

req.end();
