export interface GenerateRequestOptions {
  model: string
  prompt: string
  stream: boolean
  options: {
    temperature: number
    top_p: number
    top_k: number
    num_predict: number
    presence_penalty: number
    frequency_penalty: number
    stop: string[]
  }
}

let ollamaApiInstance: any | null = null

// Import our cross-platform fetch utility
import { safeFetch } from './cross-fetch';

// Helper to get the current host
const getCurrentHost = () => {
  if (typeof window !== "undefined") {
    // Extract the hostname from the current URL
    const hostname = window.location.hostname
    return hostname
  }
  return "127.0.0.1" // Default fallback
}

/**
 * Create an instance of the Ollama API with the specified connection mode
 * @param mode Connection mode: 'local', 'auto', or 'custom'
 * @param customUrl Custom API URL if mode is 'custom'
 * @param apiKey Optional API key for authentication
 * @returns Ollama API instance
 */
export function createOllamaAPI(
  mode: 'local' | 'auto' | 'custom' = 'auto',
  customUrl?: string,
  apiKey?: string
): any {
  let baseUrl: string

  if (mode === 'local') {
    baseUrl = 'http://127.0.0.1:11434'
  } else if (mode === 'auto') {
    // Auto-detect using window.location in browser
    if (typeof window !== 'undefined') {
      const host = window.location.hostname
      // If we're on a deployed site (not localhost), default to local connection
      // This is because we need to connect to the user's local Ollama instance
      if (host === 'localhost' || host === '127.0.0.1') {
        baseUrl = `http://${host}:11434`
      } else {
        // We're on a deployed site, so connect to localhost
        baseUrl = 'http://127.0.0.1:11434'
      }
    } else {
      // Default to 127.0.0.1 if not in browser
      baseUrl = 'http://127.0.0.1:11434'
    }
  } else if (mode === 'custom' && customUrl) {
    baseUrl = customUrl
  } else {
    // Default fallback
    baseUrl = 'http://127.0.0.1:11434'
  }

  console.log(`Creating Ollama API with baseUrl: ${baseUrl}`)

  // Create the API object
  ollamaApiInstance = {
    baseUrl,
    apiKey,
    listModels: async () => {
      try {
        console.log(`Fetching models from: ${baseUrl}/api/tags`)
        const response = await safeFetch(`${baseUrl}/api/tags`, {
          headers: {
            "Content-Type": "application/json",
            ...(apiKey ? { Authorization: `Bearer ${apiKey}` } : {}),
          },
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`API error (${response.status}): ${errorText}`)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText || response.statusText}`)
        }

        const data = await response.json()
        console.log("Models fetched successfully:", data)
        return data
      } catch (error) {
        console.error("Error connecting to Ollama API:", error)
        throw error
      }
    },
    // Generate text with a model
    generate: async (options: any) => {
      try {
        console.log(`Sending generate request to: ${baseUrl}/api/generate`)
        console.log("Request options:", JSON.stringify(options, null, 2))

        const response = await safeFetch(`${baseUrl}/api/generate`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...(apiKey ? { Authorization: `Bearer ${apiKey}` } : {}),
          },
          body: JSON.stringify(options),
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`API error (${response.status}): ${errorText}`)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText || response.statusText}`)
        }

        const responseText = await response.text()
        console.log("Raw Ollama response:", responseText.substring(0, 200) + "...")

        try {
          // Handle potential streaming response (multiple JSON objects separated by newlines)
          const lines = responseText.trim().split('\n').filter(line => line.trim())

          if (lines.length === 1) {
            // Single JSON response
            const data = JSON.parse(lines[0])
            return data
          } else if (lines.length > 1) {
            // Multiple JSON objects (streaming format)
            let combinedResponse = ""
            let finalData = null

            for (const line of lines) {
              try {
                const lineData = JSON.parse(line)
                if (lineData.response) {
                  combinedResponse += lineData.response
                }
                if (lineData.done) {
                  finalData = lineData
                }
              } catch (lineParseError) {
                console.warn("Failed to parse line:", line, lineParseError)
              }
            }

            // Return combined response
            return {
              response: combinedResponse,
              ...finalData
            }
          } else {
            throw new Error("Empty response from Ollama")
          }
        } catch (parseError) {
          console.error("JSON parse error:", parseError)
          console.error("Response text:", responseText)
          throw new Error(`Invalid JSON response from Ollama: ${parseError.message}`)
        }
      } catch (error) {
        console.error("Error generating with Ollama:", error)
        throw error
      }
    },

    // Get model information
    showModel: async (modelName: string) => {
      try {
        console.log(`Fetching model info from: ${baseUrl}/api/show`)

        const response = await safeFetch(`${baseUrl}/api/show`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...(apiKey ? { Authorization: `Bearer ${apiKey}` } : {}),
          },
          body: JSON.stringify({ name: modelName }),
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`API error (${response.status}): ${errorText}`)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText || response.statusText}`)
        }

        const data = await response.json()
        return data
      } catch (error) {
        console.error(`Error getting model info for ${modelName}:`, error)
        throw error
      }
    },

    // Stream completion from a model
    streamCompletion: async (
      options: GenerateRequestOptions,
      onData: (data: any) => void,
      onError: (error: any) => void,
      onComplete: () => void,
    ) => {
      try {
        console.log(`Sending generate request to: ${baseUrl}/api/generate`)
        console.log("Request options:", JSON.stringify(options, null, 2))

        const response = await safeFetch(`${baseUrl}/api/generate`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            ...(apiKey ? { Authorization: `Bearer ${apiKey}` } : {}),
          },
          body: JSON.stringify(options),
        })

        if (!response.ok) {
          const errorText = await response.text()
          console.error(`API error (${response.status}): ${errorText}`)
          throw new Error(`HTTP error! status: ${response.status}, message: ${errorText || response.statusText}`)
        }

        const reader = response.body?.getReader()
        if (!reader) {
          throw new Error("Failed to get reader from response body")
        }

        const decoder = new TextDecoder()
        let accumulatedData = ""

        const processStream = async () => {
          try {
            const { done, value } = await reader.read()

            if (done) {
              console.log("Stream completed")
              onComplete()
              return
            }

            accumulatedData += decoder.decode(value)

            let newlineIndex
            while ((newlineIndex = accumulatedData.indexOf("\n")) >= 0) {
              const line = accumulatedData.substring(0, newlineIndex)
              accumulatedData = accumulatedData.substring(newlineIndex + 1)

              try {
                if (line.trim()) {
                  const parsedData = JSON.parse(line)
                  onData(parsedData)
                }
              } catch (parseError) {
                console.error("Error parsing JSON:", parseError, "Line:", line)
              }
            }

            processStream()
          } catch (error) {
            console.error("Stream processing error:", error)
            onError(error)
          }
        }

        processStream()
      } catch (error) {
        console.error("Stream request error:", error)
        onError(error)
      }
    },
  }

  return ollamaApiInstance
}

/**
 * Get the Ollama API instance, creating it if necessary
 * @param mode Connection mode: 'local', 'auto', or 'custom'
 * @param customUrl Custom API URL if mode is 'custom'
 * @param apiKey Optional API key for authentication
 * @returns Ollama API instance
 */
export function getOllamaAPI(
  mode: 'local' | 'auto' | 'custom' = 'auto',
  customUrl?: string,
  apiKey?: string
): any {
  if (!ollamaApiInstance) {
    return createOllamaAPI(mode, customUrl, apiKey);
  }
  return ollamaApiInstance;
}

