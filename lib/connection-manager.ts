import { createOllamaAPI, getOllamaAPI } from "@/lib/ollama-api"

interface ConnectionStatus {
  connected: boolean
  error: string | null
  lastChecked: Date | null
  models: string[]
}

class ConnectionManager {
  private static instance: ConnectionManager
  private status: ConnectionStatus = {
    connected: false,
    error: null,
    lastChecked: null,
    models: [],
  }
  private connectionListeners: ((status: ConnectionStatus) => void)[] = []
  private checkInterval: NodeJS.Timeout | null = null
  private checkIntervalTime = 30000 // 30 seconds

  private constructor() {
    // Private constructor to enforce singleton
  }

  public static getInstance(): ConnectionManager {
    if (!ConnectionManager.instance) {
      ConnectionManager.instance = new ConnectionManager()
    }
    return ConnectionManager.instance
  }

  public async initialize(
    connectionMode: "local" | "auto" | "custom" = "auto",
    apiUrl?: string,
    apiKey?: string,
  ): Promise<ConnectionStatus> {
    try {
      // Initialize the API
      createOllamaAPI(connectionMode, apiUrl, apiKey)

      // Check connection
      return await this.checkConnection()
    } catch (error) {
      console.error("Failed to initialize connection:", error)
      this.updateStatus({
        connected: false,
        error: error instanceof Error ? error.message : String(error),
        lastChecked: new Date(),
        models: [],
      })
      return this.status
    }
  }

  public async checkConnection(): Promise<ConnectionStatus> {
    try {
      const api = getOllamaAPI()
      const response = await api.listModels()

      const models = response.models ? response.models.map((model: any) => model.name) : []

      this.updateStatus({
        connected: true,
        error: null,
        lastChecked: new Date(),
        models,
      })

      return this.status
    } catch (error) {
      console.error("Connection check failed:", error)
      this.updateStatus({
        connected: false,
        error: error instanceof Error ? error.message : String(error),
        lastChecked: new Date(),
        models: [],
      })

      return this.status
    }
  }

  public startPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
    }

    this.checkInterval = setInterval(() => {
      this.checkConnection()
    }, this.checkIntervalTime)
  }

  public stopPeriodicChecks(): void {
    if (this.checkInterval) {
      clearInterval(this.checkInterval)
      this.checkInterval = null
    }
  }

  public getStatus(): ConnectionStatus {
    return { ...this.status }
  }

  public addConnectionListener(listener: (status: ConnectionStatus) => void): void {
    this.connectionListeners.push(listener)
  }

  public removeConnectionListener(listener: (status: ConnectionStatus) => void): void {
    this.connectionListeners = this.connectionListeners.filter((l) => l !== listener)
  }

  private updateStatus(newStatus: ConnectionStatus): void {
    this.status = newStatus
    // Notify all listeners
    this.connectionListeners.forEach((listener) => listener({ ...this.status }))
  }
}

export default ConnectionManager

