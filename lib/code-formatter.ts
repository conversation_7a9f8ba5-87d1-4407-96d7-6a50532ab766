// Simple code formatter utility

import prettier from 'prettier';

/**
 * Format code using Prettier
 * @param code The code to format
 * @param language The language of the code
 * @returns Formatted code
 */
export async function formatCode(code: string, language: string): Promise<string> {
  if (!code.trim()) return code;
  
  try {
    // Determine parser based on language
    let parser: string;
    
    switch (language.toLowerCase()) {
      case 'html':
        parser = 'html';
        break;
      case 'css':
        parser = 'css';
        break;
      case 'scss':
        parser = 'scss';
        break;
      case 'less':
        parser = 'less';
        break;
      case 'typescript':
      case 'ts':
        parser = 'typescript';
        break;
      case 'tsx':
        parser = 'typescript';
        break;
      case 'json':
        parser = 'json';
        break;
      case 'markdown':
      case 'md':
        parser = 'markdown';
        break;
      case 'javascript':
      case 'js':
      case 'jsx':
      default:
        parser = 'babel';
        break;
    }
    
    // Format the code using Prettier v3 API
    const formatted = await prettier.format(code, {
      parser,
      printWidth: 100,
      tabWidth: 2,
      useTabs: false,
      semi: true,
      singleQuote: true,
      quoteProps: 'as-needed',
      jsxSingleQuote: false,
      trailingComma: 'es5',
      bracketSpacing: true,
      bracketSameLine: false,
      arrowParens: 'avoid',
      endOfLine: 'lf',
    });
    
    return formatted;
  } catch (error) {
    console.error('Error formatting code:', error);
    // If formatting fails, return the original code
    return code;
  }
}

