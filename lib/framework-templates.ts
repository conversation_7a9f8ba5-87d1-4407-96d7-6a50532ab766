export interface FrameworkTemplate {
  name: string
  description: string
  languages: string[]
  fileExtensions: Record<string, string>
  defaultFiles: {
    name: string
    content: string
    language: string
  }[]
  previewConfig?: {
    useIframe: boolean
    buildCommand?: string
    serveCommand?: string
  }
  systemPrompt: string
  processCodeForPreview?: (
    codeBlocks: { html: string; css: string; js: string },
    rawResponse: string,
  ) => { html: string; css: string; js: string }
}

export const frameworkTemplates: Record<string, FrameworkTemplate> = {
  "html-css-js": {
    name: "HTML/CSS/JS",
    description: "Basic web development with HTML, CSS, and JavaScript",
    languages: ["html", "css", "javascript"],
    fileExtensions: {
      html: "html",
      css: "css",
      javascript: "js",
    },
    defaultFiles: [
      {
        name: "index.html",
        content:
          '<!DOCTYPE html>\n<html>\n<head>\n  <meta charset="utf-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1">\n  <title>My Project</title>\n  <link rel="stylesheet" href="styles.css">\n</head>\n<body>\n  <h1>Hello World</h1>\n  <script src="script.js"></script>\n</body>\n</html>',
        language: "html",
      },
      {
        name: "styles.css",
        content: "body {\n  font-family: system-ui, sans-serif;\n  margin: 0;\n  padding: 20px;\n}",
        language: "css",
      },
      {
        name: "script.js",
        content: "console.log('Hello world!');",
        language: "javascript",
      },
    ],
    previewConfig: {
      useIframe: true,
    },
    systemPrompt: `You are a frontend expert. Generate clean, modern HTML, CSS, and JavaScript code based on the user's request.
    Format your response with code blocks using markdown syntax:
    \`\`\`html
    <!-- HTML content here -->
    \`\`\`
    \`\`\`css
    /* CSS content here */
    \`\`\`
    \`\`\`javascript
    // JavaScript code here
    \`\`\`
    Explain your implementation briefly.`,
  },
  react: {
    name: "React",
    description: "Modern UI development with React",
    languages: ["jsx", "javascript", "css"],
    fileExtensions: {
      component: "jsx",
      javascript: "js",
      css: "css",
    },
    defaultFiles: [
      {
        name: "App.jsx",
        content:
          "import React from 'react';\nimport './App.css';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello React</h1>\n    </div>\n  );\n}\n\nexport default App;",
        language: "jsx",
      },
      {
        name: "App.css",
        content: ".App {\n  text-align: center;\n  padding: 20px;\n}",
        language: "css",
      },
      {
        name: "index.jsx",
        content:
          "import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport App from './App';\n\nReactDOM.createRoot(document.getElementById('root')).render(\n  <React.StrictMode>\n    <App />\n  </React.StrictMode>\n);",
        language: "jsx",
      },
    ],
    previewConfig: {
      useIframe: true,
    },
    systemPrompt: `You are a React expert. Generate clean, modern React code based on the user's request. 
    Use functional components and hooks. Include necessary imports.
    Format your response with code blocks using markdown syntax:
    \`\`\`html
    <!-- HTML content here -->
    \`\`\`
    \`\`\`css
    /* CSS content here */
    \`\`\`
    \`\`\`javascript
    // JavaScript/React code here
    \`\`\`
    Explain your implementation briefly.`,
    processCodeForPreview: (codeBlocks, rawResponse) => {
      // For React, we need to extract the component and create a simple HTML structure
      const jsCode = codeBlocks.js || ""

      // Create a basic HTML structure with a root div for React to mount
      const html = codeBlocks.html || '<div id="root"></div>'

      // Process the JS code to ensure it renders to the root element
      let processedJs = jsCode

      // If the code doesn't include ReactDOM.render or createRoot, add it
      if (!jsCode.includes("ReactDOM.render") && !jsCode.includes("createRoot")) {
        // Extract the component name - this is a simple heuristic
        const componentMatch = jsCode.match(/function\s+([A-Z][a-zA-Z0-9]*)/)
        const componentName = componentMatch ? componentMatch[1] : "App"

        // Add rendering code
        processedJs = `${jsCode}

// Render the component to the DOM
const rootElement = document.getElementById('root');
ReactDOM.createRoot(rootElement).render(<${componentName} />);`
      }

      return {
        html,
        css: codeBlocks.css || "",
        js: processedJs,
      }
    },
  },
  nextjs: {
    name: "Next.js",
    description: "React framework with server-side rendering and routing",
    languages: ["tsx", "typescript", "css"],
    fileExtensions: {
      component: "tsx",
      page: "tsx",
      typescript: "ts",
      css: "css",
    },
    defaultFiles: [
      {
        name: "page.tsx",
        content:
          'export default function Home() {\n  return (\n    <main className="flex min-h-screen flex-col items-center justify-center p-24">\n      <h1 className="text-4xl font-bold">Hello Next.js</h1>\n    </main>\n  );\n}',
        language: "tsx",
      },
      {
        name: "layout.tsx",
        content:
          'export default function RootLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return (\n    <html lang="en">\n      <body>{children}</body>\n    </html>\n  );\n}',
        language: "tsx",
      },
    ],
    previewConfig: {
      useIframe: true,
    },
    systemPrompt: `You are a Next.js expert. Generate clean, modern Next.js code based on the user's request.
    Use the App Router, Server Components where appropriate, and Client Components when needed.
    Include necessary imports and file structure.
    Format your response with code blocks using markdown syntax:
    \`\`\`html
    <!-- HTML content here (if needed) -->
    \`\`\`
    \`\`\`css
    /* CSS content here */
    \`\`\`
    \`\`\`javascript
    // Next.js code here
    \`\`\`
    Explain your implementation briefly.`,
    processCodeForPreview: (codeBlocks, rawResponse) => {
      // For Next.js, we'll convert it to React for preview
      const jsCode = codeBlocks.js || ""

      // Create a basic HTML structure with a root div
      const html = codeBlocks.html || '<div id="root"></div>'

      // Process the JS code to make it work in the preview
      // Remove Next.js specific imports and replace with React
      let processedJs = jsCode
        .replace(/import.*from\s+['"]next\/.*['"]/g, "")
        .replace(/['"]use client['"];?\n?/g, "")
        .replace(/export default/g, "const App =")

      // Add rendering code
      processedJs = `${processedJs}

// Render the component to the DOM
const rootElement = document.getElementById('root');
ReactDOM.createRoot(rootElement).render(<App />);`

      return {
        html,
        css: codeBlocks.css || "",
        js: processedJs,
      }
    },
  },
  flutter: {
    name: "Flutter",
    description: "UI toolkit for building natively compiled applications",
    languages: ["dart"],
    fileExtensions: {
      dart: "dart",
    },
    defaultFiles: [
      {
        name: "main.dart",
        content:
          "import 'package:flutter/material.dart';\n\nvoid main() {\n  runApp(MyApp());\n}\n\nclass MyApp extends StatelessWidget {\n  @override\n  Widget build(BuildContext context) {\n    return MaterialApp(\n      title: 'Flutter Demo',\n      theme: ThemeData(\n        primarySwatch: Colors.blue,\n      ),\n      home: MyHomePage(title: 'Flutter Demo Home Page'),\n    );\n  }\n}\n\nclass MyHomePage extends StatelessWidget {\n  final String title;\n  \n  MyHomePage({required this.title});\n\n  @override\n  Widget build(BuildContext context) {\n    return Scaffold(\n      appBar: AppBar(\n        title: Text(title),\n      ),\n      body: Center(\n        child: Text('Hello Flutter!'),\n      ),\n    );\n  }\n}",
        language: "dart",
      },
    ],
    previewConfig: {
      useIframe: false, // Flutter needs special handling
    },
    systemPrompt: `You are a Flutter expert. Generate clean, modern Flutter code based on the user's request.
    Use the latest Flutter best practices. Include necessary imports and widget structure.
    Format your response with code blocks using markdown syntax:
    \`\`\`dart
    // Flutter/Dart code here
    \`\`\`
    Explain your implementation briefly.`,
    processCodeForPreview: (codeBlocks, rawResponse) => {
      // For Flutter, we can't actually preview it in the browser
      // So we'll just show a message in the preview
      return {
        html: `
          <div style="padding: 20px; text-align: center;">
            <h2>Flutter Code Generated</h2>
            <p>Flutter code cannot be previewed in the browser. Please copy the code and use it in a Flutter project.</p>
          </div>
        `,
        css: "",
        js: "",
      }
    },
  },
  bootstrap: {
    name: "Bootstrap",
    description: "Popular CSS framework for responsive websites",
    languages: ["html", "css", "javascript"],
    fileExtensions: {
      html: "html",
      css: "css",
      javascript: "js",
    },
    defaultFiles: [
      {
        name: "index.html",
        content:
          '<!DOCTYPE html>\n<html>\n<head>\n  <meta charset="utf-8">\n  <meta name="viewport" content="width=device-width, initial-scale=1">\n  <title>Bootstrap Project</title>\n  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">\n  <link rel="stylesheet" href="styles.css">\n</head>\n<body>\n  <div class="container mt-5">\n    <h1>Hello Bootstrap</h1>\n    <button class="btn btn-primary">Click Me</button>\n  </div>\n  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>\n  <script src="script.js"></script>\n</body>\n</html>',
        language: "html",
      },
      {
        name: "styles.css",
        content: "/* Custom styles */\nbody {\n  padding-top: 20px;\n}",
        language: "css",
      },
      {
        name: "script.js",
        content:
          "// Custom JavaScript\ndocument.addEventListener('DOMContentLoaded', function() {\n  console.log('Bootstrap project loaded!');\n});",
        language: "javascript",
      },
    ],
    previewConfig: {
      useIframe: true,
    },
    systemPrompt: `You are a Bootstrap expert. Generate clean, modern Bootstrap code based on the user's request.
    Use Bootstrap 5. Include necessary HTML structure and JavaScript if needed.
    Format your response with code blocks using markdown syntax:
    \`\`\`html
    <!-- HTML with Bootstrap classes here -->
    \`\`\`
    \`\`\`css
    /* Additional CSS if needed */
    \`\`\`
    \`\`\`javascript
    // JavaScript code if needed
    \`\`\`
    Explain your implementation briefly.`,
    // No special processing needed for Bootstrap
  },
  vue: {
    name: "Vue.js",
    description: "Progressive JavaScript framework",
    languages: ["vue", "javascript", "css"],
    fileExtensions: {
      vue: "vue",
      javascript: "js",
      css: "css",
    },
    defaultFiles: [
      {
        name: "App.vue",
        content: `<template>
  <div class="app">
    <h1>Hello Vue!</h1>
  </div>
</template>

<script>
export default {
  name: 'App'
}
</script>

<style scoped>
.app {
  text-align: center;
  padding: 20px;
}
</style>`,
        language: "vue",
      },
      {
        name: "main.js",
        content: `import { createApp } from 'vue'
import App from './App.vue'

createApp(App).mount('#app')`,
        language: "javascript",
      },
    ],
    previewConfig: {
      useIframe: true,
    },
    systemPrompt: `You are a Vue.js expert. Generate clean, modern Vue.js code based on the user's request.
    Use Vue 3 with the Composition API. Include necessary imports.
    Format your response with code blocks using markdown syntax:
    \`\`\`html
    <!-- HTML/Vue template here -->
    \`\`\`
    \`\`\`css
    /* CSS content here */
    \`\`\`
    \`\`\`javascript
    // JavaScript/Vue code here
    \`\`\`
    Explain your implementation briefly.`,
    processCodeForPreview: (codeBlocks, rawResponse) => {
      // For Vue, we need to ensure the app is properly mounted
      const html = codeBlocks.html || '<div id="app"></div>'
      const jsCode = codeBlocks.js || ""

      // Process the JS code to ensure it creates and mounts a Vue app
      let processedJs = jsCode

      if (!jsCode.includes("createApp") && !jsCode.includes("new Vue")) {
        // Add Vue app creation and mounting
        processedJs = `${jsCode}

// Create and mount the Vue app
const app = Vue.createApp({
  template: \`${html.replace(/`/g, "\\`")}\`
});
app.mount('#app');`
      }

      return {
        html: '<div id="app"></div>',
        css: codeBlocks.css || "",
        js: processedJs,
      }
    },
  },
}

export function getFrameworkByName(name: string): FrameworkTemplate | undefined {
  return frameworkTemplates[name]
}

export function getLanguageForFile(filename: string): string {
  const extension = filename.split(".").pop()?.toLowerCase() || ""

  const extensionToLanguage: Record<string, string> = {
    js: "javascript",
    jsx: "jsx",
    ts: "typescript",
    tsx: "tsx",
    html: "html",
    css: "css",
    json: "json",
    md: "markdown",
    dart: "dart",
    vue: "vue",
  }

  return extensionToLanguage[extension] || "plaintext"
}

export function getFrameworkTemplate(framework: string): FrameworkTemplate {
  return frameworkTemplates[framework] || frameworkTemplates.react
}

