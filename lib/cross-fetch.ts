/**
 * Cross-platform fetch implementation that works in both browser and Node.js environments
 */

// Import Node.js built-in modules conditionally
let http: any;
let https: any;
let URL: any;

// Check if we're in a Node.js environment
const isNode = typeof window === 'undefined' && typeof process !== 'undefined' && process.versions && process.versions.node;

// Only import Node.js modules if we're in a Node.js environment
if (isNode) {
  // Dynamic imports to avoid issues in browser environments
  try {
    http = require('http');
    https = require('https');
    const url = require('url');
    URL = url.URL;
  } catch (error) {
    console.error('Failed to import Node.js modules:', error);
  }
}

/**
 * Cross-platform fetch implementation
 * 
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise with fetch response
 */
export async function crossFetch(url: string, options?: RequestInit): Promise<Response> {
  try {
    // In browser environments, use the built-in fetch
    if (typeof window !== 'undefined' && window.fetch) {
      return await window.fetch(url, options);
    }
    
    // In Node.js environments with global fetch (Node.js 18+)
    if (typeof global !== 'undefined' && 'fetch' in global) {
      return await (global as any).fetch(url, options);
    }
    
    // Fallback to a basic implementation using http/https modules
    if (isNode && http && https && URL) {
      return new Promise((resolve, reject) => {
        try {
          const parsedUrl = new URL(url);
          const protocol = parsedUrl.protocol === 'https:' ? https : http;
          
          const requestOptions = {
            method: options?.method || 'GET',
            headers: options?.headers || {},
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
            path: parsedUrl.pathname + parsedUrl.search,
          };
          
          const req = protocol.request(requestOptions, (res: any) => {
            const chunks: Buffer[] = [];
            
            res.on('data', (chunk: Buffer) => {
              chunks.push(Buffer.from(chunk));
            });
            
            res.on('end', () => {
              const body = Buffer.concat(chunks);
              
              // Create a Response-like object
              const response = {
                ok: res.statusCode >= 200 && res.statusCode < 300,
                status: res.statusCode,
                statusText: res.statusMessage,
                headers: res.headers,
                text: async () => body.toString(),
                json: async () => JSON.parse(body.toString()),
                body: {
                  getReader: () => {
                    let consumed = false;
                    return {
                      read: async () => {
                        if (consumed) {
                          return { done: true, value: undefined };
                        }
                        consumed = true;
                        return { done: false, value: body };
                      }
                    };
                  }
                }
              };
              
              resolve(response as any);
            });
          });
          
          req.on('error', (error: Error) => {
            reject(error);
          });
          
          if (options?.body) {
            req.write(options.body);
          }
          
          req.end();
        } catch (error) {
          reject(error);
        }
      });
    }
    
    // If we get here, no fetch implementation is available
    throw new Error('No fetch implementation available in this environment');
  } catch (error) {
    console.error("Fetch error:", error);
    throw new Error(`Network request failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

/**
 * Safe fetch wrapper that handles errors gracefully
 * 
 * @param url URL to fetch
 * @param options Fetch options
 * @returns Promise with fetch response
 */
export async function safeFetch(url: string, options?: RequestInit): Promise<Response> {
  try {
    return await crossFetch(url, options);
  } catch (error) {
    console.error("Fetch error:", error);
    throw new Error(`Network request failed: ${error instanceof Error ? error.message : String(error)}`);
  }
}

export default safeFetch;
