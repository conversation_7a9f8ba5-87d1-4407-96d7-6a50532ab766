import { diffLines } from "diff"

export interface DiffResult {
  type: "add" | "remove" | "unchanged"
  value: string
}

export function diffCode(oldCode: string, newCode: string): DiffResult[] {
  const diff = diffLines(oldCode, newCode)

  return diff.map((part) => {
    if (part.added) {
      return { type: "add", value: part.value }
    }
    if (part.removed) {
      return { type: "remove", value: part.value }
    }
    return { type: "unchanged", value: part.value }
  })
}

