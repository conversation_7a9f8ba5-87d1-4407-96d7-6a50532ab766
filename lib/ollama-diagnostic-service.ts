/**
 * Mash Bot Diagnostic Service
 * Provides utilities for diagnosing and troubleshooting Mash Bot connection issues
 */

import { getOllamaAPI } from "./ollama-api";

export interface MashBotConnectionStatus {
  connected: boolean;
  error?: string;
  details?: {
    apiUrl: string;
    responseTime?: number;
    models?: string[];
    serverInfo?: any;
  };
}

export interface MashBotDiagnosticOptions {
  timeout?: number;
  retries?: number;
  retryDelay?: number;
}

/**
 * Service for diagnosing Mash Bot connection issues
 */
export class MashBotDiagnosticService {
  private apiUrl: string;

  constructor() {
    const ollamaApi = getOllamaAPI();
    this.apiUrl = ollamaApi.getBaseUrl();
  }

  /**
   * Check if Mash Bot is connected and responding
   *
   * @param modelName Optional model name to check
   * @param options Diagnostic options
   * @returns Connection status
   */
  public async checkConnection(
    modelName: string = 'qwen2.5:3b',
    options?: MashBotDiagnosticOptions
  ): Promise<MashBotConnectionStatus> {
    const timeout = options?.timeout || 5000;
    const retries = options?.retries || 2;
    const retryDelay = options?.retryDelay || 1000;

    let lastError: Error | null = null;

    // Try multiple times with delay between attempts
    for (let attempt = 0; attempt < retries; attempt++) {
      try {
        const startTime = Date.now();

        // Create abort controller for timeout
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        // Try to list models as a basic health check
        const ollamaApi = getOllamaAPI();
        const modelsResponse = await ollamaApi.listModels({
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        const responseTime = Date.now() - startTime;

        // If we get here, the connection is working
        return {
          connected: true,
          details: {
            apiUrl: this.apiUrl,
            responseTime,
            models: modelsResponse.models?.map((m: any) => m.name) || [],
            serverInfo: {
              version: modelsResponse.version || 'unknown'
            }
          }
        };
      } catch (error) {
        lastError = error as Error;

        // If this is an abort error, it's a timeout
        if (error instanceof DOMException && error.name === 'AbortError') {
          console.log(`Connection attempt ${attempt + 1} timed out after ${timeout}ms`);
        } else {
          console.log(`Connection attempt ${attempt + 1} failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }

        // Wait before retrying
        if (attempt < retries - 1) {
          await new Promise(resolve => setTimeout(resolve, retryDelay));
        }
      }
    }

    // All attempts failed
    return {
      connected: false,
      error: lastError?.message || 'Failed to connect to Ollama API',
      details: {
        apiUrl: this.apiUrl
      }
    };
  }

  /**
   * Get detailed diagnostic information about Mash Bot
   *
   * @param modelName Model to test with
   * @returns Detailed diagnostic information
   */
  public async getDetailedDiagnostics(modelName: string = 'qwen2.5:3b'): Promise<any> {
    try {
      const ollamaApi = getOllamaAPI();
      const diagnostics: any = {
        apiUrl: this.apiUrl,
        timestamp: new Date().toISOString(),
        connectionStatus: 'unknown',
        models: [],
        modelDetails: null,
        simpleGenerationTest: null,
        errors: []
      };

      // Check basic connection
      try {
        const connectionStatus = await this.checkConnection(modelName);
        diagnostics.connectionStatus = connectionStatus.connected ? 'connected' : 'disconnected';

        if (connectionStatus.error) {
          diagnostics.errors.push({
            component: 'connection',
            message: connectionStatus.error
          });
        }

        if (connectionStatus.details) {
          diagnostics.connectionDetails = connectionStatus.details;
        }
      } catch (error) {
        diagnostics.connectionStatus = 'error';
        diagnostics.errors.push({
          component: 'connection_check',
          message: error instanceof Error ? error.message : 'Unknown error checking connection'
        });
      }

      // If connected, get more detailed information
      if (diagnostics.connectionStatus === 'connected') {
        // List available models
        try {
          const modelsResponse = await ollamaApi.listModels();
          diagnostics.models = modelsResponse.models || [];
        } catch (error) {
          diagnostics.errors.push({
            component: 'list_models',
            message: error instanceof Error ? error.message : 'Unknown error listing models'
          });
        }

        // Check if the specified model exists
        const modelExists = diagnostics.models.some((m: any) => m.name === modelName);

        if (modelExists) {
          // Get model details
          try {
            const modelResponse = await ollamaApi.showModel(modelName);
            diagnostics.modelDetails = modelResponse;
          } catch (error) {
            diagnostics.errors.push({
              component: 'model_details',
              message: error instanceof Error ? error.message : `Unknown error getting details for model ${modelName}`
            });
          }

          // Try a simple generation
          try {
            const startTime = Date.now();
            const generateResponse = await ollamaApi.generate({
              model: modelName,
              prompt: 'Hello, how are you?',
              options: {
                num_predict: 20
              }
            });

            const responseTime = Date.now() - startTime;

            diagnostics.simpleGenerationTest = {
              success: true,
              responseTime,
              response: generateResponse.response
            };
          } catch (error) {
            diagnostics.simpleGenerationTest = {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error during generation test'
            };

            diagnostics.errors.push({
              component: 'generation_test',
              message: error instanceof Error ? error.message : 'Unknown error during generation test'
            });
          }
        } else {
          diagnostics.errors.push({
            component: 'model_check',
            message: `Model ${modelName} not found in available models`
          });
        }
      }

      return diagnostics;
    } catch (error) {
      return {
        apiUrl: this.apiUrl,
        timestamp: new Date().toISOString(),
        connectionStatus: 'error',
        error: error instanceof Error ? error.message : 'Unknown error during diagnostics',
        errors: [{
          component: 'diagnostics',
          message: error instanceof Error ? error.message : 'Unknown error during diagnostics'
        }]
      };
    }
  }

  /**
   * Get troubleshooting suggestions based on diagnostic results
   *
   * @param diagnostics Diagnostic results
   * @returns Array of troubleshooting suggestions
   */
  public getTroubleshootingSuggestions(diagnostics: any): string[] {
    const suggestions: string[] = [];

    if (diagnostics.connectionStatus === 'disconnected' || diagnostics.connectionStatus === 'error') {
      suggestions.push('Make sure Mash Bot service is running on your system.');
      suggestions.push(`Check that the API URL (${this.apiUrl}) is correct.`);
      suggestions.push('Verify that there are no firewall or network issues blocking the connection.');
      suggestions.push('Try restarting the Mash Bot service.');
    }

    if (diagnostics.errors && diagnostics.errors.length > 0) {
      // Check for specific error patterns
      for (const error of diagnostics.errors) {
        if (error.message?.includes('ECONNREFUSED')) {
          suggestions.push('Connection refused: Mash Bot server is not running or not accessible at the configured URL.');
        } else if (error.message?.includes('timeout')) {
          suggestions.push('Connection timed out: Mash Bot server might be overloaded or unresponsive.');
        } else if (error.message?.includes('not found') && error.component === 'model_check') {
          suggestions.push(`The model "${diagnostics.modelName || 'requested'}" is not available. Try adding it in the Models management section.`);
        }
      }
    }

    // If no specific suggestions were added, add general ones
    if (suggestions.length === 0 && (diagnostics.connectionStatus !== 'connected' || diagnostics.errors?.length > 0)) {
      suggestions.push('Check the Mash Bot logs for more detailed error information.');
      suggestions.push('Verify that your system meets the minimum requirements for running Mash Bot.');
      suggestions.push('Try using a different model to see if the issue is model-specific.');
    }

    return suggestions;
  }
}

// Create a singleton instance
export const mashBotDiagnosticService = new MashBotDiagnosticService();
