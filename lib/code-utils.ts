export function extractCodeBlocks(text: string) {
  const htmlRegex = /```html\n([\s\S]*?)```/g
  const cssRegex = /```css\n([\s\S]*?)```/g
  const jsRegex = /```(javascript|js)\n([\s\S]*?)```/g

  const htmlMatch = htmlRegex.exec(text)
  const cssMatch = cssRegex.exec(text)
  const jsMatch = jsRegex.exec(text)

  return {
    html: htmlMatch ? htmlMatch[1] : "",
    css: cssMatch ? cssMatch[1] : "",
    js: jsMatch ? jsMatch[2] || "" : "",
  }
}

