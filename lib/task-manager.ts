import { checkOllamaHealth, generateWithOllama, listOllamaModels } from "../ollama-service";
import { checkBackendConnection, listDatasets, listFineTuningJobs, listModelDeployments } from "../api-service";
import { reasoningEngine, ThinkingProcess } from "./reasoning-engine";
import { getHuggingFaceAPI } from "./huggingface-api";

// Function for listing models from both Ollama and Hugging Face
async function listModels() {
  try {
    // Try to get Ollama models
    const ollamaModels = await listOllamaModels().catch(() => ({ models: [] }));

    // Try to get Hugging Face models (limited to text generation models)
    const huggingFaceAPI = getHuggingFaceAPI();
    const hfModels = await huggingFaceAPI.listModels('text-generation').catch(() => []);

    // Format Hugging Face models to match our format
    const formattedHFModels = (hfModels || []).slice(0, 10).map((model: any) => ({
      id: model.id,
      name: model.id.split('/').pop() || model.id,
      type: 'huggingface',
      size: model.downloads ? `${Math.round(model.downloads / 1000)}K downloads` : 'Unknown',
      description: model.description || 'Hugging Face model'
    }));

    // Combine models from both sources
    return {
      models: [
        // Ollama models
        ...(ollamaModels.models || []),
        // Hugging Face models
        ...formattedHFModels,
        // Fallback models if both APIs fail
        { id: 'qwen2.5:3b', name: 'Llama 2 (7B)', type: 'ollama', size: '7B', description: 'General purpose language model' },
        { id: 'codellama:7b', name: 'Code Llama (7B)', type: 'ollama', size: '7B', description: 'Specialized for code generation' },
        { id: 'mistral:7b', name: 'Mistral (7B)', type: 'ollama', size: '7B', description: 'High-performance language model' },
        { id: 'gpt2', name: 'GPT-2', type: 'huggingface', size: '124M', description: 'OpenAI GPT-2 model' },
        { id: 'facebook/opt-350m', name: 'OPT-350M', type: 'huggingface', size: '350M', description: 'Meta OPT model' }
      ]
    };
  } catch (error) {
    console.error('Error listing models:', error);
    // Return fallback models if everything fails
    return {
      models: [
        { id: 'qwen2.5:3b', name: 'Llama 2 (7B)', type: 'ollama', size: '7B', description: 'General purpose language model' },
        { id: 'codellama:7b', name: 'Code Llama (7B)', type: 'ollama', size: '7B', description: 'Specialized for code generation' },
        { id: 'gpt2', name: 'GPT-2', type: 'huggingface', size: '124M', description: 'OpenAI GPT-2 model' }
      ]
    };
  }
}

// Function for getting available deployments
async function getAvailableDeployments() {
  return {
    deployments: [
      { id: 'dep-1', name: 'Frontend Builder', model: 'codellama:7b', type: 'ollama', status: 'active', endpoint: '/api/models/frontend-builder' },
      { id: 'dep-2', name: 'React Component Generator', model: 'facebook/opt-350m', type: 'huggingface', status: 'active', endpoint: '/api/models/component-generator' }
    ]
  };
}

/**
 * Task types that the agent can execute
 */
export enum TaskType {
  Conversation = 'conversation',
  DatasetManagement = 'dataset',
  ModelTraining = 'training',
  ModelDeployment = 'deployment',
  ModelInference = 'inference',
  SystemCheck = 'system_check',
  Unknown = 'unknown'
}

/**
 * Status of system connections
 */
export interface SystemStatus {
  ollamaConnected: boolean;
  backendConnected: boolean;
}

/**
 * A step in the reasoning process
 */
export interface ReasoningStep {
  step: string;
  output: string;
}

/**
 * Task execution state
 */
export interface TaskState {
  id: string;
  type: TaskType;
  status: 'pending' | 'in_progress' | 'complete' | 'error';
  message?: string;
  reasoning: ReasoningStep[];
  apiCalls: { name: string, result: any }[];
  startedAt: Date;
  completedAt?: Date;
  thinkingProcess?: ThinkingProcess; // New field for detailed thinking
}

/**
 * Message model for communication
 */
export interface Message {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

/**
 * Manages task execution and thinking process for the AI agent
 */
export class TaskManager {
  private tasks: TaskState[] = [];
  private systemStatus: SystemStatus = {
    ollamaConnected: false,
    backendConnected: false,
  };
  private conversationHistory: Message[] = [];
  private llmModel: string = 'qwen2.5:3b'; // Default LLM model

  /**
   * Initialize the task manager
   */
  constructor() {}

  /**
   * Get the current system status
   */
  public async checkSystemStatus(): Promise<SystemStatus> {
    try {
      // Check Ollama connection
      this.systemStatus.ollamaConnected = await checkOllamaHealth();

      // Check backend connection
      this.systemStatus.backendConnected = await checkBackendConnection();

      return this.systemStatus;
    } catch (error) {
      console.error('Error checking system status:', error);
      return this.systemStatus;
    }
  }

  /**
   * Add conversation history for context
   */
  public addToConversationHistory(message: Message): void {
    this.conversationHistory.push(message);

    // Keep last 10 messages for context
    if (this.conversationHistory.length > 10) {
      this.conversationHistory = this.conversationHistory.slice(-10);
    }
  }

  /**
   * Get conversation history for context
   */
  public getConversationContext(): string {
    return this.conversationHistory
      .map(msg => `${msg.role === 'user' ? 'Human' : msg.role === 'system' ? 'System' : 'Assistant'}: ${msg.content}`)
      .join('\n');
  }

  /**
   * Process a user message through multiple thinking steps
   * Using the enhanced reasoning engine for improved results
   */
  public async processUserMessage(userMessage: string, selectedModel?: string): Promise<TaskState> {
    // Create a new task
    const taskId = `task-${Date.now()}`;
    const task: TaskState = {
      id: taskId,
      type: TaskType.Unknown,
      status: 'pending',
      reasoning: [],
      apiCalls: [],
      startedAt: new Date(),
    };

    this.tasks.push(task);

    // Start processing
    task.status = 'in_progress';

    try {
      // Get conversation context for reasoning
      const conversationContext = this.getConversationContext();

      // Check for direct API request patterns first (for more responsive UX)
      const lowerCaseMessage = userMessage.toLowerCase();

      // Fast path for common direct requests (skip reasoning engine)
      if (this.isDatasetListRequest(lowerCaseMessage)) {
        console.log("Direct dataset list request detected, skipping reasoning");
        task.type = TaskType.DatasetManagement;
        await this.executeApiCalls(task, userMessage);

        // Add a simple reasoning step for tracking
        task.reasoning.push({
          step: 'direct_request',
          output: 'User directly requested to list datasets. Fetched dataset information from the API.'
        });

        // Complete with minimal reasoning
        const respStep = await this.simpleResponseFormulation(task, userMessage, conversationContext);
        task.reasoning.push(respStep);
        task.status = 'complete';
        task.completedAt = new Date();
        return task;
      }

      // Set the selected model if provided
      if (selectedModel) {
        reasoningEngine.setModel(selectedModel);
      }

      // Use the enhanced reasoning engine for detailed thinking
      const thinkingProcess = await reasoningEngine.think(userMessage, conversationContext);
      task.thinkingProcess = thinkingProcess;

      // Update task type based on reasoning engine result
      task.type = thinkingProcess.taskType;

      // Add reasoning steps to task for tracking
      thinkingProcess.steps.forEach((step) => {
        task.reasoning.push({
          step: step.stage,
          output: step.reasoning
        });
      });

      // Execute API calls based on the task type
      await this.executeApiCalls(task, userMessage);

      // Formulate a response using the reasoning engine
      const responseResult = await reasoningEngine.formulateResponse(
        thinkingProcess,
        task.apiCalls,
        conversationContext
      );

      // Add the response formulation to the reasoning steps
      task.reasoning.push({
        step: 'response_formulation',
        output: responseResult.reasoning
      });

      // Complete the task
      task.status = 'complete';
      task.completedAt = new Date();

      return task;
    } catch (error) {
      console.error('Error processing message:', error);
      task.status = 'error';
      task.message = error instanceof Error ? error.message : 'Unknown error occurred';
      return task;
    }
  }

  /**
   * Check if the user message is directly asking for dataset listing
   * This is a simple pattern matching approach for direct access
   */
  private isDatasetListRequest(message: string): boolean {
    const datasetListPatterns = [
      'list datasets',
      'show datasets',
      'what datasets',
      'my datasets',
      'available datasets',
      'all datasets',
      'show my datasets',
      'list my datasets',
      'get datasets'
    ];

    return datasetListPatterns.some(pattern => message.includes(pattern));
  }

  /**
   * Create a simple response for direct API requests
   */
  private async simpleResponseFormulation(
    task: TaskState,
    userMessage: string,
    conversationContext?: string
  ): { step: string, output: string } {
    // Handle dataset listing
    if (task.type === TaskType.DatasetManagement) {
      const datasetCall = task.apiCalls.find(call => call.name === 'listDatasets');

      if (datasetCall) {
        const datasets = datasetCall.result.datasets || [];

        if (datasets.length > 0) {
          const datasetList = datasets.map((ds: any) => `- ${ds.name || 'Unnamed dataset'} (ID: ${ds.id})`).join('\n');
          return {
            step: 'response_formulation',
            output: `Here are your datasets:\n${datasetList}\n\nIs there anything specific you'd like to know about these datasets?`
          };
        } else {
          return {
            step: 'response_formulation',
            output: "I checked our system and you don't have any datasets available yet. Would you like to upload a new dataset?"
          };
        }
      }
    }

    // Default fallback - should not normally reach here
    return {
      step: 'response_formulation',
      output: "I've processed your request and gathered the information you asked for."
    };
  }

  /**
   * Execute API calls based on task type
   */
  private async executeApiCalls(task: TaskState, userMessage: string): Promise<void> {
    console.log(`Executing API calls for task type: ${task.type}`);

    // Check system status if not already checked
    if (!this.systemStatus.ollamaConnected && !this.systemStatus.backendConnected) {
      await this.checkSystemStatus();
    }

    try {
      // Always attempt to update connection status
      const systemStatus = await this.checkSystemStatus();
      task.apiCalls.push({
        name: 'checkSystemStatus',
        result: systemStatus
      });

      switch (task.type) {
        case TaskType.Conversation:
          // For conversation, just make sure Ollama is available
          if (!this.systemStatus.ollamaConnected) {
            throw new Error("Cannot generate conversation response: Ollama is not connected");
          }
          break;

        case TaskType.SystemCheck:
          // Additional system check info
          if (this.systemStatus.backendConnected) {
            try {
              // Get available models from backend
              try {
                const models = await listModels();
                task.apiCalls.push({
                  name: 'listModels',
                  result: models
                });
              } catch (error) {
                console.error('Error listing models:', error);
                task.apiCalls.push({
                  name: 'listModels',
                  result: { error: 'Failed to list models', models: [] }
                });
              }
            } catch (error) {
              console.error('Error listing models:', error);
            }
          }
          break;

        case TaskType.DatasetManagement:
          // Always try to get datasets for dataset-related tasks
          try {
            const datasets = await listDatasets();
            task.apiCalls.push({
              name: 'listDatasets',
              result: datasets
            });

            console.log('Datasets retrieved:', datasets);
          } catch (error) {
            console.error('Error listing datasets:', error);
            task.apiCalls.push({
              name: 'listDatasets',
              result: {
                error: 'Failed to list datasets',
                details: error instanceof Error ? error.message : 'Unknown error',
                datasets: [] // Add empty array for consistent handling
              }
            });
          }
          break;

        case TaskType.ModelTraining:
          // Try to gather all relevant information for training tasks
          try {
            // Get datasets
            const datasets = await listDatasets();
            task.apiCalls.push({
              name: 'listDatasets',
              result: datasets
            });

            // Get fine-tuning jobs
            const jobs = await listFineTuningJobs();
            task.apiCalls.push({
              name: 'listFineTuningJobs',
              result: jobs
            });

            // Get available models
            if (this.systemStatus.ollamaConnected) {
              const ollamaModels = await listOllamaModels();
              task.apiCalls.push({
                name: 'listOllamaModels',
                result: ollamaModels
              });
            }
          } catch (error) {
            console.error('Error getting training information:', error);
            task.apiCalls.push({
              name: 'getTrainingInfo',
              result: {
                error: 'Failed to get training information',
                details: error instanceof Error ? error.message : 'Unknown error'
              }
            });
          }
          break;

        case TaskType.ModelDeployment:
          // Gather all deployment-related information
          try {
            // Get fine-tuning jobs
            const jobs = await listFineTuningJobs();
            task.apiCalls.push({
              name: 'listFineTuningJobs',
              result: jobs
            });

            // Get current deployments
            const deployments = await listModelDeployments();
            task.apiCalls.push({
              name: 'listModelDeployments',
              result: deployments
            });

            // Get available models for deployment
            try {
              const models = await listModels();
              task.apiCalls.push({
                name: 'listModels',
                result: models
              });
            } catch (error) {
              console.error('Error listing models:', error);
              task.apiCalls.push({
                name: 'listModels',
                result: { error: 'Failed to list models', models: [] }
              });
            }
          } catch (error) {
            console.error('Error getting deployment information:', error);
            task.apiCalls.push({
              name: 'getDeploymentInfo',
              result: {
                error: 'Failed to get deployment information',
                details: error instanceof Error ? error.message : 'Unknown error'
              }
            });
          }
          break;

        case TaskType.ModelInference:
          // Get all models available for inference
          let models = [];

          // Try Ollama models first
          if (this.systemStatus.ollamaConnected) {
            try {
              const ollamaModels = await listOllamaModels();
              models = ollamaModels?.models || ollamaModels?.tags || [];
              task.apiCalls.push({
                name: 'listOllamaModels',
                result: { models }
              });
            } catch (error) {
              console.error('Error listing Ollama models:', error);
              task.apiCalls.push({
                name: 'listOllamaModels',
                result: {
                  error: 'Failed to list Ollama models',
                  models: []
                }
              });
            }
          }

          // Then try backend deployments
          if (this.systemStatus.backendConnected) {
            try {
              const deployments = await listModelDeployments();
              task.apiCalls.push({
                name: 'listModelDeployments',
                result: deployments
              });

              // Also get available API deployments
              try {
                const apiDeployments = await getAvailableDeployments();
                task.apiCalls.push({
                  name: 'getAvailableDeployments',
                  result: apiDeployments
                });
              } catch (error) {
                console.error('Error getting available deployments:', error);
                task.apiCalls.push({
                  name: 'getAvailableDeployments',
                  result: { error: 'Failed to get available deployments', deployments: [] }
                });
              }
            } catch (error) {
              console.error('Error listing model deployments:', error);
              task.apiCalls.push({
                name: 'listModelDeployments',
                result: {
                  error: 'Failed to list deployments',
                  deployments: []
                }
              });
            }
          }
          break;

        case TaskType.Unknown:
          // For unknown tasks, check if we can infer the type from the message
          const lowerCaseMessage = userMessage.toLowerCase();

          // Check for dataset-related keywords
          if (lowerCaseMessage.includes('dataset') ||
              lowerCaseMessage.includes('data') ||
              lowerCaseMessage.includes('upload') ||
              lowerCaseMessage.includes('list')) {
            try {
              const datasets = await listDatasets();
              task.apiCalls.push({
                name: 'listDatasets',
                result: datasets
              });
              // Update task type based on API call
              task.type = TaskType.DatasetManagement;
            } catch (error) {
              console.error('Error listing datasets for unknown task:', error);
            }
          }

          // Check for model-related keywords
          if (lowerCaseMessage.includes('model') ||
              lowerCaseMessage.includes('inference') ||
              lowerCaseMessage.includes('generate') ||
              lowerCaseMessage.includes('predict')) {
            try {
              if (this.systemStatus.ollamaConnected) {
                const ollamaModels = await listOllamaModels();
                task.apiCalls.push({
                  name: 'listOllamaModels',
                  result: ollamaModels
                });
              }
              // Update task type if not already set
              if (task.type === TaskType.Unknown) {
                task.type = TaskType.ModelInference;
              }
            } catch (error) {
              console.error('Error listing models for unknown task:', error);
            }
          }
          break;

        default:
          // Default to basic conversation mode
          break;
      }

      console.log(`Completed API calls for task ${task.id}. Total API calls: ${task.apiCalls.length}`);
    } catch (error) {
      console.error('Error executing API calls:', error);
      task.apiCalls.push({
        name: 'apiCallError',
        result: {
          error: 'Error executing API calls',
          details: error instanceof Error ? error.message : 'Unknown error'
        }
      });
    }
  }

  /**
   * Get response content from a completed task
   */
  public getTaskResponse(task: TaskState): string {
    // If the task has a message, return it (usually for errors)
    if (task.message) {
      return task.message;
    }

    // If the task has a thinking process with a response formulation step, return that
    if (task.thinkingProcess?.steps) {
      const responseStep = task.thinkingProcess.steps.find(step =>
        step.stage === 'response_formulation'
      );

      if (responseStep) {
        return responseStep.reasoning;
      }
    }

    // Check if task has a response formulation in reasoning steps
    const responseStep = task.reasoning.find(step => step.step === 'response_formulation');

    if (responseStep) {
      return responseStep.output;
    }

    // Fallback if no specific response formulation
    const lastStep = task.reasoning[task.reasoning.length - 1];
    return lastStep ? lastStep.output : 'No response available';
  }

  /**
   * Get all tasks
   */
  public getTasks(): TaskState[] {
    return this.tasks;
  }

  /**
   * Get a specific task by ID
   */
  public getTask(taskId: string): TaskState | undefined {
    return this.tasks.find(task => task.id === taskId);
  }

  /**
   * Get the most recent task
   */
  public getLatestTask(): TaskState | undefined {
    if (this.tasks.length === 0) return undefined;
    return this.tasks[this.tasks.length - 1];
  }
}

// Create a singleton instance
export const taskManager = new TaskManager();