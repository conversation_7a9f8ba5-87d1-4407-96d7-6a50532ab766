/**
 * Hugging Face API Service
 * Provides utilities for interacting with Hugging Face models
 */

// Default API URL for Hugging Face Inference API
const DEFAULT_API_URL = 'https://api-inference.huggingface.co/models';

export interface HuggingFaceOptions {
  apiKey?: string;
  apiUrl?: string;
  model?: string;
  waitForModel?: boolean;
  useCache?: boolean;
}

export interface TextGenerationOptions {
  max_new_tokens?: number;
  temperature?: number;
  top_p?: number;
  top_k?: number;
  repetition_penalty?: number;
  do_sample?: boolean;
  num_return_sequences?: number;
  return_full_text?: boolean;
  wait_for_model?: boolean;
}

export interface TextGenerationResponse {
  generated_text: string;
  [key: string]: any;
}

export interface CompletionOptions {
  prompt: string;
  model: string;
  options?: TextGenerationOptions;
  signal?: AbortSignal;
}

export interface StreamCompletionOptions extends CompletionOptions {
  onChunk: (chunk: any) => void;
  onError: (error: any) => void;
  onComplete: () => void;
}

/**
 * Hugging Face API client
 */
export class HuggingFaceAPI {
  private apiKey: string;
  private apiUrl: string;
  private defaultModel: string;
  private waitForModel: boolean;
  private useCache: boolean;

  /**
   * Create a new Hugging Face API client
   * 
   * @param options Configuration options
   */
  constructor(options: HuggingFaceOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.apiUrl = options.apiUrl || DEFAULT_API_URL;
    this.defaultModel = options.model || 'gpt2';
    this.waitForModel = options.waitForModel !== undefined ? options.waitForModel : true;
    this.useCache = options.useCache !== undefined ? options.useCache : true;
  }

  /**
   * Set the API key
   * 
   * @param apiKey Hugging Face API key
   */
  public setApiKey(apiKey: string): void {
    this.apiKey = apiKey;
  }

  /**
   * Set the API URL
   * 
   * @param apiUrl Hugging Face API URL
   */
  public setApiUrl(apiUrl: string): void {
    this.apiUrl = apiUrl;
  }

  /**
   * Set the default model
   * 
   * @param model Model ID
   */
  public setDefaultModel(model: string): void {
    this.defaultModel = model;
  }

  /**
   * Get the base URL for the API
   * 
   * @returns The base URL
   */
  public getBaseUrl(): string {
    return this.apiUrl;
  }

  /**
   * Generate text using a Hugging Face model
   * 
   * @param options Generation options
   * @returns Generated text
   */
  public async generateText(options: CompletionOptions): Promise<TextGenerationResponse[]> {
    const model = options.model || this.defaultModel;
    const url = `${this.apiUrl}/${model}`;
    
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };
    
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    const params: Record<string, any> = {
      inputs: options.prompt,
      options: {
        use_cache: this.useCache,
        wait_for_model: this.waitForModel,
        ...options.options
      }
    };
    
    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(params),
        signal: options.signal
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(`Hugging Face API error: ${error.error || response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error during text generation');
    }
  }

  /**
   * Stream text generation from a Hugging Face model
   * 
   * Note: This is a simulated stream since the Hugging Face API doesn't support streaming directly.
   * It breaks the response into chunks to simulate streaming behavior.
   * 
   * @param options Stream options
   */
  public async streamText(options: StreamCompletionOptions): Promise<void> {
    try {
      const response = await this.generateText({
        prompt: options.prompt,
        model: options.model || this.defaultModel,
        options: options.options,
        signal: options.signal
      });
      
      if (!response || response.length === 0) {
        options.onError(new Error('Empty response from Hugging Face API'));
        return;
      }
      
      // Get the generated text
      const generatedText = response[0].generated_text;
      
      // Simulate streaming by breaking the text into chunks
      const chunkSize = 10; // Characters per chunk
      const chunks = [];
      
      for (let i = 0; i < generatedText.length; i += chunkSize) {
        chunks.push(generatedText.slice(i, i + chunkSize));
      }
      
      // Send chunks with a small delay to simulate streaming
      for (const chunk of chunks) {
        options.onChunk({ response: chunk });
        await new Promise(resolve => setTimeout(resolve, 50));
      }
      
      options.onComplete();
    } catch (error) {
      options.onError(error);
    }
  }

  /**
   * List available models from Hugging Face
   * 
   * @param filter Optional filter for model types
   * @returns List of models
   */
  public async listModels(filter?: string): Promise<any> {
    const url = 'https://huggingface.co/api/models';
    const params = new URLSearchParams();
    
    if (filter) {
      params.append('filter', filter);
    }
    
    const headers: Record<string, string> = {};
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    try {
      const response = await fetch(`${url}?${params.toString()}`, {
        method: 'GET',
        headers
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(`Hugging Face API error: ${error.error || response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error listing models');
    }
  }

  /**
   * Get model information
   * 
   * @param modelId Model ID
   * @returns Model information
   */
  public async getModelInfo(modelId: string): Promise<any> {
    const url = `https://huggingface.co/api/models/${modelId}`;
    
    const headers: Record<string, string> = {};
    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }
    
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers
      });
      
      if (!response.ok) {
        const error = await response.json().catch(() => ({ error: response.statusText }));
        throw new Error(`Hugging Face API error: ${error.error || response.statusText}`);
      }
      
      return await response.json();
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Unknown error getting model info');
    }
  }
}

// Create a singleton instance with default settings
let huggingFaceAPIInstance: HuggingFaceAPI | null = null;

/**
 * Get the Hugging Face API instance
 * 
 * @param options Optional configuration options
 * @returns Hugging Face API instance
 */
export function getHuggingFaceAPI(options: HuggingFaceOptions = {}): HuggingFaceAPI {
  if (!huggingFaceAPIInstance) {
    huggingFaceAPIInstance = new HuggingFaceAPI(options);
  } else {
    // Update existing instance with new options if provided
    if (options.apiKey) huggingFaceAPIInstance.setApiKey(options.apiKey);
    if (options.apiUrl) huggingFaceAPIInstance.setApiUrl(options.apiUrl);
    if (options.model) huggingFaceAPIInstance.setDefaultModel(options.model);
  }
  
  return huggingFaceAPIInstance;
}
