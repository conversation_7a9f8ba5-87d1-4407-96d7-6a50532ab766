# Ollama UI Builder

A modern web interface for interacting with Ollama LLMs. This application provides a user-friendly way to chat with and utilize Ollama's language models, with built-in code editing capabilities.

## Features

- **Interactive Chat Interface**: Chat with Ollama models to generate code or get assistance
- **Code Editor**: Built-in Monaco editor with syntax highlighting and code formatting
- **Model Selection**: Choose from your locally installed Ollama models
- **File Management**: Create, edit, and organize multiple files
- **Customizable Settings**: Configure connection modes, API settings, and editor preferences
- **Dark/Light Theme Support**: Choose your preferred theme for comfortable coding
- **Responsive Design**: Works on desktop and mobile devices
- **Connection Troubleshooter**: Diagnose and fix connection issues with Ollama
- **Temperature Control**: Adjust the creativity level of the AI responses

## Getting Started

### Prerequisites

- [Ollama](https://ollama.ai/) installed and running on your machine
- Node.js (v18 or later)
- npm, yarn, or pnpm

### Installation

1. Clone this repository:
   ```
   git clone https://github.com/your-username/ollama-ui.git
   cd ollama-ui
   ```

2. Install dependencies:
   ```
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. Start the development server:
   ```
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. Open your browser and navigate to http://localhost:3000

### Network Access for Multiple Users

To allow other users on your network to access the UI:

1. Run the server with the host flag to listen on all network interfaces:
   ```
   npm run dev -- -H 0.0.0.0
   # or
   yarn dev -- -H 0.0.0.0
   ```

2. Find your computer's IP address:
   - Windows: Open Command Prompt and type `ipconfig`
   - Mac: Open Terminal and type `ifconfig` or go to System Preferences → Network
   - Linux: Open Terminal and type `ip addr show` or `ifconfig`

3. Other users can access the UI by entering `http://YOUR_IP_ADDRESS:3000` in their browsers

4. Configure the Ollama connection:
   - If Ollama is running on the same machine as the UI, users should select "Local" in settings
   - If Ollama is running on a different machine, select "Custom URL" and enter that machine's address with the Ollama port (default: 11434)

### Building for Production

```
npm run build
npm run start
# or
yarn build
yarn start
# or
pnpm build
pnpm start
```

## Connection Modes

The UI supports three connection modes to connect to your Ollama service:

1. **Local**: Connects to `http://127.0.0.1:11434` (best for local development)
2. **Auto**: Automatically detects the server from the current hostname (useful for deployments)
3. **Custom**: Specify a custom URL to connect to a remote Ollama instance

## Using the Chat Interface

1. Select a model from the dropdown menu
2. Type your question or code generation request in the input box
3. Press Enter or click the Send button
4. View the response in the chat area

You can ask the model various questions or request code examples. Try prompts like:
- "Explain how to use React hooks"
- "Write a function to sort an array of objects by a specific property"
- "Create a simple API endpoint using Express"
- "Help me debug this code: [paste your code]"

## Using the Code Editor

1. Switch to the Editor tab
2. Create new files or edit existing ones
3. Save your changes
4. Export your project or individual files

## Troubleshooting

If you encounter connection issues:

1. Ensure Ollama is installed and running
2. Check that you have the models you want to use installed (`ollama list`)
3. Verify the connection URL in settings
4. Use the built-in Connection Troubleshooter

## Technologies Used

- Next.js 15
- React 19
- TypeScript
- Tailwind CSS
- Shadcn UI Components
- Monaco Editor
- Ollama API Integration

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

