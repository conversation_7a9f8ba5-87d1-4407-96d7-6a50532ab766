# Creating Custom Model Files with Ollama

This guide explains how to create, customize, and share your own AI models using Ollama. By following these steps, you'll learn how to create Modelfiles, build custom models, and optimize them for your specific use cases.

## Table of Contents

- [Understanding Modelfiles](#understanding-modelfiles)
- [Creating Your First Modelfile](#creating-your-first-modelfile)
- [Modelfile Parameters and Options](#modelfile-parameters-and-options)
- [Building Models with Custom Instructions](#building-models-with-custom-instructions)
- [Fine-tuning and Customization](#fine-tuning-and-customization)
- [Sharing and Distributing Models](#sharing-and-distributing-models)
- [Advanced Techniques](#advanced-techniques)
- [Troubleshooting Common Issues](#troubleshooting-common-issues)

## Understanding Modelfiles

A Modelfile is a configuration file that defines how Ollama should set up and run a language model. It allows you to customize an existing LLM by adding specific instructions, context, and parameters.

Modelfiles are similar to Dockerfiles, if you're familiar with those. They start with a base model and then apply various customizations on top of it.

### Basic Structure

```
FROM llama2
PARAMETER temperature 0.7
PARAMETER top_p 0.9
SYSTEM You are a helpful AI assistant.
```

This simple Modelfile:
1. Uses `llama2` as the base model
2. Sets the temperature parameter to 0.7
3. Sets the top_p parameter to 0.9
4. Provides a system prompt that defines the AI's persona

## Creating Your First Modelfile

Let's create a basic Modelfile for a helpful coding assistant:

1. Create a new file named `Modelfile` (with no file extension)
2. Add the following content:

```
FROM codellama
PARAMETER temperature 0.3
SYSTEM You are a helpful coding assistant. You help users write clean, efficient, and bug-free code. When asked to explain code, you break it down in simple terms. When asked to write code, you provide well-commented solutions.
```

3. Save the file
4. Build the model using the Ollama CLI:

```bash
ollama create coding-assistant -f ./Modelfile
```

5. Now you can run your custom model:

```bash
ollama run coding-assistant
```

## Modelfile Parameters and Options

Modelfiles support a variety of directives to customize your model. Here are the key ones:

### FROM

Specifies the base model to build upon.

```
FROM llama2
```

You can also specify a specific model version:

```
FROM llama2:13b
```

### PARAMETER

Sets runtime parameters for the model. Common parameters include:

- `temperature`: Controls randomness (0.0-1.0)
- `top_p`: Controls diversity via nucleus sampling
- `top_k`: Limits vocabulary to top K options
- `repeat_penalty`: Penalizes repetition
- `context_length`: Maximum token context window

Example:

```
PARAMETER temperature 0.3
PARAMETER top_p 0.8
PARAMETER repeat_penalty 1.1
```

### SYSTEM

Defines the system prompt that establishes the model's persona and behavior.

```
SYSTEM You are a friendly and helpful assistant that specializes in explaining complex topics simply.
```

### TEMPLATE

Customizes how prompts are formatted before being sent to the model. This is useful for models that were trained with specific prompt formats.

```
TEMPLATE """
<s>[INST] {{ .System }} {{ .Prompt }} [/INST]
"""
```

Variables:
- `{{ .System }}`: System prompt
- `{{ .Prompt }}`: User input
- `{{ .Response }}`: Model response (for chat history)

### ADAPTER

For models that support Parameter-Efficient Fine-Tuning (PEFT) methods:

```
ADAPTER example-lora.bin
```

## Building Models with Custom Instructions

Let's create a more sophisticated AI assistant for data science:

```
FROM llama2:13b
PARAMETER temperature 0.4
PARAMETER top_p 0.9
PARAMETER repeat_penalty 1.2
SYSTEM You are DataSciBot, an expert in data science, statistics, and machine learning. You help users analyze data, choose appropriate models, and interpret results. You explain concepts clearly and provide code examples in Python using libraries like pandas, scikit-learn, and TensorFlow.

When asked about data analysis, you first inquire about the nature of the data and the user's goals.
When giving code examples, you include comments explaining the logic.
You warn users about potential pitfalls and suggest best practices.
You do not make up information. If you are unsure, you acknowledge your limitations.
```

Build this model:

```bash
ollama create datascibot -f ./Modelfile
```

## Fine-tuning and Customization

### Using the FILE Directive

The `FILE` directive lets you include external content in your model context. This is useful for providing reference materials or examples.

1. Create a file named `examples.txt` with some data science examples
2. Add the FILE directive to your Modelfile:

```
FROM llama2:13b
PARAMETER temperature 0.4
SYSTEM You are a data science assistant.
FILE examples.txt
```

The content of `examples.txt` will be included in the system context.

### Custom Response Templates

To customize how the model formulates responses, use the `TEMPLATE` directive:

```
FROM mistral
PARAMETER temperature 0.7
SYSTEM You are a helpful assistant.
TEMPLATE """
<|im_start|>system
{{ .System }}
<|im_end|>
<|im_start|>user
{{ .Prompt }}
<|im_end|>
<|im_start|>assistant
"""
```

This template formats prompts according to the Mistral model's expected format.

## Sharing and Distributing Models

### Exporting Models

You can export your custom model to share with others:

```bash
ollama export mymodel > mymodel.tar
```

### Importing Models

Others can import your exported model:

```bash
ollama import mymodel.tar
```

### Publishing to the Ollama Library

While Ollama doesn't have an official public model library for community models yet, you can share your Modelfiles via GitHub or other platforms.

Example repository structure:
```
models/
  ├── coding-assistant/
  │   └── Modelfile
  ├── datascibot/
  │   └── Modelfile
  └── README.md
```

Users can then clone your repository and build the models themselves.

## Advanced Techniques

### Multi-Model Orchestration

You can create specialized models for different tasks and switch between them as needed:

```bash
# Create different specialist models
ollama create coder -f ./Coder.Modelfile
ollama create writer -f ./Writer.Modelfile
ollama create researcher -f ./Researcher.Modelfile

# Run the appropriate model for each task
ollama run coder "Write a function to calculate prime numbers"
ollama run writer "Draft a product announcement email"
ollama run researcher "Summarize recent papers on quantum computing"
```

### Layered Prompting

Add multiple system prompts to build more complex behaviors:

```
FROM llama2
SYSTEM You are a helpful AI assistant.
SYSTEM You specialize in cybersecurity and network protection.
SYSTEM Always consider security implications in your answers.
SYSTEM Format your responses with markdown for readability.
```

### Using LLama.cpp Quantized Models

For models that come from llama.cpp ecosystem:

```
FROM ./path/to/ggml-model-q4_0.bin
PARAMETER temperature 0.7
SYSTEM You are a helpful assistant.
```

## Troubleshooting Common Issues

### Model Building Errors

If you encounter errors during model creation:

1. Check for syntax errors in your Modelfile
2. Ensure the base model is available (run `ollama list` to check)
3. Make sure you have enough disk space
4. Check system resources if building large models

### Response Quality Issues

If your model isn't responding as expected:

1. Review your SYSTEM prompt for clarity and specificity
2. Adjust temperature (lower for more focused answers, higher for creativity)
3. Try different prompting strategies
4. Experiment with different base models

### Memory Issues

If your model runs out of memory:

1. Try a smaller base model (7B parameters instead of 13B)
2. Reduce context length with `PARAMETER context_length 2048`
3. Use quantized models (4-bit instead of 16-bit)

## Examples of Specialized Modelfiles

### Customer Service Bot

```
FROM llama2
PARAMETER temperature 0.3
SYSTEM You are CustomerCareAI, a professional and empathetic customer service representative.

Your goals:
1. Address customer concerns politely and efficiently
2. Find solutions to customer problems
3. Maintain a professional tone
4. Follow up to ensure customer satisfaction

When a customer has a complaint:
- Acknowledge their frustration
- Apologize for the inconvenience
- Propose a concrete solution
- Explain next steps clearly

Never argue with customers or make excuses. Focus on solutions, not problems.
Always thank customers for their patience and business.
```

### Legal Assistant

```
FROM llama2:13b
PARAMETER temperature 0.2
PARAMETER top_p 0.9
SYSTEM You are LegalAssistantAI, a helpful tool for legal professionals.

You can help with:
- Summarizing legal documents
- Explaining legal concepts in plain language
- Suggesting relevant case law (but not providing legal advice)
- Drafting document templates

Important limitations:
- You are not a licensed attorney
- You do not provide legal advice
- You remind users to consult qualified legal counsel
- You do not guarantee accuracy of information

Always maintain formal language appropriate for legal contexts.
When uncertain, explicitly acknowledge the limits of your knowledge.
```

## Conclusion

Creating custom models with Ollama gives you the power to tailor large language models to your specific needs. By mastering Modelfiles, you can build AI assistants that are more helpful, specialized, and aligned with your requirements.

Remember that the quality of a custom model depends heavily on the quality of your instructions and system prompt. Take time to refine these elements, and don't hesitate to experiment with different parameters to find the optimal configuration.

For more information and updates, visit:
- [Ollama Documentation](https://github.com/ollama/ollama/blob/main/README.md)
- [Ollama GitHub Repository](https://github.com/ollama/ollama) 