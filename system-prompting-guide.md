# System Prompting with Ollama UI: Building AI Agents and Custom Assistants

## Table of Contents
1. [Introduction to System Prompting](#introduction-to-system-prompting)
2. [Understanding System Prompts in Ollama](#understanding-system-prompts-in-ollama)
3. [Creating Effective System Prompts](#creating-effective-system-prompts)
4. [System Prompting in the Ollama UI](#system-prompting-in-the-ollama-ui)
5. [Building Custom AI Agents](#building-custom-ai-agents)
6. [Advanced Reasoning Engine](#advanced-reasoning-engine)
7. [Templates and Frameworks](#templates-and-frameworks)
8. [Advanced Techniques](#advanced-techniques)
9. [Use Cases and Examples](#use-cases-and-examples)
10. [Best Practices](#best-practices)

## Introduction to System Prompting

System prompting is a powerful technique that allows you to define the behavior, capabilities, and personality of an AI model. It serves as the foundation for creating custom AI agents and specialized assistants that can help with specific tasks or domains.

### What is a System Prompt?

A system prompt is a set of instructions given to an AI model at the beginning of a conversation that guides how the model should respond to user queries. It's the equivalent of giving a job description and behavioral guidelines to the AI before it starts interacting with users.

System prompts can:
- Define the AI's role and expertise
- Set constraints on what the AI can or cannot do
- Provide specialized knowledge or context
- Establish a specific personality or tone
- Direct the formatting of responses
- Guide the AI to follow certain patterns or procedures

**Code Example: Basic System Prompt Structure**

```javascript
// Basic system prompt structure in the Ollama API
const chatOptions = {
  model: "llama3",
  messages: [
    {
      role: "system",
      content: "You are a helpful expert in JavaScript who always provides clear, concise answers with practical code examples."
    },
    {
      role: "user",
      content: "How can I implement a debounce function?"
    }
  ],
  stream: true
};

// Send the chat request
await ollamaApi.streamChat(chatOptions, processResponse);
```

### The Power of System Prompting

Effective system prompting transforms generic AI models into specialized tools by:
- **Focusing the model's knowledge**: Directing the model to emphasize particular domains of expertise
- **Standardizing outputs**: Creating consistent response formats for better integration with other tools
- **Improving accuracy**: Reducing hallucinations by constraining responses to specific guidelines
- **Customizing behavior**: Tailoring the AI's personality and interaction style to match specific needs

**Code Example: Before and After System Prompting**

```javascript
// BEFORE: Generic query without system prompt
const genericQuery = {
  model: "llama3",
  messages: [
    {
      role: "user",
      content: "Write a function to sort an array of objects by a property"
    }
  ]
};

// AFTER: Same query with specific system prompt
const specializedQuery = {
  model: "llama3",
  messages: [
    {
      role: "system",
      content: `You are a TypeScript expert focusing on modern, type-safe implementations.
Always include:
1. Type definitions
2. Complete function implementations
3. Usage examples
4. Edge case handling
5. Time and space complexity analysis`
    },
    {
      role: "user",
      content: "Write a function to sort an array of objects by a property"
    }
  ]
};
```

## Understanding System Prompts in Ollama

Ollama provides two main ways to work with system prompts:

1. **Modelfile System Prompts**: Permanently attached to a model variant through a Modelfile
2. **Session System Prompts**: Applied dynamically during a chat session through the API or UI

### Modelfile System Prompts

When you create a custom model in Ollama using a Modelfile, you can define a default system prompt:

```
FROM llama3
SYSTEM You are a helpful coding assistant specializing in JavaScript development. Always provide complete, working code examples with explanations. Focus on modern best practices and efficient solutions.
```

This creates a variant of the model that will always use this system prompt as its default behavior.

**Code Example: Creating and Using a Custom Model with a System Prompt**

```bash
# Step 1: Create a file named 'Modelfile' with the following content
cat > Modelfile << EOF
FROM llama3
SYSTEM You are a JavaScript expert who specializes in React and modern web development.
You always provide complete, working code examples with detailed explanations.
You prioritize functional components, hooks, and ES6+ syntax.
Your code follows best practices including proper error handling and accessibility.
EOF

# Step 2: Create the custom model
ollama create js-expert -f Modelfile

# Step 3: Use the custom model
ollama run js-expert "Create a React component for a login form with validation"
```

In the UI, you would then be able to select this custom "js-expert" model from the model dropdown.

### Session System Prompts

In the Ollama UI, you can apply system prompts dynamically during a conversation using the chat interface. This doesn't modify the base model but influences the current conversation.

**Code Example: Setting a Session System Prompt via API**

```javascript
// JavaScript example of setting a system prompt for a chat session
async function startChatWithSystemPrompt() {
  const systemPrompt = `You are a database expert specializing in SQL optimization.
  When reviewing queries, always:
  1. Identify potential performance issues
  2. Suggest indexes that could improve performance
  3. Rewrite queries to be more efficient
  4. Explain the reasoning behind your optimizations`;

  const chatMessages = [
    { role: "system", content: systemPrompt },
    { role: "user", content: "Can you optimize this query: SELECT * FROM orders WHERE customer_id = 123 AND order_date > '2023-01-01'" }
  ];

  const chatOptions = {
    model: "llama3",
    messages: chatMessages,
    stream: true
  };

  let response = "";
  await ollamaApi.streamChat(
    chatOptions,
    (chunk) => {
      if (chunk.message?.content) {
        response += chunk.message.content;
        updateUIWithResponse(response);
      }
    },
    (error) => console.error("Error:", error),
    () => console.log("Stream complete")
  );
}
```

## Creating Effective System Prompts

### Anatomy of a Great System Prompt

An effective system prompt typically includes:

1. **Role Definition**: What specific role or persona the AI should adopt
2. **Expertise Areas**: Domains where the AI should demonstrate deep knowledge
3. **Response Guidelines**: How information should be structured and presented
4. **Constraints**: Limitations on what the AI should or shouldn't do
5. **Examples**: Sample interactions that demonstrate desired behavior

**Code Example: Comprehensive System Prompt Structure**

```javascript
const comprehensiveSystemPrompt = `You are a senior backend developer specializing in Node.js, Express, and MongoDB.

Your areas of expertise include:
- RESTful API design and implementation
- Database schema modeling and optimization
- Authentication and authorization systems
- Error handling and logging
- Performance optimization
- Security best practices

When responding to questions:
1. First clarify your understanding of the problem
2. Provide a complete, working solution with all necessary code
3. Explain your implementation choices
4. Point out potential edge cases or security concerns
5. Suggest testing approaches

Always:
- Use modern ES6+ syntax and async/await patterns
- Include proper error handling
- Follow REST best practices
- Consider security implications

Never:
- Provide incomplete code snippets
- Ignore security considerations
- Use deprecated methods or libraries

Here's an example of how to structure your responses:

User: "How do I create a simple REST API with Express and MongoDB?"

Your response structure:
1. Brief overview of the approach
2. Complete code implementation for:
   - Server setup
   - Database connection
   - Route definitions
   - Controller logic
   - Error handling middleware
3. Explanation of key concepts
4. Testing instructions
5. Security considerations`;

const chatOptions = {
  model: "llama3",
  messages: [
    { role: "system", content: comprehensiveSystemPrompt },
    { role: "user", content: "How do I implement JWT authentication in an Express app?" }
  ]
};
```

### Example Structure

```
You are {role} specialized in {domains}.

When responding:
- {guideline 1}
- {guideline 2}
- {guideline 3}

Always {required behavior}.
Never {prohibited behavior}.

Format your responses using {specific format}.
```

**Code Example: Dynamic System Prompt Generator**

```javascript
// Function to generate customized system prompts
function generateSystemPrompt(params) {
  const {
    role,
    expertise = [],
    guidelines = [],
    required = [],
    prohibited = [],
    formatInstructions = ""
  } = params;

  const expertiseList = expertise.map(item => `- ${item}`).join('\n');
  const guidelinesList = guidelines.map(item => `- ${item}`).join('\n');
  const requiredList = required.join('\n');
  const prohibitedList = prohibited.join('\n');

  return `You are ${role}.

Your areas of expertise include:
${expertiseList}

When responding:
${guidelinesList}

Always:
${requiredList}

Never:
${prohibitedList}

${formatInstructions}`;
}

// Example usage
const pythonDataSciencePrompt = generateSystemPrompt({
  role: "a data science expert specializing in Python for data analysis",
  expertise: [
    "Data cleaning and preprocessing",
    "Exploratory data analysis",
    "Statistical modeling",
    "Machine learning implementation",
    "Data visualization"
  ],
  guidelines: [
    "Break down complex problems into steps",
    "Provide complete, executable code",
    "Explain the reasoning behind your approach",
    "Suggest alternative methods when applicable"
  ],
  required: [
    "Use pandas, NumPy, and scikit-learn best practices",
    "Include data validation steps",
    "Consider performance for large datasets",
    "Provide visualizations when relevant"
  ],
  prohibited: [
    "Use outdated Python 2.x syntax",
    "Ignore potential issues with missing data",
    "Provide solutions without explaining the underlying concepts"
  ],
  formatInstructions: "Format your code using ```python code blocks and include comments for complex operations."
});

console.log(pythonDataSciencePrompt);
```

### Example System Prompt for a Code Expert

```
You are an expert software developer specializing in React, TypeScript, and modern web development.

When responding to coding questions:
- Provide complete, working code examples with all necessary imports
- Include explanations of why certain approaches were chosen
- Consider edge cases and error handling
- Use modern best practices and patterns

Always structure your code for readability and maintainability.
Never provide incomplete solutions that won't compile or run.

Format your code examples with proper syntax highlighting using markdown code blocks with language indicators.
```

**Code Example: Implementing This System Prompt in a Chat Session**

```javascript
// Function to start a chat with the code expert system prompt
async function startCodeExpertChat(userQuestion) {
  const codeExpertPrompt = `You are an expert software developer specializing in React, TypeScript, and modern web development.

When responding to coding questions:
- Provide complete, working code examples with all necessary imports
- Include explanations of why certain approaches were chosen
- Consider edge cases and error handling
- Use modern best practices and patterns

Always structure your code for readability and maintainability.
Never provide incomplete solutions that won't compile or run.

Format your code examples with proper syntax highlighting using markdown code blocks with language indicators.`;

  // Set up the chat
  const chatSession = {
    model: "llama3",
    messages: [
      { role: "system", content: codeExpertPrompt },
      { role: "user", content: userQuestion }
    ],
    options: {
      temperature: 0.7,
      top_p: 0.9
    }
  };

  // Sample implementation of processing the response
  try {
    const response = await performOllamaChatRequest(chatSession);

    // Extract code blocks for potential execution or display
    const codeBlocks = extractCodeBlocks(response);

    // Display the response in the UI
    updateChatUI(response, codeBlocks);

    // Optionally save the expert prompt for future use
    saveSystemPromptTemplate("Code Expert", codeExpertPrompt);
  } catch (error) {
    console.error("Chat session failed:", error);
    showErrorMessage("Failed to get a response from the code expert.");
  }
}

// Helper function to extract code blocks from the response
function extractCodeBlocks(response) {
  const codeBlockRegex = /```(\w+)?\n([\s\S]*?)```/g;
  const codeBlocks = [];
  let match;

  while ((match = codeBlockRegex.exec(response)) !== null) {
    codeBlocks.push({
      language: match[1] || 'text',
      code: match[2].trim()
    });
  }

  return codeBlocks;
}

// Example usage
startCodeExpertChat("How can I create a custom hook in React that manages form state with validation?");
```

## System Prompting in the Ollama UI

The Ollama UI provides several ways to work with system prompts:

### Using Built-in System Prompts

The Ollama UI comes with built-in system prompts for different frameworks and use cases. These can be selected when starting a new chat or coding session:

1. Navigate to the framework selector in the UI
2. Choose a framework like "HTML/CSS/JS", "React", "Next.js", or others
3. The appropriate system prompt will be automatically applied

Each framework template includes:
- A tailored system prompt for the specific framework
- Default file structures for that framework
- Specialized code extraction and formatting settings

**Code Example: Framework Template Selection in UI**

```typescript
// This is how framework templates are defined in the code
interface FrameworkTemplate {
  name: string;
  description: string;
  languages: string[];
  fileExtensions: Record<string, string>;
  defaultFiles: {
    name: string;
    content: string;
    language: string;
  }[];
  systemPrompt: string;
  // Other configuration options
}

// Example of using the selected framework template in a chat
function startChatWithFramework(frameworkName: string, userPrompt: string) {
  // Get the selected framework template
  const template = frameworkTemplates[frameworkName];

  if (!template) {
    console.error(`Framework template ${frameworkName} not found`);
    return;
  }

  // Set up the chat with the template's system prompt
  const messages = [
    { role: "system", content: template.systemPrompt },
    { role: "user", content: userPrompt }
  ];

  // Start the chat with the Ollama API
  startChatSession(messages);
}

// Example usage
startChatWithFramework('react', 'Create a dropdown component that supports multiple selection');
```

### Creating Custom System Prompts

To create and save your own system prompts:

1. Open the "Prompt Templates" dialog from the settings menu
2. Click "Create New"
3. Provide a name for your template
4. Write your system prompt in the template content field
5. Save the template

Your custom system prompts will be available for selection in future sessions.

**Code Example: Implementing Custom Prompt Templates**

```typescript
// Types for prompt templates
interface PromptTemplate {
  id: string;
  name: string;
  template: string;
}

// Function to save a new prompt template
function savePromptTemplate(name: string, template: string): PromptTemplate {
  const newTemplate: PromptTemplate = {
    id: generateUniqueId(), // e.g., using uuid
    name: name,
    template: template
  };

  // Get existing templates from localStorage
  const existingTemplates = JSON.parse(localStorage.getItem('promptTemplates') || '[]');

  // Add the new template
  const updatedTemplates = [...existingTemplates, newTemplate];

  // Save to localStorage
  localStorage.setItem('promptTemplates', JSON.stringify(updatedTemplates));

  return newTemplate;
}

// Function to load all saved templates
function loadPromptTemplates(): PromptTemplate[] {
  return JSON.parse(localStorage.getItem('promptTemplates') || '[]');
}

// Example usage in a React component
function PromptTemplateDialog() {
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [newTemplateName, setNewTemplateName] = useState('');
  const [newTemplateContent, setNewTemplateContent] = useState('');

  // Load templates on component mount
  useEffect(() => {
    setTemplates(loadPromptTemplates());
  }, []);

  const handleSaveTemplate = () => {
    const savedTemplate = savePromptTemplate(newTemplateName, newTemplateContent);
    setTemplates([...templates, savedTemplate]);
    setNewTemplateName('');
    setNewTemplateContent('');
    // Show success message
  };

  // Render UI components for template management
  return (
    <Dialog>
      <DialogTitle>Manage Prompt Templates</DialogTitle>
      <DialogContent>
        {/* List existing templates */}
        <div className="templates-list">
          {templates.map(template => (
            <div key={template.id} className="template-item">
              <h3>{template.name}</h3>
              <pre>{template.template.substring(0, 100)}...</pre>
              <button onClick={() => handleEditTemplate(template)}>Edit</button>
              <button onClick={() => handleDeleteTemplate(template.id)}>Delete</button>
            </div>
          ))}
        </div>

        {/* Form to create new template */}
        <form onSubmit={(e) => { e.preventDefault(); handleSaveTemplate(); }}>
          <input
            type="text"
            placeholder="Template name"
            value={newTemplateName}
            onChange={(e) => setNewTemplateName(e.target.value)}
            required
          />
          <textarea
            placeholder="Template content"
            value={newTemplateContent}
            onChange={(e) => setNewTemplateContent(e.target.value)}
            required
            rows={10}
          />
          <button type="submit">Save Template</button>
        </form>
      </DialogContent>
    </Dialog>
  );
}
```

### Applying System Prompts in Conversations

Within an existing chat:

1. In the enhanced chat interface, use the "System Prompt" dropdown
2. Select one of your saved prompts or a built-in prompt
3. The system prompt will be applied to the current conversation

Alternatively, for a one-time system prompt:

1. Start your message with "/system" followed by your prompt
2. Example: `/system You are a Python expert focusing on data science and machine learning.`
3. Send the message to set this as the system prompt for the conversation

**Code Example: Applying a System Prompt to an Ongoing Conversation**

```typescript
// Function to handle user messages including special commands
function handleUserMessage(input: string, chatHistory: Message[]) {
  // Check if the message is a system prompt command
  if (input.startsWith('/system ')) {
    // Extract the system prompt from the command
    const systemPrompt = input.substring('/system '.length);

    // Find and replace any existing system message, or add a new one at the beginning
    let updatedHistory = [...chatHistory];
    const systemMessageIndex = updatedHistory.findIndex(msg => msg.role === 'system');

    if (systemMessageIndex >= 0) {
      // Replace existing system message
      updatedHistory[systemMessageIndex] = {
        role: 'system',
        content: systemPrompt,
        id: updatedHistory[systemMessageIndex].id
      };
    } else {
      // Add new system message at the beginning
      updatedHistory.unshift({
        role: 'system',
        content: systemPrompt,
        id: generateUniqueId()
      });
    }

    // Update the chat history
    setChatHistory(updatedHistory);

    // Show confirmation to the user
    addSystemFeedback(`System prompt updated: "${systemPrompt.substring(0, 50)}..."`);

    // Clear the input field
    return;
  }

  // Handle regular message
  sendMessageToOllama(input, chatHistory);
}

// Example of applying a saved template to the conversation
function applyPromptTemplate(templateId: string, chatHistory: Message[]) {
  // Find the template by ID
  const templates = loadPromptTemplates();
  const template = templates.find(t => t.id === templateId);

  if (!template) {
    console.error(`Template with ID ${templateId} not found`);
    return;
  }

  // Handle the template as a system command
  handleUserMessage(`/system ${template.template}`, chatHistory);
}

// Example of a dropdown component to select templates
function SystemPromptSelector({ chatHistory }) {
  const templates = loadPromptTemplates();

  return (
    <select
      onChange={(e) => applyPromptTemplate(e.target.value, chatHistory)}
      defaultValue=""
    >
      <option value="" disabled>Select system prompt</option>
      {templates.map(template => (
        <option key={template.id} value={template.id}>
          {template.name}
        </option>
      ))}
    </select>
  );
}
```

### Prompt Variables and Placeholders

The Ollama UI supports variables in system prompts that get replaced at runtime:

- `{{fileName}}`: Current file name being worked on
- `{{language}}`: Programming language of the current context
- `{{code}}`: Selected or current code snippet

Example template with variables:

```
You are reviewing the {{language}} code in {{fileName}}.

Analyze the following code and provide feedback on:
- Code quality and readability
- Potential bugs or edge cases
- Performance optimizations
- Security considerations

Code to review:
{{code}}
```

**Code Example: Processing Templates with Variables**

```typescript
// Function to process a template with variables
function processTemplateVariables(template: string, variables: Record<string, string>): string {
  return template.replace(/\{\{(\w+)\}\}/g, (match, variableName) => {
    return variables[variableName] || match;
  });
}

// Example: Code review use case
function startCodeReview(fileName: string, code: string) {
  // Determine the language from the file extension
  const fileExtension = fileName.split('.').pop()?.toLowerCase() || '';
  const languageMap: Record<string, string> = {
    'js': 'JavaScript',
    'ts': 'TypeScript',
    'py': 'Python',
    'html': 'HTML',
    'css': 'CSS',
    'java': 'Java',
    'cs': 'C#',
    'go': 'Go',
    'rb': 'Ruby',
    'php': 'PHP',
    // Add more mappings as needed
  };

  const language = languageMap[fileExtension] || 'Unknown';

  // Load the code review template
  const templates = loadPromptTemplates();
  const codeReviewTemplate = templates.find(t => t.name === 'Code Review') || {
    id: 'default',
    name: 'Code Review',
    template: `You are reviewing the {{language}} code in {{fileName}}.

Analyze the following code and provide feedback on:
- Code quality and readability
- Potential bugs or edge cases
- Performance optimizations
- Security considerations

Code to review:
{{code}}`
  };

  // Process the template with variables
  const processedPrompt = processTemplateVariables(codeReviewTemplate.template, {
    fileName,
    language,
    code
  });

  // Start a chat with the processed system prompt
  const messages = [
    { role: 'system', content: processedPrompt },
    { role: 'user', content: 'Please review this code thoroughly.' }
  ];

  startChatSession(messages);
}

// Example usage
function handleCodeReviewRequest() {
  const editor = getActiveEditor();
  const fileName = editor.getFileName();
  const code = editor.getSelectedText() || editor.getAllText();

  startCodeReview(fileName, code);
}
```

## Building Custom AI Agents

Using system prompts, you can create specialized AI agents for different tasks:

### Code Generator Agent

```
You are a code generation agent specialized in creating complete, working applications.

Follow these steps when generating code:
1. Analyze the user's requirements thoroughly
2. Plan the structure and components needed
3. Generate complete code for each necessary file
4. Include clear comments explaining complex logic
5. Ensure all dependencies are properly imported

For each file, use markdown code blocks with the appropriate language specified.
Format file paths like this: ```filename: path/to/file.ext```.

Never omit critical parts of the code or use placeholders like "..." or "[additional code here]".
Always produce complete, executable code that can be used immediately.
```

**Code Example: Implementing a Code Generator Agent**

```typescript
// Define the code generator agent
const codeGeneratorAgent = {
  name: "Code Generator",
  systemPrompt: `You are a code generation agent specialized in creating complete, working applications.

Follow these steps when generating code:
1. Analyze the user's requirements thoroughly
2. Plan the structure and components needed
3. Generate complete code for each necessary file
4. Include clear comments explaining complex logic
5. Ensure all dependencies are properly imported

For each file, use markdown code blocks with the appropriate language specified.
Format file paths like this: \`\`\`filename: path/to/file.ext\`\`\`.

Never omit critical parts of the code or use placeholders like "..." or "[additional code here]".
Always produce complete, executable code that can be used immediately.`,

  model: "codellama", // Preferably use a code-specialized model
  temperature: 0.7,
  maxTokens: 4096
};

// Function to extract file information from the AI response
function extractFilesFromResponse(response: string) {
  const fileRegex = /```filename:\s*([^\n]+)\n([\s\S]*?)```/g;
  const files: Array<{path: string, content: string}> = [];

  let match;
  while ((match = fileRegex.exec(response)) !== null) {
    files.push({
      path: match[1].trim(),
      content: match[2].trim()
    });
  }

  return files;
}

// Function to generate code using the agent
async function generateCode(userRequest: string) {
  try {
    // Set up chat with the code generator agent
    const messages = [
      { role: "system", content: codeGeneratorAgent.systemPrompt },
      { role: "user", content: userRequest }
    ];

    // Get response from Ollama
    const response = await chatWithOllama({
      model: codeGeneratorAgent.model,
      messages,
      options: {
        temperature: codeGeneratorAgent.temperature,
        num_predict: codeGeneratorAgent.maxTokens
      }
    });

    // Extract files from the response
    const files = extractFilesFromResponse(response);

    // Create the files in the project
    if (files.length > 0) {
      for (const file of files) {
        await createProjectFile(file.path, file.content);
      }

      console.log(`Generated ${files.length} files based on the request`);
    } else {
      console.warn("No files extracted from the response");
    }

    return {
      response,
      files
    };
  } catch (error) {
    console.error("Error generating code:", error);
    throw error;
  }
}

// Example usage
async function handleCodeGeneration(request: string) {
  try {
    const result = await generateCode(request);

    // Display the generated files
    displayGeneratedFiles(result.files);

    // Show the explanation part of the response
    displayExplanation(result.response);
  } catch (error) {
    showErrorMessage("Failed to generate code: " + error.message);
  }
}

// Sample invocation
handleCodeGeneration("Create a React component for a shopping cart that fetches products from an API, allows adding/removing items, and calculates the total");
```

### Code Reviewer Agent

```
You are a senior code reviewer with expertise in best practices, design patterns, and secure coding.

Whenever reviewing code:
1. First identify the language and framework being used
2. Analyze the code for:
   - Code quality issues
   - Potential bugs or edge cases
   - Security vulnerabilities
   - Performance concerns
   - Adherence to best practices
3. Provide specific, actionable feedback
4. Suggest improvements with example code when appropriate

Format your feedback in sections:
- 🔍 **Code Analysis**: General assessment of the code
- ⚠️ **Issues Found**: Problems that need to be addressed
- ✅ **Positive Aspects**: What's done well
- 🛠️ **Suggested Improvements**: Code snippets showing better approaches

Always be constructive and specific in your feedback.
```

**Code Example: Implementing a Code Review Workflow**

```typescript
// Define the code reviewer agent
const codeReviewerAgent = {
  name: "Code Reviewer",
  systemPrompt: `You are a senior code reviewer with expertise in best practices, design patterns, and secure coding.

Whenever reviewing code:
1. First identify the language and framework being used
2. Analyze the code for:
   - Code quality issues
   - Potential bugs or edge cases
   - Security vulnerabilities
   - Performance concerns
   - Adherence to best practices
3. Provide specific, actionable feedback
4. Suggest improvements with example code when appropriate

Format your feedback in sections:
- 🔍 **Code Analysis**: General assessment of the code
- ⚠️ **Issues Found**: Problems that need to be addressed
- ✅ **Positive Aspects**: What's done well
- 🛠️ **Suggested Improvements**: Code snippets showing better approaches

Always be constructive and specific in your feedback.`,

  model: "codellama", // Preferably use a code-specialized model
  temperature: 0.4 // Lower temperature for more precise analysis
};

// Function to handle code review
async function reviewCode(code: string, fileName: string) {
  // Determine language from file extension
  const extension = fileName.split('.').pop() || '';
  const languageMap: Record<string, string> = {
    'js': 'JavaScript',
    'ts': 'TypeScript',
    'jsx': 'React JavaScript',
    'tsx': 'React TypeScript',
    'py': 'Python',
    // Add more as needed
  };
  const language = languageMap[extension] || 'Unknown';

  try {
    // Prepare the user message with the code to review
    const userMessage = `Please review the following ${language} code from file "${fileName}":\n\n\`\`\`${extension}\n${code}\n\`\`\``;

    // Set up chat with the code reviewer agent
    const messages = [
      { role: "system", content: codeReviewerAgent.systemPrompt },
      { role: "user", content: userMessage }
    ];

    // Get response from Ollama
    const reviewResponse = await chatWithOllama({
      model: codeReviewerAgent.model,
      messages,
      options: {
        temperature: codeReviewerAgent.temperature
      }
    });

    // Process the review to extract sections
    const sections = extractReviewSections(reviewResponse);

    return {
      fullReview: reviewResponse,
      sections
    };
  } catch (error) {
    console.error("Error during code review:", error);
    throw error;
  }
}

// Function to extract structured sections from the review
function extractReviewSections(review: string) {
  const sections = {
    analysis: extractSection(review, '🔍 **Code Analysis**'),
    issues: extractSection(review, '⚠️ **Issues Found**'),
    positiveAspects: extractSection(review, '✅ **Positive Aspects**'),
    improvements: extractSection(review, '🛠️ **Suggested Improvements**')
  };

  return sections;
}

function extractSection(text: string, sectionHeader: string): string {
  const regex = new RegExp(`${escapeRegExp(sectionHeader)}[:\\s]*(.*?)(?=(?:🔍|⚠️|✅|🛠️) \\*\\*|$)`, 's');
  const match = text.match(regex);
  return match ? match[1].trim() : '';
}

function escapeRegExp(string: string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// Example usage in a code editor context
async function handleReviewRequest() {
  const editor = getActiveEditor();
  const code = editor.getSelectedText() || editor.getText();
  const fileName = editor.getFileName();

  if (!code) {
    showMessage("No code selected for review");
    return;
  }

  showProgress("Reviewing code...");

  try {
    const review = await reviewCode(code, fileName);

    // Display the review in a structured format
    displayCodeReview(review);
  } catch (error) {
    showError("Failed to review code: " + error.message);
  } finally {
    hideProgress();
  }
}

### Architectural Design Agent

```
You are a software architecture expert who helps design scalable, maintainable systems.

When responding to architectural questions:
1. First clarify the requirements and constraints
2. Propose a suitable architecture pattern
3. Outline key components and their relationships
4. Discuss:
   - Data flow
   - API design
   - Scalability considerations
   - Security aspects

Include diagrams when useful, with ASCII or text-based representations like this:
```
[Client] → [API Gateway] → [Microservices] → [Database]
                         ↓
                  [Cache Service]
```

Present tradeoffs between different approaches and explain why a particular architecture is recommended for the specific case.
```

## Advanced Reasoning Engine

The Ollama UI Builder now includes a sophisticated reasoning engine that enhances the capabilities of AI agents through multi-stage thinking processes.

### Understanding the Reasoning Engine

The reasoning engine implements a step-by-step thinking approach that mimics human reasoning:

```typescript
export class ReasoningEngine {
  private llmModel: string = 'llama2:7b';
  private thinkingProcesses: ThinkingProcess[] = [];
  private systemPrompt = `You are an AI assistant specialized in machine learning tasks.
  You analyze user requests carefully, understand intent, and take immediate action.`;

  // Core thinking method
  public async think(userMessage: string, conversationContext?: string): Promise<ThinkingProcess> {
    // Implementation of multi-stage reasoning process
  }

  // Execute individual thinking steps
  private async executeThinkingStep(stage: ThinkingStage, userMessage: string,
    conversationContext?: string, specificPrompt?: string): Promise<ThinkingStepResult> {
    // Implementation of individual reasoning steps
  }
}
```

### Thinking Stages

The reasoning process is divided into distinct stages:

```typescript
export enum ThinkingStage {
  IntentUnderstanding = 'intent_understanding',
  TaskClassification = 'task_classification',
  ExecutionPlanning = 'execution_planning',
  ResponseFormulation = 'response_formulation',
  ErrorRecovery = 'error_recovery'
}
```

Each stage serves a specific purpose in the reasoning process:

1. **Intent Understanding**: Analyzes the user's message to identify their primary intent
2. **Task Classification**: Categorizes the request into specific task types
3. **Execution Planning**: Determines the necessary steps to fulfill the request
4. **Response Formulation**: Generates a coherent and helpful response
5. **Error Recovery**: Handles exceptions and provides graceful fallbacks

### Task Management System

The reasoning engine works with a task management system that orchestrates different types of tasks:

```typescript
export class TaskManager {
  // Process user messages through the reasoning engine
  public async processUserMessage(userMessage: string): Promise<TaskState> {
    // Create a new task
    const task: TaskState = {
      id: taskId,
      type: TaskType.Unknown,
      status: 'pending',
      reasoning: [],
      apiCalls: [],
      startedAt: new Date(),
    };

    // Use the reasoning engine to understand the request
    const thinkingProcess = await reasoningEngine.think(userMessage, conversationContext);
    task.thinkingProcess = thinkingProcess;

    // Update task type based on reasoning engine result
    task.type = thinkingProcess.taskType;

    // Execute API calls based on the task type
    await this.executeApiCalls(task, userMessage);

    // Formulate a response using the reasoning engine
    const responseResult = await reasoningEngine.formulateResponse(
      thinkingProcess,
      task.apiCalls,
      conversationContext
    );

    // Complete the task
    task.status = 'complete';
    task.completedAt = new Date();

    return task;
  }
}
```

### Creating Agents with Advanced Reasoning

You can leverage the reasoning engine to create more sophisticated agents:

```typescript
// Create an agent with reasoning capabilities
function createReasoningAgent(domain: string, expertise: string[]): Agent {
  // Create a system prompt that guides the reasoning process
  const systemPrompt = `You are an expert in ${domain} with specialized knowledge in ${expertise.join(', ')}.

  When responding to queries:
  1. First understand the user's intent
  2. Classify the task type
  3. Plan your approach
  4. Execute the necessary steps
  5. Formulate a clear, helpful response

  Always think step-by-step and explain your reasoning process.`;

  return {
    name: `${domain} Expert`,
    systemPrompt,
    model: 'llama3',
    temperature: 0.4,
    reasoningEnabled: true // Enable the reasoning engine
  };
}

// Example usage
const dataAnalysisAgent = createReasoningAgent('Data Analysis', [
  'Statistical methods',
  'Data visualization',
  'Machine learning',
  'Python data science libraries'
]);

// Process a request with the reasoning agent
async function processWithReasoning(request: string, agent: Agent) {
  // Initialize the reasoning engine with the agent's system prompt
  reasoningEngine.setSystemPrompt(agent.systemPrompt);
  reasoningEngine.setModel(agent.model);

  // Process the request through the task manager
  const task = await taskManager.processUserMessage(request);

  // Return the complete result with reasoning steps
  return {
    response: task.response,
    reasoning: task.reasoning,
    confidence: task.thinkingProcess?.confidence || 0.5
  };
}
```

### Benefits of the Reasoning Engine

The reasoning engine provides several advantages:

1. **Improved Understanding**: Better comprehension of user requests through multi-stage analysis
2. **Task-Specific Handling**: Different types of requests are processed with specialized approaches
3. **Transparent Reasoning**: The system's thinking process can be inspected and understood
4. **Error Resilience**: Graceful handling of errors and edge cases
5. **Confidence Scoring**: Assessment of the quality and reliability of responses

**Code Example: Architecture Design Session Handler**

```typescript
// Define the architecture design agent
const architectureDesignAgent = {
  name: "Architecture Designer",
  systemPrompt: `You are a software architecture expert who helps design scalable, maintainable systems.

When responding to architectural questions:
1. First clarify the requirements and constraints
2. Propose a suitable architecture pattern
3. Outline key components and their relationships
4. Discuss:
   - Data flow
   - API design
   - Scalability considerations
   - Security aspects

Include diagrams when useful, with ASCII or text-based representations.

Present tradeoffs between different approaches and explain why a particular architecture is recommended for the specific case.`,

  model: "llama3", // A general-purpose model should work well here
  temperature: 0.7,
  maxTokens: 4096
};

// Function to extract ASCII diagrams from the response
function extractDiagrams(text: string): string[] {
  const diagrams: string[] = [];
  const lines = text.split('\n');
  let inDiagram = false;
  let currentDiagram = '';

  for (const line of lines) {
    // Simple heuristic: diagrams often contain arrows or brackets
    const isDiagramLine = /[\[\]\-\>\<\|\{\}\(\)]+/.test(line);

    if (isDiagramLine) {
      if (!inDiagram) {
        inDiagram = true;
        currentDiagram = line + '\n';
      } else {
        currentDiagram += line + '\n';
      }
    } else if (inDiagram) {
      // If we were in a diagram but hit a non-diagram line,
      // the diagram is complete
      inDiagram = false;
      diagrams.push(currentDiagram);
      currentDiagram = '';
    }
  }

  // Handle case where diagram is at the end of text
  if (inDiagram && currentDiagram) {
    diagrams.push(currentDiagram);
  }

  return diagrams;
}

// Main function to get architecture design
async function getArchitectureDesign(requirements: string) {
  try {
    // Set up chat with the architecture design agent
    const messages = [
      { role: "system", content: architectureDesignAgent.systemPrompt },
      { role: "user", content: requirements }
    ];

    // Get response from Ollama
    const response = await chatWithOllama({
      model: architectureDesignAgent.model,
      messages,
      options: {
        temperature: architectureDesignAgent.temperature,
        num_predict: architectureDesignAgent.maxTokens
      }
    });

    // Extract any diagrams
    const diagrams = extractDiagrams(response);

    return {
      fullResponse: response,
      diagrams
    };
  } catch (error) {
    console.error("Error getting architecture design:", error);
    throw error;
  }
}

// Example usage in a collaborative design session
async function startArchitectureDesignSession() {
  // Get project requirements from a form or editor
  const requirementsForm = document.getElementById('requirements-form') as HTMLFormElement;
  const requirements = new FormData(requirementsForm).get('requirements') as string;

  if (!requirements) {
    showError("Please provide detailed requirements");
    return;
  }

  showProgress("Designing architecture...");

  try {
    const design = await getArchitectureDesign(requirements);

    // Display the design
    const designOutput = document.getElementById('design-output');
    if (designOutput) {
      // Create main response section
      const responseSection = document.createElement('div');
      responseSection.className = 'design-response';
      responseSection.innerHTML = formatMarkdown(design.fullResponse);
      designOutput.appendChild(responseSection);

      // Create diagrams section if diagrams exist
      if (design.diagrams.length > 0) {
        const diagramsSection = document.createElement('div');
        diagramsSection.className = 'design-diagrams';

        const heading = document.createElement('h3');
        heading.textContent = 'Architecture Diagrams';
        diagramsSection.appendChild(heading);

        design.diagrams.forEach((diagram, index) => {
          const diagramBox = document.createElement('pre');
          diagramBox.className = 'diagram';
          diagramBox.textContent = diagram;
          diagramsSection.appendChild(diagramBox);
        });

        designOutput.appendChild(diagramsSection);
      }
    }
  } catch (error) {
    showError("Failed to generate architecture design: " + error.message);
  } finally {
    hideProgress();
  }
}
```

## Advanced Techniques

### Chain-of-Thought Prompting

Guide the AI through a reasoning process:

```
You are a problem-solving assistant specializing in algorithmic challenges.

For each problem, follow this step-by-step approach:
1. Restate the problem to ensure understanding
2. Identify the key variables and constraints
3. Explore potential solutions and approaches
4. Analyze the time and space complexity of each approach
5. Select the optimal solution and explain why
6. Implement the solution with clean, commented code
7. Test the solution with example inputs

Think through each step explicitly before providing your final answer.
```

**Code Example: Chain-of-Thought Problem Solving**

```typescript
// Define the problem-solving agent with chain-of-thought prompting
const algorithmSolvingAgent = {
  name: "Algorithm Solver",
  systemPrompt: `You are a problem-solving assistant specializing in algorithmic challenges.

For each problem, follow this step-by-step approach:
1. Restate the problem to ensure understanding
2. Identify the key variables and constraints
3. Explore potential solutions and approaches
4. Analyze the time and space complexity of each approach
5. Select the optimal solution and explain why
6. Implement the solution with clean, commented code
7. Test the solution with example inputs

Think through each step explicitly before providing your final answer.`,

  model: "codellama",
  temperature: 0.3, // Lower temperature for more logical reasoning
  maxTokens: 4096
};

// Function to solve an algorithmic problem
async function solveAlgorithmProblem(problem: string) {
  try {
    // Set up chat with the algorithm solving agent
    const messages = [
      { role: "system", content: algorithmSolvingAgent.systemPrompt },
      { role: "user", content: problem }
    ];

    // Get response from Ollama
    const response = await chatWithOllama({
      model: algorithmSolvingAgent.model,
      messages,
      options: {
        temperature: algorithmSolvingAgent.temperature,
        num_predict: algorithmSolvingAgent.maxTokens
      }
    });

    // Extract the solution steps and code
    const solution = {
      fullResponse: response,
      steps: extractReasoningSteps(response),
      code: extractCodeFromResponse(response)
    };

    return solution;
  } catch (error) {
    console.error("Error solving algorithm problem:", error);
    throw error;
  }
}

// Function to extract reasoning steps from the response
function extractReasoningSteps(response: string) {
  const steps = [];

  // Look for numbered steps in the response (1., 2., etc.)
  const stepRegex = /(\d+)\.\s+(.*?)(?=\n\d+\.|$)/gs;
  let match;

  while ((match = stepRegex.exec(response)) !== null) {
    steps.push({
      number: parseInt(match[1]),
      content: match[2].trim()
    });
  }

  return steps;
}

// Example usage in a coding interview prep platform
async function handleProblemSubmission() {
  const problemInput = document.getElementById('problem-input') as HTMLTextAreaElement;
  const problem = problemInput.value.trim();

  if (!problem) {
    showError("Please enter an algorithm problem");
    return;
  }

  showProgress("Analyzing problem...");

  try {
    const solution = await solveAlgorithmProblem(problem);

    // Display the step-by-step solution
    const solutionOutput = document.getElementById('solution-output');
    if (solutionOutput) {
      // Clear previous content
      solutionOutput.innerHTML = '';

      // Create a container for reasoning steps
      const stepsContainer = document.createElement('div');
      stepsContainer.className = 'reasoning-steps';

      // Add each reasoning step
      solution.steps.forEach(step => {
        const stepElement = document.createElement('div');
        stepElement.className = 'reasoning-step';

        const stepNumber = document.createElement('div');
        stepNumber.className = 'step-number';
        stepNumber.textContent = `Step ${step.number}`;

        const stepContent = document.createElement('div');
        stepContent.className = 'step-content';
        stepContent.innerHTML = formatMarkdown(step.content);

        stepElement.appendChild(stepNumber);
        stepElement.appendChild(stepContent);
        stepsContainer.appendChild(stepElement);
      });

      solutionOutput.appendChild(stepsContainer);

      // Add the final code solution
      if (solution.code) {
        const codeContainer = document.createElement('div');
        codeContainer.className = 'solution-code';

        const codeHeading = document.createElement('h3');
        codeHeading.textContent = 'Solution Code';
        codeContainer.appendChild(codeHeading);

        const codeElement = document.createElement('pre');
        codeElement.className = 'code';
        codeElement.innerHTML = highlightCode(solution.code.content, solution.code.language);
        codeContainer.appendChild(codeElement);

        solutionOutput.appendChild(codeContainer);
      }
    }
  } catch (error) {
    showError("Failed to solve problem: " + error.message);
  } finally {
    hideProgress();
  }
}
```

### Role-Playing Techniques

Create expert personas for specific domains:

```
You are a senior security engineer with 15+ years of experience in application security and penetration testing.

When analyzing code for security vulnerabilities:
1. Identify the language and framework
2. Perform a systematic security review looking for:
   - Injection vulnerabilities
   - Authentication flaws
   - Sensitive data exposure
   - XML/JSON parsing vulnerabilities
   - Access control issues
   - Security misconfigurations
   - Cross-site scripting
   - Insecure deserialization
   - Using components with known vulnerabilities
   - Insufficient logging and monitoring

For each vulnerability found:
- Explain why it's a security concern
- Rate the severity (Low/Medium/High/Critical)
- Provide a secure code example to fix the issue
- Reference relevant OWASP guidelines or CVEs

Use your experience to prioritize issues and focus on the highest-risk vulnerabilities first.
```

**Code Example: Security Review Agent Implementation**

```typescript
// Define the security expert agent
const securityExpertAgent = {
  name: "Security Expert",
  systemPrompt: `You are a senior security engineer with 15+ years of experience in application security and penetration testing.

When analyzing code for security vulnerabilities:
1. Identify the language and framework
2. Perform a systematic security review looking for:
   - Injection vulnerabilities
   - Authentication flaws
   - Sensitive data exposure
   - XML/JSON parsing vulnerabilities
   - Access control issues
   - Security misconfigurations
   - Cross-site scripting
   - Insecure deserialization
   - Using components with known vulnerabilities
   - Insufficient logging and monitoring

For each vulnerability found:
- Explain why it's a security concern
- Rate the severity (Low/Medium/High/Critical)
- Provide a secure code example to fix the issue
- Reference relevant OWASP guidelines or CVEs

Use your experience to prioritize issues and focus on the highest-risk vulnerabilities first.`,

  model: "llama3",
  temperature: 0.3,
  maxTokens: 4096
};

// Interface for security vulnerabilities
interface SecurityVulnerability {
  description: string;
  severity: 'Low' | 'Medium' | 'High' | 'Critical';
  fix: string;
  references?: string[];
}

// Function to perform a security review
async function performSecurityReview(code: string, fileName: string) {
  // Determine language from file extension
  const extension = fileName.split('.').pop() || '';

  try {
    // Prepare the user message with the code to review
    const userMessage = `Please perform a security review on this ${extension.toUpperCase()} code:

\`\`\`${extension}
${code}
\`\`\``;

    // Set up chat with the security expert agent
    const messages = [
      { role: "system", content: securityExpertAgent.systemPrompt },
      { role: "user", content: userMessage }
    ];

    // Get response from Ollama
    const response = await chatWithOllama({
      model: securityExpertAgent.model,
      messages,
      options: {
        temperature: securityExpertAgent.temperature,
        num_predict: securityExpertAgent.maxTokens
      }
    });

    // Extract vulnerabilities from the response
    const vulnerabilities = extractVulnerabilities(response);

    return {
      fullResponse: response,
      vulnerabilities,
      overallRisk: calculateOverallRisk(vulnerabilities)
    };
  } catch (error) {
    console.error("Error during security review:", error);
    throw error;
  }
}

// Function to extract vulnerabilities from the response
function extractVulnerabilities(response: string): SecurityVulnerability[] {
  const vulnerabilities: SecurityVulnerability[] = [];

  // Look for vulnerability descriptions using regex patterns
  // This is a simplified approach; in practice, more sophisticated parsing may be needed
  const severityLevels = ['Critical', 'High', 'Medium', 'Low'];

  for (const severity of severityLevels) {
    const regex = new RegExp(`(.*?)\\s*Severity:\\s*${severity}\\s*[\\n\\r]+(.*?)(?=(?:Severity:|$))`, 'gis');
    let match;

    while ((match = regex.exec(response)) !== null) {
      const description = match[1].trim();
      const details = match[2].trim();

      // Extract fix code
      const fixCodeMatch = details.match(/```(?:\w+)?\s*([\s\S]*?)```/);
      const fix = fixCodeMatch ? fixCodeMatch[1].trim() : 'No specific fix provided';

      // Extract references
      const referencesMatch = details.match(/References?:?\s*((?:OWASP|CVE|CWE)[\s\S]*?)(?=\n\n|$)/i);
      const references = referencesMatch
        ? referencesMatch[1].split('\n').map(r => r.trim()).filter(r => r)
        : [];

      vulnerabilities.push({
        description,
        severity: severity as 'Low' | 'Medium' | 'High' | 'Critical',
        fix,
        references
      });
    }
  }

  return vulnerabilities;
}

// Function to calculate overall risk based on vulnerabilities
function calculateOverallRisk(vulnerabilities: SecurityVulnerability[]): string {
  if (vulnerabilities.some(v => v.severity === 'Critical')) {
    return 'Critical';
  } else if (vulnerabilities.some(v => v.severity === 'High')) {
    return 'High';
  } else if (vulnerabilities.some(v => v.severity === 'Medium')) {
    return 'Medium';
  } else if (vulnerabilities.some(v => v.severity === 'Low')) {
    return 'Low';
  } else {
    return 'No significant vulnerabilities found';
  }
}

// Example usage in a security scanning workflow
async function scanFileForSecurityIssues(filePath: string) {
  const fileName = getFileNameFromPath(filePath);
  const code = await readFile(filePath);

  showProgress(`Scanning ${fileName} for security vulnerabilities...`);

  try {
    const securityReport = await performSecurityReview(code, fileName);

    // Generate a security report
    const report = {
      fileName,
      scannedAt: new Date().toISOString(),
      overallRisk: securityReport.overallRisk,
      vulnerabilities: securityReport.vulnerabilities,
      rawOutput: securityReport.fullResponse
    };

    // Save the report
    await saveSecurityReport(fileName, report);

    // Display the report in the UI
    displaySecurityReport(report);

    // If critical issues were found, trigger alerts
    if (securityReport.overallRisk === 'Critical') {
      triggerSecurityAlert(fileName, securityReport.vulnerabilities);
    }

    return report;
  } catch (error) {
    console.error(`Security scan failed for ${fileName}:`, error);
    showError(`Failed to scan ${fileName}: ${error.message}`);
    return null;
  } finally {
    hideProgress();
  }
}
```

## Use Cases and Examples

### Full-Stack Web Application Developer

```
You are a full-stack web application developer expert specializing in the MERN stack (MongoDB, Express, React, Node.js).

When helping users build web applications:
1. Understand the requirements and user stories
2. Design both frontend and backend architectures
3. Create complete, working code for both client and server
4. Implement proper authentication and authorization
5. Follow best practices for security and performance

Structure your responses with clear separation between:
- Backend code (Node.js/Express)
- Frontend code (React)
- Database schemas (MongoDB)
- API specifications (RESTful or GraphQL)

Always include:
- Complete package.json dependencies
- Configuration files
- Environment variable examples

Format all code in syntax-highlighted code blocks with appropriate language tags.
```

### DevOps and Infrastructure Specialist

```
You are a DevOps and infrastructure specialist experienced with cloud platforms, containerization, and CI/CD pipelines.

When responding to DevOps questions:
1. Understand the infrastructure requirements
2. Recommend appropriate cloud services and tools
3. Provide infrastructure-as-code solutions
4. Design CI/CD pipelines
5. Address security, scaling, and monitoring concerns

Your expertise includes:
- Docker and Kubernetes
- AWS, Azure, and GCP services
- Terraform and CloudFormation
- GitHub Actions, Jenkins, and ArgoCD
- Prometheus, Grafana, and ELK stack

Provide complete, working examples of:
- Dockerfile and docker-compose configurations
- Kubernetes manifests
- Infrastructure as code (Terraform/CloudFormation)
- CI/CD pipeline configurations

Always consider security best practices, cost optimization, and operational excellence in your recommendations.
```

### Data Science and ML Engineer

```
You are a data science and machine learning engineer specialized in building end-to-end ML solutions.

Your expertise includes:
- Data cleaning and preprocessing
- Feature engineering and selection
- Model selection and training
- Hyperparameter tuning
- Model evaluation and interpretation
- Deployment and monitoring

When helping with ML tasks:
1. Understand the problem and data characteristics
2. Recommend appropriate techniques and algorithms
3. Provide complete code implementations
4. Explain the reasoning behind your choices
5. Address potential issues like overfitting or bias

Use Python with relevant libraries (pandas, scikit-learn, TensorFlow, PyTorch, etc.).
Include visualizations when applicable.

Format your code with proper markdown code blocks and include detailed comments.
```

## Best Practices

### Clarity and Specificity

- Be clear about the exact role and capabilities you want the AI to embody
- Use specific rather than general instructions
- Prioritize instructions by importance

✅ Good:
```
You are a React performance optimization expert. Focus specifically on minimizing render cycles, optimizing state management, and reducing bundle size.
```

❌ Not as effective:
```
You are a frontend expert. Help with performance.
```

**Code Example: Comparing Different Levels of Specificity**

```typescript
// Comparing vague vs. specific system prompts

// Vague prompt - less effective
const vagueSystemPrompt = `You are a frontend expert. Help with performance.`;

// Specific prompt - more effective
const specificSystemPrompt = `You are a React performance optimization expert. Focus specifically on minimizing render cycles, optimizing state management, and reducing bundle size.

When analyzing React components:
1. Identify unnecessary re-renders using React.memo, useMemo, and useCallback
2. Optimize state management by:
   - Using appropriate state location (local vs. global)
   - Implementing proper state update patterns
   - Avoiding state derived from props without memoization
3. Reduce bundle size through:
   - Code splitting with React.lazy and Suspense
   - Tree shaking unused dependencies
   - Efficient asset loading strategies

Always include concrete code examples showing before and after optimizations.
Explain the performance impact of each suggested change.`;

// Function to demonstrate prompt effectiveness
async function comparePromptEffectiveness(userQuery: string) {
  const queries = [
    {
      name: "Vague Prompt",
      systemPrompt: vagueSystemPrompt
    },
    {
      name: "Specific Prompt",
      systemPrompt: specificSystemPrompt
    }
  ];

  const results = [];

  for (const query of queries) {
    const messages = [
      { role: "system", content: query.systemPrompt },
      { role: "user", content: userQuery }
    ];

    try {
      const response = await chatWithOllama({
        model: "llama3",
        messages,
        options: {
          temperature: 0.7
        }
      });

      results.push({
        promptType: query.name,
        response
      });
    } catch (error) {
      console.error(`Error with ${query.name}:`, error);
    }
  }

  return results;
}

// Example usage
async function demoPromptSpecificity() {
  const userQuery = "My React app is slow. The product listing page takes too long to render when filtering products.";

  showProgress("Comparing system prompts...");

  try {
    const results = await comparePromptEffectiveness(userQuery);

    // Display the comparison
    const comparisonOutput = document.getElementById('comparison-output');
    if (comparisonOutput) {
      comparisonOutput.innerHTML = '';

      for (const result of results) {
        const section = document.createElement('div');
        section.className = 'comparison-section';

        const heading = document.createElement('h3');
        heading.textContent = result.promptType;
        section.appendChild(heading);

        const responseContent = document.createElement('div');
        responseContent.className = 'response-content';
        responseContent.innerHTML = formatMarkdown(result.response);
        section.appendChild(responseContent);

        comparisonOutput.appendChild(section);
      }
    }
  } catch (error) {
    showError("Comparison failed: " + error.message);
  } finally {
    hideProgress();
  }
}
```

### Constrain and Guide

- Specify constraints to prevent unwanted behaviors
- Provide clear guidelines for the format and structure of responses
- Include step-by-step procedures for complex tasks

✅ Good:
```
When analyzing code performance:
1. First identify the language and runtime environment
2. Look for obvious inefficiencies (O(n²) algorithms, redundant operations)
3. Suggest specific optimization techniques relevant to the language
4. Provide before/after code examples
5. Explain the performance impact of each change

Never suggest premature optimizations without explaining the measurable benefit.
```

**Code Example: Structured Response Format Implementation**

```typescript
// Define a system prompt with strict output structure constraints
const structuredPerformanceAnalysisPrompt = `You are a code performance optimization expert.

When analyzing code performance:
1. First identify the language and runtime environment
2. Look for obvious inefficiencies (O(n²) algorithms, redundant operations)
3. Suggest specific optimization techniques relevant to the language
4. Provide before/after code examples
5. Explain the performance impact of each change

Never suggest premature optimizations without explaining the measurable benefit.

Format your response in JSON as follows:
\`\`\`json
{
  "language": "The programming language of the code",
  "environment": "The runtime environment",
  "inefficiencies": [
    {
      "description": "Description of the inefficiency",
      "location": "Where in the code it occurs",
      "impact": "Performance impact assessment"
    }
  ],
  "optimizations": [
    {
      "description": "Description of the optimization",
      "originalCode": "The code before optimization",
      "optimizedCode": "The code after optimization",
      "expectedImprovement": "Estimated performance improvement",
      "reasoning": "Why this optimization helps"
    }
  ],
  "summary": "Overall assessment and recommendations"
}
\`\`\`

This structured format is essential for the response to be correctly processed.`;

// Function to analyze code performance with a structured response
async function analyzeCodePerformance(code: string) {
  try {
    const messages = [
      { role: "system", content: structuredPerformanceAnalysisPrompt },
      { role: "user", content: `Please analyze the performance of this code:\n\n\`\`\`\n${code}\n\`\`\`` }
    ];

    const response = await chatWithOllama({
      model: "codellama",
      messages,
      options: {
        temperature: 0.3
      }
    });

    // Extract the JSON from the response
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);

    if (!jsonMatch) {
      throw new Error("Failed to get structured response");
    }

    try {
      const analysisData = JSON.parse(jsonMatch[1]);
      return analysisData;
    } catch (e) {
      console.error("Failed to parse JSON:", e);
      throw new Error("Invalid JSON in response");
    }
  } catch (error) {
    console.error("Error analyzing code performance:", error);
    throw error;
  }
}

// Example usage in a code editor plugin
async function runPerformanceAnalysis() {
  const editor = getActiveEditor();
  const code = editor.getSelectedText() || editor.getText();

  if (!code) {
    showMessage("No code selected for analysis");
    return;
  }

  showProgress("Analyzing code performance...");

  try {
    const analysis = await analyzeCodePerformance(code);

    // Display the analysis in a structured UI
    displayStructuredAnalysis(analysis);

    // Optionally create performance report
    if (analysis.inefficiencies.length > 0) {
      createPerformanceReport(analysis);
    }
  } catch (error) {
    showError("Performance analysis failed: " + error.message);
  } finally {
    hideProgress();
  }
}

// Function to display structured analysis
function displayStructuredAnalysis(analysis) {
  const container = document.getElementById('analysis-results');
  if (!container) return;

  // Clear previous results
  container.innerHTML = '';

  // Create language and environment section
  const envSection = document.createElement('div');
  envSection.className = 'env-section';
  envSection.innerHTML = `<h3>Code Environment</h3>
    <div><strong>Language:</strong> ${analysis.language}</div>
    <div><strong>Environment:</strong> ${analysis.environment}</div>`;
  container.appendChild(envSection);

  // Create inefficiencies section
  if (analysis.inefficiencies.length > 0) {
    const ineffSection = document.createElement('div');
    ineffSection.className = 'inefficiencies-section';
    ineffSection.innerHTML = `<h3>Identified Inefficiencies</h3>`;

    const ineffList = document.createElement('ul');
    analysis.inefficiencies.forEach(item => {
      const listItem = document.createElement('li');
      listItem.innerHTML = `
        <div><strong>${item.description}</strong></div>
        <div><em>Location:</em> ${item.location}</div>
        <div><em>Impact:</em> ${item.impact}</div>
      `;
      ineffList.appendChild(listItem);
    });

    ineffSection.appendChild(ineffList);
    container.appendChild(ineffSection);
  }

  // Create optimizations section
  if (analysis.optimizations.length > 0) {
    const optSection = document.createElement('div');
    optSection.className = 'optimizations-section';
    optSection.innerHTML = `<h3>Recommended Optimizations</h3>`;

    analysis.optimizations.forEach((opt, index) => {
      const optItem = document.createElement('div');
      optItem.className = 'optimization-item';
      optItem.innerHTML = `
        <h4>Optimization ${index + 1}: ${opt.description}</h4>
        <div><strong>Expected Improvement:</strong> ${opt.expectedImprovement}</div>
        <div><strong>Reasoning:</strong> ${opt.reasoning}</div>

        <div class="code-comparison">
          <div class="original-code">
            <h5>Original Code</h5>
            <pre><code>${escapeHtml(opt.originalCode)}</code></pre>
          </div>
          <div class="optimized-code">
            <h5>Optimized Code</h5>
            <pre><code>${escapeHtml(opt.optimizedCode)}</code></pre>
          </div>
        </div>
        <button class="apply-optimization" data-index="${index}">Apply This Optimization</button>
      `;
      optSection.appendChild(optItem);
    });

    container.appendChild(optSection);

    // Add summary
    const summarySection = document.createElement('div');
    summarySection.className = 'summary-section';
    summarySection.innerHTML = `<h3>Summary</h3><p>${analysis.summary}</p>`;
    container.appendChild(summarySection);

    // Add event listeners for the "Apply" buttons
    document.querySelectorAll('.apply-optimization').forEach(button => {
      button.addEventListener('click', (e) => {
        const index = parseInt((e.target as HTMLElement).getAttribute('data-index') || '0');
        applyOptimization(analysis.optimizations[index]);
      });
    });
  }
}
```

### Combine Multiple Techniques

Layer different prompting techniques for more powerful results:

- Start with a clear role definition
- Add specific expertise areas
- Include procedural guidelines
- Specify response formatting
- Add examples for complex patterns
- Include reflection steps

**Code Example: Multi-Technique System Prompt Generator**

```typescript
// Define components of advanced system prompts
interface PromptComponent {
  type: string;
  content: string;
}

// Function to build a multi-technique system prompt
function buildAdvancedSystemPrompt(components: PromptComponent[]): string {
  return components.map(c => c.content).join('\n\n');
}

// Predefined prompt components
const promptComponents = {
  roleDefinition: (role: string): PromptComponent => ({
    type: 'role',
    content: `You are ${role}.`
  }),

  expertiseAreas: (areas: string[]): PromptComponent => ({
    type: 'expertise',
    content: `Your areas of expertise include:\n${areas.map(area => `- ${area}`).join('\n')}`
  }),

  procedureSteps: (procedure: string[]): PromptComponent => ({
    type: 'procedure',
    content: `Follow these steps when responding:\n${procedure.map((step, i) => `${i+1}. ${step}`).join('\n')}`
  }),

  formatGuidelines: (format: string): PromptComponent => ({
    type: 'format',
    content: `Format your response as follows:\n${format}`
  }),

  constraints: (do_constraints: string[], dont_constraints: string[]): PromptComponent => ({
    type: 'constraints',
    content: `Always:\n${do_constraints.map(c => `- ${c}`).join('\n')}\n\nNever:\n${dont_constraints.map(c => `- ${c}`).join('\n')}`
  }),

  examples: (examples: {scenario: string, response: string}[]): PromptComponent => ({
    type: 'examples',
    content: `Here are examples of how to respond:\n\n${examples.map(ex =>
      `Example scenario: "${ex.scenario}"\n\nExample response:\n${ex.response}`).join('\n\n')}`
  }),

  selfReflection: (): PromptComponent => ({
    type: 'reflection',
    content: `Before providing your final answer, review your response to ensure it is:
- Technically accurate and complete
- Well-structured and easy to understand
- Directly addressing the user's query
- Following all the guidelines above`
  })
};

// Example usage: Creating a comprehensive data analysis agent
function createDataAnalysisAgent() {
  const prompt = buildAdvancedSystemPrompt([
    promptComponents.roleDefinition("a data analysis expert specializing in Python data science"),

    promptComponents.expertiseAreas([
      "Data cleaning and preprocessing",
      "Exploratory data analysis",
      "Statistical analysis and hypothesis testing",
      "Data visualization",
      "Machine learning model selection and evaluation",
      "Time series analysis"
    ]),

    promptComponents.procedureSteps([
      "Understand the data analysis goal and available data",
      "Recommend appropriate analysis techniques",
      "Provide complete Python code with pandas, numpy, scikit-learn, or other relevant libraries",
      "Include data visualization code where applicable",
      "Explain the statistical reasoning behind your approach",
      "Interpret the results in plain language"
    ]),

    promptComponents.formatGuidelines(`1. Start with a brief overview of the approach
2. Provide complete Python code snippets in markdown code blocks
3. Include step-by-step explanations between code blocks
4. End with conclusions and next steps`),

    promptComponents.constraints(
      [
        "Use pandas, numpy, and scikit-learn best practices",
        "Include data validation steps",
        "Consider potential issues like missing data or outliers",
        "Cite statistical methods where relevant"
      ],
      [
        "Provide code that's unnecessarily complex",
        "Skip data validation and error handling",
        "Make unfounded statistical claims",
        "Use outdated libraries or methods"
      ]
    ),

    promptComponents.examples([
      {
        scenario: "I have a CSV file with sales data and I want to find seasonal patterns",
        response: `# Time Series Analysis for Seasonal Patterns

First, let's load and explore the sales data:

\`\`\`python
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.seasonal import seasonal_decompose

# Load data
sales_df = pd.read_csv('sales_data.csv', parse_dates=['date'])
sales_df = sales_df.set_index('date')

# Inspect the data
print(sales_df.head())
print(sales_df.info())
\`\`\`

Let's check for missing values and visualize the time series:

\`\`\`python
# Check for missing values
print(sales_df.isna().sum())

# Plot the time series
plt.figure(figsize=(12, 6))
plt.plot(sales_df.index, sales_df['sales'])
plt.title('Sales Over Time')
plt.xlabel('Date')
plt.ylabel('Sales')
plt.grid(True)
plt.show()
\`\`\`

Now let's perform a seasonal decomposition to identify patterns:

\`\`\`python
# Perform seasonal decomposition
result = seasonal_decompose(sales_df['sales'], model='multiplicative', period=12)

# Plot the decomposition
fig, (ax1, ax2, ax3, ax4) = plt.subplots(4, 1, figsize=(12, 12))
result.observed.plot(ax=ax1)
ax1.set_title('Observed')
result.trend.plot(ax=ax2)
ax2.set_title('Trend')
result.seasonal.plot(ax=ax3)
ax3.set_title('Seasonal')
result.resid.plot(ax=ax4)
ax4.set_title('Residual')
plt.tight_layout()
plt.show()
\`\`\`

## Conclusions

Based on the seasonal decomposition:
1. We can observe clear seasonal patterns with peaks in [specific months]
2. The overall trend shows [increasing/decreasing] sales over time
3. Next steps would include forecasting future sales using models like SARIMA or Prophet`
      }
    ]),

    promptComponents.selfReflection()
  ]);

  return {
    name: "Data Analysis Expert",
    systemPrompt: prompt,
    model: "llama3",
    temperature: 0.4
  };
}

// Implementation example
async function handleDataAnalysisRequest(dataDescription: string, analysisGoal: string) {
  const agent = createDataAnalysisAgent();

  const userQuery = `I have the following data: ${dataDescription}

I want to: ${analysisGoal}

Please help me with Python code to perform this analysis.`;

  try {
    const messages = [
      { role: "system", content: agent.systemPrompt },
      { role: "user", content: userQuery }
    ];

    const response = await chatWithOllama({
      model: agent.model,
      messages,
      options: {
        temperature: agent.temperature
      }
    });

    return response;
  } catch (error) {
    console.error("Error handling data analysis request:", error);
    throw error;
  }
}
```

### Self-Reflection System Prompts

Prompts that encourage the AI to evaluate its own responses:

```
You are a precision-focused coding assistant that double-checks your work.

After generating code or providing technical information, always:
1. Review your response for technical accuracy
2. Verify that all code is complete and will run without errors
3. Check for logical inconsistencies or gaps in explanation
4. Ensure you've fully addressed the user's question

If you find any issues, correct them immediately with a notation:
"[Self-correction: I noticed an issue with my previous response...]"

Prioritize accuracy over speed, and be transparent about any limitations or uncertainties in your knowledge.
```

**Code Example: Implementing Self-Reflection in Responses**

```typescript
// Define a self-reflecting agent
const selfReflectingAgent = {
  name: "Self-Reflecting Assistant",
  systemPrompt: `You are a precision-focused coding assistant that double-checks your work.

After generating code or providing technical information, always:
1. Review your response for technical accuracy
2. Verify that all code is complete and will run without errors
3. Check for logical inconsistencies or gaps in explanation
4. Ensure you've fully addressed the user's question

If you find any issues, correct them immediately with a notation:
"[Self-correction: I noticed an issue with my previous response...]"

Prioritize accuracy over speed, and be transparent about any limitations or uncertainties in your knowledge.`,

  model: "llama3",
  temperature: 0.4,
  maxTokens: 4096
};

// Two-stage response processing with self-reflection
async function generateResponseWithReflection(userQuery: string) {
  try {
    // First stage: Generate initial response
    const messages = [
      { role: "system", content: selfReflectingAgent.systemPrompt },
      { role: "user", content: userQuery }
    ];

    const initialResponse = await chatWithOllama({
      model: selfReflectingAgent.model,
      messages,
      options: {
        temperature: selfReflectingAgent.temperature,
        num_predict: selfReflectingAgent.maxTokens
      }
    });

    // Second stage: Ask for self-reflection and correction
    const reflectionRequest = `You provided this response to the user's query:

"""
${initialResponse}
"""

Now, carefully review this response for:
1. Technical accuracy
2. Completeness of code
3. Logical consistency
4. Whether it fully addresses the user's query

If you find any issues, provide a corrected version.`;

    const reflectionMessages = [
      { role: "system", content: selfReflectingAgent.systemPrompt },
      { role: "user", content: userQuery },
      { role: "assistant", content: initialResponse },
      { role: "user", content: reflectionRequest }
    ];

    const reflectedResponse = await chatWithOllama({
      model: selfReflectingAgent.model,
      messages: reflectionMessages,
      options: {
        temperature: 0.3, // Lower temperature for more critical analysis
        num_predict: selfReflectingAgent.maxTokens
      }
    });

    // Check if the reflection identified issues
    if (reflectedResponse.includes("[Self-correction:") ||
        reflectedResponse.includes("corrected version") ||
        reflectedResponse.includes("issue with my previous response")) {
      // Return the corrected response
      return {
        finalResponse: reflectedResponse,
        hadCorrections: true,
        originalResponse: initialResponse
      };
    } else {
      // No corrections needed, return the original response
      return {
        finalResponse: initialResponse,
        hadCorrections: false,
        originalResponse: initialResponse
      };
    }
  } catch (error) {
    console.error("Error generating response with reflection:", error);
    throw error;
  }
}

// Example usage in a technical help system
async function handleTechnicalQuery() {
  const queryInput = document.getElementById('query-input') as HTMLTextAreaElement;
  const query = queryInput.value.trim();

  if (!query) {
    showMessage("Please enter a query");
    return;
  }

  showProgress("Generating response...");

  try {
    const response = await generateResponseWithReflection(query);

    // Display the response
    const responseOutput = document.getElementById('response-output');
    if (responseOutput) {
      responseOutput.innerHTML = formatMarkdown(response.finalResponse);

      // If there were corrections, add an info note
      if (response.hadCorrections) {
        const noteElement = document.createElement('div');
        noteElement.className = 'info-note';
        noteElement.innerHTML = '<i>Note: This response includes self-corrections from the AI assistant to ensure accuracy.</i>';
        responseOutput.prepend(noteElement);
      }
    }
  } catch (error) {
    showError("Failed to generate response: " + error.message);
  } finally {
    hideProgress();
  }
}
```

By mastering system prompting in the Ollama UI, you can create powerful, customized AI assistants that can significantly enhance your productivity across a wide range of tasks and domains.